import React, { useState, useRef, useEffect } from "react";
import {
  Dimensions,
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
  ScrollView,
} from "react-native";
import { colors } from "../../Config/colors";
import Div from "../../Components/Div";
import H4 from "../../Components/H4";
import P from "../../Components/P";
import { fonts } from "../../Config/Fonts";
import Button from "../../Components/Button";

const { width, height } = Dimensions.get("window");
const MAX_WIDTH = 500;
export default function OnboardingScreen({ navigation, route }) {
  const [currentPage, setCurrentPage] = useState(0);
  const scrollViewRef = useRef(null);
  const { isPractional } = route.params || false;

  const onboardingData = [
    {
      image: require("../../assets/appMock1.png"),
      title: "Find Care Effortlessly",
      subtitle: "Connect with trusted practitioners and clinics near you.",
    },
    {
      image: require("../../assets/appMock2.png"),
      title: "Manage Appointments",
      subtitle: "Schedule, track, and update your visits with ease.",
    },
    {
      image: require("../../assets/appMock1.png"),
      title: "Access Health Records",
      subtitle: "Keep all your medical history in one secure place.",
    },
  ];

  const handleScroll = (event) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const currentIndex = Math.round(contentOffsetX / width);
    setCurrentPage(currentIndex);
  };
  return (
    <View style={styles.mainContainer}>
      <Div>
        <View style={styles.container}>
          {/* Carousel */}
          <ScrollView
            ref={scrollViewRef}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            onScroll={handleScroll}
            scrollEventThrottle={16}
            style={styles.carouselContainer}
          >
            {onboardingData.map((item, index) => (
              <View key={index} style={styles.slide}>
                {/* Phone mockup image */}
                <View style={styles.imageContainer}>
                  <Image
                    source={item.image}
                    style={styles.mockupImage}
                    resizeMode="contain"
                  />
                </View>
                {/* Content section */}
                <View style={styles.contentContainer}>
                  <H4 style={styles.mainTitle}>{item.title}</H4>
                  <P style={styles.subtitle}>{item.subtitle}</P>
                </View>
              </View>
            ))}
          </ScrollView>

          {/* Buttons */}
          <View style={styles.buttonsContainer}>
            {/* Pagination indicators */}
            <View style={styles.paginationContainer}>
              {onboardingData.map((_, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.paginationDot,
                    currentPage === index && styles.activeDot,
                  ]}
                  onPress={() => {
                    scrollViewRef.current?.scrollTo({
                      x: index * width,
                      animated: true,
                    });
                  }}
                />
              ))}
            </View>
            <Button
              btnText="Login"
              onPress={() =>
                navigation.navigate("IntroScreen", { path: "LoginScreen" })
              }
              style={styles.loginButton}
            />
            <Button
              btnText="Create Account"
              type="alt"
              onPress={() =>
                navigation.navigate("IntroScreen", { path: "SignUpScreen" })
              }
              style={styles.loginButton}
            />
          </View>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "90%",
    height: "100%",
    backgroundColor: colors.white,
    alignItems: "center",
  },
  carouselContainer: {
    flex: 1,
    width: Math.min(width, MAX_WIDTH),
  },
  slide: {
    width: Math.min(width, MAX_WIDTH),
    alignItems: "center",
    paddingHorizontal: width * 0.05,
  },
  imageContainer: {
    alignItems: "center",
    // backgroundColor: "red",
    justifyContent: "center",
    marginTop: (5 * height) / 100,
    width: "100%",
    height: (65 * height) / 100,
  },
  mockupImage: {
    width: "100%",
    height: "100%",
  },
  contentContainer: {
    width: "90%",
    position: "absolute",
    bottom: (21 * height) / 100,
    alignItems: "center",
    marginTop: (5 * height) / 100,
  },
  mainTitle: {
    fontFamily: fonts.dmSansBold,
    fontSize: 24,
    textAlign: "center",
    marginBottom: 10,
  },
  subtitle: {
    textAlign: "center",
    marginBottom: 20,
    fontSize: 14,
    color: "#666",
  },
  paginationContainer: {
    flexDirection: "row",
    justifyContent: "center",
    top: (-17 * height) / 100,
  },
  paginationDot: {
    width: 34,
    height: 4,
    borderRadius: 14,
    backgroundColor: "#D3D3D3",
    marginHorizontal: 8,
  },
  activeDot: {
    backgroundColor: colors.primary,
  },
  buttonsContainer: {
    width: "100%",
    marginTop: (3 * height) / 100,
    position: "absolute",
    zIndex: 10,
    bottom: (3 * height) / 100,
  },
  loginButton: {
    marginBottom: 10,
  },
  createAccountButton: {
    borderWidth: 1,
    borderColor: colors.primary,
    backgroundColor: "transparent",
    borderRadius: 50,
    padding: 15,
    alignItems: "center",
  },
  createAccountText: {
    color: colors.primary,
    fontFamily: fonts.dmSansBold,
    fontSize: 14,
  },
});
