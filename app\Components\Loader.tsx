import { ActivityIndicator, Modal, StyleSheet, Text, View } from "react-native";
import React from "react";
import { colors } from "../Config/colors";

export default function Loader() {
  return (
    <Modal
      visible={true}
      statusBarTranslucent
      style={{ width: "100%", flex: 1, position: "absolute" }}
    >
      <View style={styles.container}>
        <ActivityIndicator size={"large"} color={colors.primary} />
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    height: "100%",
    backgroundColor: colors.white,
    alignItems: "center",
    justifyContent: "center",
  },
});
