import AsyncStorage from "@react-native-async-storage/async-storage";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./Request";

const request = new RequestHandler();

// Function to get auth token from AsyncStorage
export async function getAuthToken(): Promise<string> {
  try {
    const cookiesString = await AsyncStorage.getItem("cookies##$$");
    console.log(cookiesString);

    if (cookiesString) {
      const cookies = JSON.parse(cookiesString);
      if (
        (cookies && cookies.data && cookies.data.token) ||
        (cookies && cookies.token)
      ) {
        return cookies.data ? cookies.data.token : cookies.token;
      }
    }
    return "";
  } catch (error) {
    console.error("Error getting auth token:", error);
    return "";
  }
}

export function Login(body: object): Promise<any> {
  return request.post("user/login", body, "");
}

export function CreateUser(body: object): Promise<any> {
  return request.post("user/register", body, "");
}

export function SendOtp(body: object): Promise<any> {
  return request.post("user/send-otp", body, "");
}

export function VerifyOtp(body: object): Promise<any> {
  return request.post("user/verify-otp", body, "");
}

export function ValidateGoogleToken(body: object): Promise<any> {
  return request.post("user/google/auth", body, "");
}

export function ValidateAppleToken(body: object): Promise<any> {
  return request.post("user/apple/auth", body, "");
}

export function ResetPassword(body: object): Promise<any> {
  return request.post("user/reset-password", body, "");
}
/**
 * Update user profile with avatar
 * @param body - User profile data including avatar URL
 * @returns Promise with response data
 */
export async function UpdateProfileWithAvatar(body: object): Promise<any> {
  const token = await getAuthToken();
  return request.put("user/profile", body, token);
}

export async function LogoutUser(): Promise<any> {
  const token = await getAuthToken();
  return request.post("user/logout", {}, token);
}

/**
 * Get drug allergies list from the API
 * @returns Promise with drug allergies data
 */
export function GetDrugAllergies(): Promise<any> {
  return request.get("common/drug-allergies", "");
}

/**
 * Get chronic illnesses list from the API
 * @returns Promise with chronic illnesses data
 */
export function GetChronicIllnesses(): Promise<any> {
  return request.get("common/chronic-illnesses", "");
}

/**
 * Get cognitive conditions list from the API
 * @returns Promise with cognitive conditions data
 */
export function GetCognitiveConditions(): Promise<any> {
  return request.get("common/cognitive-conditions", "");
}

/**
 * Get physical disabilities list from the API
 * @returns Promise with physical disabilities data
 */
export function GetPhysicalDisabilities(): Promise<any> {
  return request.get("common/physical-disabilities", "");
}

/**
 * Get drug reactions list from the API
 * @returns Promise with drug reactions data
 */
export function GetDrugReactions(): Promise<any> {
  return request.get("common/drug-reactions", "");
}

/**
 * Get genetic conditions list from the API
 * @returns Promise with genetic conditions data
 */
export function GetGeneticConditions(): Promise<any> {
  return request.get("common/genetic-conditions", "");
}

/**
 * Get vaccines list from the API
 * @returns Promise with vaccines data
 */
export function GetVaccines(): Promise<any> {
  return request.get("common/vaccines", "");
}

/**
 * Get medications list from the API
 * @returns Promise with medications data
 */
export function GetMedications(): Promise<any> {
  return request.get("common/medications", "");
}

/**
 * Get habits (smoking/alcohol) list from the API
 * @returns Promise with habits data
 */
export function GetHabits(): Promise<any> {
  return request.get("common/habits", "");
}

/**
 * Get diet restrictions list from the API
 * @returns Promise with diet restrictions data
 */
export function GetDietRestrictions(): Promise<any> {
  return request.get("common/diet-restrictions", "");
}
/**
 * Get food preferences list from the API
 * @returns Promise with food preferences data
 */
export function GetFoodPreferences(): Promise<any> {
  return request.get("common/food-preferences", "");
}
export function GetHealthcareSpecializations(): Promise<any> {
  return request.get("common/healthcare-specializations");
}
export function GetHealthcareEducation(): Promise<any> {
  return request.get("common/healthcare-educations");
}
export function GetLanguage(): Promise<any> {
  return request.get("common/language");
}
