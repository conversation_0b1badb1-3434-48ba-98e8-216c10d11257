import React, { useState, useEffect } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
  ScrollView,
  Image,
  Platform,
  ActivityIndicator,
  Alert,
} from "react-native";
import * as DocumentPicker from "expo-document-picker";
import { colors } from "../../../Config/colors";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../Config/Svg";
import Input from "../../../Components/Input";
import SelectInput from "../../../Components/SelectInput";
import * as yup from "yup";
import { Formik } from "formik";
import { useToast } from "../../../Context/ToastContext";
import NetworkErrorView from "../../../Components/NetworkErrorView";
import {
  getErrorMessage,
  getErrorType,
} from "../../../utils/networkErrorHandler";
import { GetHealthcareSpecializations } from "../../../RequestHandler.tsx/Auth";
import { UpdateProssionalDetails, GetProssionalDetails } from "../../../RequestHandler.tsx/User";
import { uploadDocumentToCloudinary } from "../../../Services/CloudinaryService";

const { height } = Dimensions.get("window");

// Default specialization options (will be replaced with API data)
const defaultSpecializationOptions = [
  { label: "Loading specializations...", value: "" },
];

// Years of experience options
const experienceOptions = [
  { label: "Less than 1 year", value: "Less than 1 year" },
  { label: "1-3 years", value: "1-3 years" },
  { label: "3-5 years", value: "3-5 years" },
  { label: "5-10 years", value: "5-10 years" },
  { label: "10+ years", value: "10+ years" },
];

export default function VerifyQualificationsComponent() {
  const [loading, setLoading] = useState(false);
  const [fetchingSpecializations, setFetchingSpecializations] = useState(true);
  const [fetchingProfessionalDetails, setFetchingProfessionalDetails] = useState(true);
  const { handleToast } = useToast();
  const [documentUploaded, setDocumentUploaded] = useState(false);
  const [documentInfo, setDocumentInfo] = useState<{
    name: string;
    uri: string;
    type: string;
    size: number;
  } | null>(null);
  const [specializationOptions, setSpecializationOptions] = useState<
    Array<{ label: string; value: string }>
  >([{ label: "Loading specializations...", value: "" }]);
  const [initialValues, setInitialValues] = useState({
    registrationNumber: "",
    specialization: "",
    experience: "",
    documentUploaded: false,
  });
  const [networkError, setNetworkError] = useState<any>(null);
  const [showNetworkError, setShowNetworkError] = useState(false);

  // Fetch healthcare specializations and professional details from API
  useEffect(() => {
    fetchSpecializations();
    fetchProfessionalDetails();
  }, []);

  // Function to fetch healthcare specializations
  const fetchSpecializations = async () => {
    try {
      setFetchingSpecializations(true);
      const response = await GetHealthcareSpecializations();
      if (response &&  Array.isArray(response)) {
        // Transform API response to the format needed for SelectInput
        const options = response.map((item) => ({
          label: item.name,
          value: item.id,
        }));
        setSpecializationOptions(options);
      } else {
        handleToast("Failed to load specializations", "error");
      }
    } catch (error) {
      console.error("Error fetching specializations:", error);
      // Check if it's a network error
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        setNetworkError(error);
        setShowNetworkError(true);
      } else {
        handleToast("Failed to load specializations", "error");
      }
    } finally {
      setFetchingSpecializations(false);
    }
  };

  // Function to fetch professional details
  const fetchProfessionalDetails = async () => {
    try {
      setFetchingProfessionalDetails(true);
      const response = await GetProssionalDetails();

      if (response) {        
        const existingData = {
          registrationNumber: response.data.medicalRegistrationNumber || "",
          specialization: response.data.specializations?.[0]?.id || "",
          experience: response.data.yearsOfExpirence || "",
          documentUploaded: !!response.data.qualificationDocumentUrl,
        };
        setInitialValues(existingData);

        // If there's an existing document, set the document info
        if (response.data.qualificationDocumentUrl) {
          setDocumentUploaded(true);
          setDocumentInfo({
            name: "Existing Document",
            uri: response.data.qualificationDocumentUrl,
            type: "application/pdf",
            size: 0,
          });
        }
      }
    } catch (error) {
      console.error("Error fetching professional details:", error);
      // Don't show error for this, just use default values
    } finally {
      setFetchingProfessionalDetails(false);
    }
  };

  // Function to retry after network error
  const handleRetry = () => {
    setShowNetworkError(false);
    setNetworkError(null);
    fetchSpecializations();
    fetchProfessionalDetails();
  };

  const verificationSchema = yup.object().shape({
    registrationNumber: yup
      .string()
      .required("Medical registration number is required"),
    specialization: yup.string().required("Specialization is required"),
    experience: yup.string().required("Years of experience is required"),
    documentUploaded: yup
      .boolean()
      .oneOf([true], "Please upload your documentation")
      .required("Please upload your documentation"),
  });

  const handleUploadDocument = async (
    setFieldValue: (field: string, value: any) => void
  ) => {
    try {
      // Open document picker for selecting files
      const result = await DocumentPicker.getDocumentAsync({
        type: [
          "application/pdf",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "image/jpeg",
          "image/png",
        ],
        copyToCacheDirectory: true,
        multiple: false,
      });

      // Check if the user selected a document
      if (
        result.canceled === false &&
        result.assets &&
        result.assets.length > 0
      ) {
        const selectedFile = result.assets[0];
        // Check file size (limit to 100MB)
        const fileSize = selectedFile.size || 0;
        const maxSize = 100 * 1024 * 1024; // 100MB in bytes

        if (fileSize > maxSize) {
          handleToast("File size exceeds 100MB limit", "error");
          return;
        }
        // Store document info and update state
        setDocumentInfo({
          name: selectedFile.name,
          uri: selectedFile.uri,
          type: selectedFile.mimeType,
          size: fileSize,
        });

        // Update Formik state
        setFieldValue("documentUploaded", true);
        setDocumentUploaded(true);
        handleToast("Document uploaded successfully", "success");
      }
    } catch (error) {
      console.error("Error picking document:", error);
      handleToast("Failed to upload document. Please try again.", "error");
    }
  };

  const handleSubmit = async (values: {
    registrationNumber: string;
    specialization: string;
    experience: string;
    documentUploaded: boolean;
  }) => {
    try {
      setLoading(true);

      let qualificationDocumentUrl = "";

      // Only upload if a new document was selected
      if (documentInfo && !documentInfo.uri.startsWith("https://")) {
        try {
          // Show uploading message
          handleToast("Uploading document...", "success");

          // Upload document to Cloudinary
          qualificationDocumentUrl = await uploadDocumentToCloudinary(
            documentInfo.uri,
            documentInfo.type
          );

          if (!qualificationDocumentUrl) {
            throw new Error("Failed to upload document");
          }
        } catch (uploadError) {
          console.error("Error uploading document:", uploadError);
          handleToast("Failed to upload document. Please try again.", "error");
          setLoading(false);
          return;
        }
      } else if (documentInfo) {
        // Use existing document URL
        qualificationDocumentUrl = documentInfo.uri;
      }

      // Prepare the request body for the API
      const professionalDetails = {
        specializations: [values.specialization], // API expects an array
        qualificationDocumentUrl: qualificationDocumentUrl,
        medicalRegistrationNumber: values.registrationNumber,
        yearsOfExpirence: values.experience,
      };

      // Call the API to update professional details
      const response = await UpdateProssionalDetails(professionalDetails);
      console.log("Professional details update response:", response);

      // Show success message
      handleToast("Qualification details updated successfully", "success");
    } catch (error) {
      console.error("Error updating professional details:", error);

      // Check if it's a network error
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        setNetworkError(error);
        setShowNetworkError(true);
      } else {
        // Show error message
        handleToast(
          getErrorMessage(error) || "Failed to update professional details",
          "error"
        );
      }
    } finally {
      setLoading(false);
    }
  };

  // If there's a network error, show the NetworkErrorView
  if (showNetworkError) {
    return (
      <NetworkErrorView
        errorType={
          getErrorType(networkError) as
            | "network"
            | "timeout"
            | "server"
            | "generic"
        }
        message={getErrorMessage(networkError)}
        onRetry={handleRetry}
      />
    );
  }

  // Show loading while fetching initial data
  if (fetchingSpecializations || fetchingProfessionalDetails) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <P style={styles.loadingText}>Loading qualification details...</P>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        validationSchema={verificationSchema}
        onSubmit={(values) => {
          handleSubmit(values);
        }}
      >
        {(formikProps) => (
          <ScrollView
            style={{ width: "100%" }}
            automaticallyAdjustKeyboardInsets={true}
            contentContainerStyle={{
              paddingBottom: 50,
            }}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
          >
            {/* Title */}
            <H4 style={styles.title}>Verify Qualifications</H4>

            {/* Form Fields */}
            <View style={styles.formContainer}>
              {/* License Number */}
              <Input
                label="License Number"
                placeholder="--------"
                value={formikProps.values.registrationNumber}
                onChangeText={formikProps.handleChange(
                  "registrationNumber"
                )}
                onBlur={formikProps.handleBlur("registrationNumber")}
                error={
                  formikProps.errors.registrationNumber &&
                  formikProps.touched.registrationNumber
                }
                errorText={formikProps.errors.registrationNumber}
              />

              {/* Specialization */}
              <View style={styles.selectInput}>
                <SelectInput
                 disabled={true}
                  label="Specialization"
                  placeholder={
                    fetchingSpecializations
                      ? "Loading specializations..."
                      : "Select"
                  }
                  options={specializationOptions}
                  selectedValues={
                    formikProps.values.specialization
                      ? [formikProps.values.specialization]
                      : []
                  }
                  onSelect={(values) => {
                    if (values.length > 0) {
                      formikProps.setFieldValue(
                        "specialization",
                        values[0]
                      );
                    }
                  }}
                  multiSelect={false}
                  error={
                    formikProps.errors.specialization &&
                    formikProps.touched.specialization
                  }
                  errorText={formikProps.errors.specialization}
                />
                {fetchingSpecializations && (
                  <ActivityIndicator
                    size="small" 
                    color={colors.primary}
                    style={styles.loadingIndicator}
                  />
                )}
              </View>

              {/* Years of Experience */}
              <SelectInput
                label="Years of Experience"
                placeholder="Select"
                options={experienceOptions}
                selectedValues={
                  formikProps.values.experience
                    ? [formikProps.values.experience]
                    : []
                }
                onSelect={(values) => {
                  if (values.length > 0) {
                    formikProps.setFieldValue("experience", values[0]);
                  }
                }}
                multiSelect={false}
                contStyle={styles.selectInput}
                error={
                  formikProps.errors.experience &&
                  formikProps.touched.experience
                }
                errorText={formikProps.errors.experience}
              />

              {/* Upload Documentation */}
              <View style={styles.uploadSection}>
                <P style={styles.uploadLabel}>Upload Documentation</P>
                <TouchableOpacity
                  style={[
                    styles.uploadContainer,
                    documentUploaded && styles.uploadedContainer,
                    formikProps.errors.documentUploaded &&
                      formikProps.touched.documentUploaded &&
                      styles.uploadError,
                  ]}
                  onPress={() =>
                    handleUploadDocument(formikProps.setFieldValue)
                  }
                  onBlur={() =>
                    formikProps.setFieldTouched("documentUploaded")
                  }
                >
                  <SvgXml
                    xml={
                      svg.upload ||
                      '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="#F0A500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M17 8L12 3L7 8" stroke="#F0A500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 3V15" stroke="#F0A500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
                    }
                    width={24}
                    height={24}
                  />
                  <P style={styles.uploadText}>
                    {documentUploaded
                      ? "Document Uploaded Successfully"
                      : "Drag & Drop your File here or Browse Files"}
                  </P>
                  {documentUploaded && documentInfo && (
                    <P
                      style={[
                        styles.uploadSubtext,
                        { color: colors.primary },
                      ]}
                    >
                      {documentInfo.name}
                    </P>
                  )}
                  <P style={styles.uploadSubtext}>
                    Works with any DOC, DOCX, PDF below 100MB
                  </P>
                  {formikProps.errors.documentUploaded &&
                    formikProps.touched.documentUploaded && (
                      <P style={styles.errorText}>
                        {formikProps.errors.documentUploaded}
                      </P>
                    )}
                </TouchableOpacity>
              </View>
            </View>

            {/* Submit Button */}
            <View style={styles.buttonContainer}>
              <Button
                btnText="Update Qualifications"
                onPress={formikProps.handleSubmit}
                loading={loading}
                style={styles.submitButton}
              />
            </View>
          </ScrollView>
        )}
      </Formik>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: "100%",
    backgroundColor: colors.white,
  },
  title: {
    fontSize: 20,
    fontFamily: fonts.dmSansBold,
    marginBottom: 20,
    color: colors.black,
  },
  formContainer: {
    width: "100%",
    marginTop: 10,
  },
  selectInput: {
    marginTop: 16,
  },
  uploadSection: {
    width: "100%",
    marginTop: 32,
  },
  uploadLabel: {
    fontFamily: fonts.dmSansMedium,
    fontSize: 14,
    color: colors.black,
    marginBottom: 8,
  },
  uploadContainer: {
    width: "100%",
    height: 120,
    borderWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.white,
    paddingHorizontal: 16,
  },
  uploadedContainer: {
    borderColor: colors.primary,
    backgroundColor: colors.primaryLight_100,
  },
  uploadError: {
    borderColor: colors.error || "red",
  },
  uploadText: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    color: colors.black,
    textAlign: "center",
    marginTop: 8,
  },
  uploadSubtext: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    color: colors.navGray,
    textAlign: "center",
    marginTop: 4,
  },
  buttonContainer: {
    width: "100%",
    marginTop: 30,
  },
  submitButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
  },
  errorText: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    color: colors.error || "red",
    marginTop: 4,
  },
  loadingIndicator: {
    position: 'absolute',
    right: 16,
    top: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 14,
    fontFamily: fonts.dmSansRegular,
    color: colors.navGray,
  },
});
