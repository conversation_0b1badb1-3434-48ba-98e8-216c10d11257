import React from "react";
import { StyleSheet, View, TouchableOpacity } from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import P from "../P";
import { SvgXml } from "react-native-svg";
import { svg } from "../../Config/Svg";

interface AddressItemProps {
  address: {
    id: string;
    formattedAddress: string;
  };
  index: number;
  onDelete: () => void;
  onEdit?: () => void;
}

export default function AddressItem({
  address,
  index,
  onDelete,
  onEdit,
}: AddressItemProps) {
  return (
    <View style={styles.container}>
      <View style={styles.addressInfo}>
        <P style={styles.addressLabel}>Address {index + 1}</P>
        <P style={styles.addressText}>{address.formattedAddress}</P>
      </View>
      <TouchableOpacity style={styles.deleteButton} onPress={onDelete}>
        <SvgXml
          xml={svg.trash}
          width={20}
          height={20}
          color="#FF0000"
        />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
  },
  addressInfo: {
    flex: 1,
  },
  addressLabel: {
    fontSize: 14,
    fontFamily: fonts.dmSansBold,
    color: colors.black,
    marginBottom: 4,
  },
  addressText: {
    fontSize: 14,
    fontFamily: fonts.dmSansRegular,
    color: colors.gray1,
  },
  deleteButton: {
    padding: 8,
  },
});
