import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ActivityIndicator } from 'react-native';
import { colors } from '../Config/colors';
import NetworkErrorView from './NetworkErrorView';
import { getErrorType } from '../utils/networkErrorHandler';

interface NetworkErrorHandlerProps {
  children: React.ReactNode;
  isLoading?: boolean;
  error?: any;
  onRetry?: () => void;
}

/**
 * A component that handles network errors and displays appropriate UI
 */
const NetworkErrorHandler: React.FC<NetworkErrorHandlerProps> = ({
  children,
  isLoading = false,
  error = null,
  onRetry,
}) => {
  // If there's an error, show the NetworkErrorView
  if (error) {
    return (
      <NetworkErrorView
        errorType={getErrorType(error)}
        message={error.message}
        onRetry={onRetry}
      />
    );
  }

  // If loading, show a loading indicator
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  // Otherwise, render the children
  return <>{children}</>;
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
});

export default NetworkErrorHandler;
