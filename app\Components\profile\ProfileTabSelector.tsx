import React from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import P from "./../P";

interface TabSelectorProps {
  tabs: string[];
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export default function TabSelector({
  tabs,
  activeTab,
  onTabChange,
}: TabSelectorProps) {
  return (
    <View style={styles.container}>
      {tabs.map((tab) => (
        <TouchableOpacity
          key={tab}
          style={[styles.tab, activeTab === tab && styles.activeTab]}
          onPress={() => onTabChange(tab)}
        >
          <P
            style={[styles.tabText, activeTab === tab && styles.activeTabText]}
          >
            {tab}
          </P>
        </TouchableOpacity>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  tab: {
    alignItems: "center",
    paddingVertical: 12,
    position: "relative",
  },
  activeTab: {},
  tabText: {
    fontFamily: fonts.dmSansBold,
    fontSize: 14,
    color: colors.navGray,
    letterSpacing: 2,
  },
  activeTabText: {
    fontFamily: fonts.dmSansBold,
    color: colors.black,
  },
  activeIndicator: {
    position: "absolute",
    bottom: 0,
    width: "100%",
    height: 2,
    backgroundColor: colors.black,
  },
});
