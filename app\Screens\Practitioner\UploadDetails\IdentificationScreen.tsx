import React, { useState } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
  ScrollView,
  Image,
  Platform,
  ActivityIndicator,
  Alert,
} from "react-native";
import * as DocumentPicker from "expo-document-picker";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../Config/Svg";
import AppHeader from "../../../Components/AppHeader";
import Input from "../../../Components/Input";
import SelectInput from "../../../Components/SelectInput";
import * as yup from "yup";
import { Formik } from "formik";
import { useToast } from "../../../Context/ToastContext";
import { UpdateUserIdentity } from "../../../RequestHandler.tsx/User";
import { uploadDocumentToCloudinary } from "../../../Services/CloudinaryService";
import NetworkErrorView from "../../../Components/NetworkErrorView";
import { getErrorType, getErrorMessage } from "../../../utils/networkErrorHandler";

const { width, height } = Dimensions.get("window");

// ID type options for dropdown
const idTypeOptions = [
  { label: "National Identification Number", value: "NIN" },
  { label: "Passport", value: "passport" },
  { label: "Voter's Card", value: "voters-card" },
];

export default function IdentificationScreen({ navigation, route }) {
  const [loading, setLoading] = useState(false);
  const { handleToast } = useToast();
  const [documentUploaded, setDocumentUploaded] = useState(false);
  const [documentInfo, setDocumentInfo] = useState<{
    name: string;
    uri: string;
    type: string;
    size: number;
  } | null>(null);
  const [networkError, setNetworkError] = useState<any>(null);
  const [showNetworkError, setShowNetworkError] = useState(false);

  // Get doctor profile data from previous screen if available
  const { doctorProfile } = route.params || {};

  // Function to handle network errors
  const handleNetworkError = (error: any) => {
    setNetworkError(error);
    setShowNetworkError(true);
  };

  // Function to retry after network error
  const handleRetry = () => {
    setShowNetworkError(false);
    setNetworkError(null);
  };

  const identificationSchema = yup.object().shape({
    idType: yup.string().required("ID type is required"),
    idNumber: yup.string().required("ID number is required"),
    documentUploaded: yup
      .boolean()
      .oneOf([true], "Please upload your documentation")
      .required("Please upload your documentation"),
  });

  const handleUploadDocument = async (setFieldValue: (field: string, value: any) => void) => {
    try {
      // Open document picker for selecting files
      const result = await DocumentPicker.getDocumentAsync({
        type: [
          "application/pdf",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "image/jpeg",
          "image/png",
        ],
        copyToCacheDirectory: true,
        multiple: false,
      });

      // Check if the user selected a document
      if (
        result.canceled === false &&
        result.assets &&
        result.assets.length > 0
      ) {
        const selectedFile = result.assets[0];

        // Check file size (limit to 100MB)
        const fileSize = selectedFile.size || 0;
        const maxSize = 100 * 1024 * 1024; // 100MB in bytes

        if (fileSize > maxSize) {
          handleToast("File size exceeds 100MB limit", "error");
          return;
        }

        // Store document info and update state
        setDocumentInfo({
          name: selectedFile.name,
          uri: selectedFile.uri,
          type: selectedFile.mimeType,
          size: fileSize,
        });

        // Update Formik state
        setFieldValue("documentUploaded", true);
        setDocumentUploaded(true);
        handleToast("Document uploaded successfully", "success");
      }
    } catch (error) {
      console.error("Error picking document:", error);
      handleToast("Failed to upload document. Please try again.", "error");
    }
  };

  const handleContinue = async (values: {
    idType: string;
    idNumber: string;
    documentUploaded: boolean;
  }) => {
    // Formik validation will handle the required fields
    if (!documentInfo) {
      handleToast("Please upload your identification document", "error");
      return;
    }

    try {
      setLoading(true);

      // First, upload the document to Cloudinary
      let documentUrl = "";
      try {
        // Show uploading message
        handleToast("Uploading document...", "success");

        // Upload document to Cloudinary
        documentUrl = await uploadDocumentToCloudinary(
          documentInfo.uri,
          documentInfo.type
        );

        if (!documentUrl) {
          throw new Error("Failed to upload document");
        }
      } catch (uploadError) {
        console.error("Error uploading document:", uploadError);
        handleToast("Failed to upload document. Please try again.", "error");
        setLoading(false);
        return;
      }

      // Prepare the request body for the API according to the required format
      const identityData = {
        id_type: values.idType,
        documentUrl: documentUrl,
        documentId: values.idNumber
      };

      console.log("Sending identity data:", identityData);

      // Call the API to update user identity
      const response = await UpdateUserIdentity(identityData);
      console.log("Identity update response:", response);

      // Show success message
      handleToast("Identification details submitted successfully", "success");

      // Combine identification data with doctor profile for navigation
      const updatedDoctorProfile = {
        ...doctorProfile,
        identification: {
          idType: values.idType,
          idNumber: values.idNumber,
          documentUrl: documentUrl,
        },
      };

      // Navigate to profile details screen
      navigation.navigate("ProfileDetailsScreen");
    } catch (error) {
      console.error("Error updating identity:", error);

      // Check if it's a network error
      if (getErrorType(error) === "network" || getErrorType(error) === "timeout") {
        setNetworkError(error);
        setShowNetworkError(true);
      } else {
        // Show error message
        handleToast(
          getErrorMessage(error) || "Failed to update identity",
          "error"
        );
      }
    } finally {
      setLoading(false);
    }
  };

  // If there's a network error, show the NetworkErrorView
  if (showNetworkError) {
    return (
      <NetworkErrorView
        errorType={getErrorType(networkError) as "network" | "timeout" | "server" | "generic"}
        message={getErrorMessage(networkError)}
        onRetry={handleRetry}
      />
    );
  }

  return (
    <View style={styles.mainContainer}>
      <Div>
        <Formik
          initialValues={{
            idType: "",
            idNumber: "",
            documentUploaded: false,
          }}
          validationSchema={identificationSchema}
          onSubmit={(values) => {
            handleContinue(values);
          }}
        >
          {(formikProps) => (
            <View style={styles.container}>
              <ScrollView
                style={{ width: "100%" }}
                automaticallyAdjustKeyboardInsets={true}
                contentContainerStyle={{
                  alignItems: "center",
                  paddingBottom: 100,
                }}
                showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}
              >
                {/* Header */}
                <View style={{ width: "100%" }}>
                  <AppHeader navigation={navigation} />
                </View>

                {/* Title */}
                <H4 style={styles.title}>Identification</H4>

                {/* Form Fields */}
                <View style={styles.formContainer}>
                  {/* ID Type */}
                  <SelectInput
                    label="ID type"
                    placeholder="Select"
                    options={idTypeOptions}
                    selectedValues={
                      formikProps.values.idType
                        ? [formikProps.values.idType]
                        : []
                    }
                    onSelect={(values) => {
                      if (values.length > 0) {
                        formikProps.setFieldValue("idType", values[0]);
                      }
                    }}
                    multiSelect={false}
                    error={
                      formikProps.errors.idType && formikProps.touched.idType
                    }
                    errorText={formikProps.errors.idType}
                  />

                  {/* ID Number */}
                  <Input
                    contStyle={{ marginTop: 16 }}
                    label="ID number"
                    placeholder="--------"
                    value={formikProps.values.idNumber}
                    onChangeText={formikProps.handleChange("idNumber")}
                    onBlur={formikProps.handleBlur("idNumber")}
                    error={
                      formikProps.errors.idNumber &&
                      formikProps.touched.idNumber
                    }
                    errorText={formikProps.errors.idNumber}
                  />

                  {/* Upload Documentation */}
                  <View style={styles.uploadSection}>
                    <P style={styles.uploadLabel}>Upload Documentation</P>
                    <TouchableOpacity
                      style={[
                        styles.uploadContainer,
                        documentUploaded && styles.uploadedContainer,
                        formikProps.errors.documentUploaded &&
                          formikProps.touched.documentUploaded &&
                          styles.uploadError,
                      ]}
                      onPress={() =>
                        handleUploadDocument(formikProps.setFieldValue)
                      }
                      onBlur={() =>
                        formikProps.setFieldTouched("documentUploaded")
                      }
                    >
                      <SvgXml
                        xml={
                          svg.upload ||
                          '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="#F0A500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M17 8L12 3L7 8" stroke="#F0A500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 3V15" stroke="#F0A500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
                        }
                        width={24}
                        height={24}
                      />
                      <P style={styles.uploadText}>
                        {documentUploaded
                          ? "Document Uploaded Successfully"
                          : "Drag & Drop your File here or Browse Files"}
                      </P>
                      {documentUploaded && documentInfo && (
                        <P
                          style={[
                            styles.uploadSubtext,
                            { color: colors.primary },
                          ]}
                        >
                          {documentInfo.name}
                        </P>
                      )}
                      <P style={styles.uploadSubtext}>
                        Works with any DOC, DOCX, PDF below 100MB
                      </P>
                      {formikProps.errors.documentUploaded &&
                        formikProps.touched.documentUploaded && (
                          <P style={styles.errorText}>
                            {formikProps.errors.documentUploaded}
                          </P>
                        )}
                    </TouchableOpacity>
                  </View>
                </View>

                {/* Continue Button */}
                <View style={styles.buttonContainer}>
                  <Button
                    btnText="Continue"
                    onPress={formikProps.handleSubmit}
                    loading={loading}
                    style={styles.continueButton}
                  />
                </View>
              </ScrollView>
            </View>
          )}
        </Formik>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "90%",
    height: "100%",
    backgroundColor: colors.white,
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontFamily: fonts.dmSansBold,
    marginTop: 16,
    marginBottom: 24,
    alignSelf: "flex-start",
  },
  formContainer: {
    width: "100%",
    marginTop: (2 * height) / 100,
  },
  uploadSection: {
    width: "100%",
    marginTop: 32,
  },
  uploadLabel: {
    fontFamily: fonts.dmSansMedium,
    fontSize: 14,
    color: colors.black,
    marginBottom: 8,
  },
  uploadContainer: {
    width: "100%",
    height: 120,
    borderWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  uploadedContainer: {
    borderColor: colors.primary,
    backgroundColor: colors.primaryLight,
  },
  uploadError: {
    borderColor: colors.error || "red",
  },
  uploadText: {
    fontFamily: fonts.dmSansMedium,
    fontSize: 14,
    color: colors.b2Brown,
    marginTop: 8,
    textAlign: "center",
  },
  uploadSubtext: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    color: colors.gray1,
    marginTop: 4,
    textAlign: "center",
  },
  errorText: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    color: colors.error || "red",
    marginTop: 4,
  },
  buttonContainer: {
    width: "100%",
    marginTop: (5 * height) / 100,
  },
  continueButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
  },
});
