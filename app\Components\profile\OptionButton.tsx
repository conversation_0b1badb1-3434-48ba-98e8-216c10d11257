import React from "react";
import { StyleSheet, TouchableOpacity } from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import P from "./../P";

interface OptionButtonProps {
  label: string;
  onPress: () => void;
  selected?: boolean;
}

export default function OptionButton({
  label,
  onPress,
  selected = false,
}: OptionButtonProps) {
  return (
    <TouchableOpacity
      style={[
        styles.container,
        selected && styles.selectedContainer
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <P style={[styles.label, selected && styles.selectedLabel]}>{label}</P>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    paddingVertical: 18,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.stroke || "#EEEEEE",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 12,
  },
  selectedContainer: {
    borderColor: colors.primary || "#007AFF",
    backgroundColor: colors.primaryLight || "#E1F5FE",
  },
  label: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    color: "#FFA000",
  },
  selectedLabel: {
    fontFamily: fonts.dmSansMedium,
  },
});