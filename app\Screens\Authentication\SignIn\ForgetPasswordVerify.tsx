import React, { useState, useEffect } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import { OtpInput } from "react-native-otp-entry";
import AppHeader from "../../../Components/AppHeader";
import { maskEmail, maskPhoneNumber } from "../../../utils/Functions";
import DotLoader from "../../../Components/DotLoader";
const { width, height } = Dimensions.get("window");
import {
  CreateUser,
  SendOtp,
  VerifyOtp,
} from "../../../RequestHandler.tsx/Auth";

export default function ForgottenPassWordVerify({ navigation, route }) {
  const { data } = route.params || {};
  // Assuming email is passed from previous screen
  const { value } = route.params || {};
  // State for OTP inputs
  const [otpCode, setOtpCode] = useState("");
  const [timer, setTimer] = useState(60);
  const [isResendDisabled, setIsResendDisabled] = useState(true);
  const [otpError, setOtpError] = useState("");
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    sendOtp();
  }, []);
  // Timer effect
  useEffect(() => {
    if (timer > 0) {
      const interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer - 1);
      }, 1000);
      return () => clearInterval(interval);
    } else {
      setIsResendDisabled(false);
    }
  }, [timer]);
  const sendOtp = async () => {
    setLoading(true);
    try {
      const body = value.includes("@")
        ? {
            email: value,
            type: "reset-password",
          }
        : {
            phone: value,
            type: "reset-password",
          };

      await SendOtp(body);
      // OTP sent successfully
      setOtpError("");
    } catch (error) {
      console.log(error);
      setOtpError("Failed to send verification code");
      // You can add a toast notification here if you have a toast handler
      // handleToast(error.message || "Failed to send code", "error");
    } finally {
      setLoading(false);
    }
  };

  // verify email
  const verifyOtp = async (otp) => {
    setLoading(true);
    try {
      const body = value.includes("@")
        ? {
            otp: String(otp),
            email: value,
            type: "reset-password",
          }
        : {
            otp: String(otp),
            phone: value,
            type: "reset-password",
          };

      await VerifyOtp(body);
      // Navigate to reset password screen with email/phone
      navigation.navigate("ResetPasswordScreen", { value: value });
    } catch (error) {
      console.log(error);
      setOtpError("Invalid verification code");
      // You can add a toast notification here if you have a toast handler
      // handleToast(error.message || "Failed to verify code", "error");
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    sendOtp();
  }, []);
  // Resend OTP function
  const resendOtp = () => {
    if (!isResendDisabled) {
      // Reset OTP field
      setOtpCode("");
      // Reset timer
      setTimer(60);
      setIsResendDisabled(true);
      sendOtp();
      setOtpError("");
      // Add API call to resend OTP here
      console.log("Resending OTP to", value);
    }
  };

  // Handle OTP changes
  const handleOtpChange = (text) => {
    setOtpCode(text);
    setOtpError("");
  };

  // Handle OTP filled
  const handleOtpFilled = (text) => {
    verifyOtp(text);
  };

  return (
    <View style={styles.mainContainer}>
      <Div>
        <View style={styles.container}>
          {/* Back Button */}
          <AppHeader navigation={navigation} />

          {/* Title */}
          <H4 style={styles.title}>
            Verify {value.includes("@") ? "PhoneNumber" : "Email address"}
          </H4>

          {/* Description */}
          <P style={styles.description}>
            {value.includes("@")
              ? "We’ve sent an SMS with an activation code to your phone"
              : "We've sent an Email with an activation code to your Email"}{" "}
            {/* <P style={styles.emailText}>
              {value || activeTab?.includes("@") ? activeTab : value}
            </P> */}
          </P>

          {/* OTP Input */}
          <View style={styles.otpWrapper}>
            <OtpInput
              numberOfDigits={5}
              focusColor={colors.primary}
              autoFocus={true}
              hideStick={true}
              placeholder=""
              blurOnFilled={true}
              disabled={false}
              type="numeric"
              secureTextEntry={false}
              focusStickBlinkingDuration={500}
              onFocus={() => console.log("Focused")}
              onBlur={() => console.log("Blurred")}
              onTextChange={handleOtpChange}
              onFilled={handleOtpFilled}
              textInputProps={{
                accessibilityLabel: "Email Verification Code",
              }}
              theme={{
                containerStyle: styles.otpContainer,
                pinCodeContainerStyle: otpError
                  ? styles.otpInputError
                  : styles.otpInput,
                pinCodeTextStyle: styles.otpText,
                focusedPinCodeContainerStyle: styles.otpInputFocused,
                placeholderTextStyle: styles.otpPlaceholder,
                filledPinCodeContainerStyle: styles.otpInputFilled,
                disabledPinCodeContainerStyle: styles.otpInputDisabled,
              }}
            />
          </View>
          {/* Otp Error */}
          {loading ? (
            <View style={{ marginTop: (3 * height) / 100 }}>
              <DotLoader loading={true} />
            </View>
          ) : (
            <>
              <View
                style={{
                  width: "100%",
                  alignItems: "center",
                  justifyContent: "center",
                  marginTop: 14,
                }}
              >
                {otpError && (
                  <P
                    // @ts-ignore
                    style={[
                      {
                        marginRight: 8,
                        fontFamily: fonts.dmSansRegular,
                        color: colors.red,
                      },
                    ]}
                  >
                    {otpError ? otpError : ""}
                  </P>
                )}
              </View>
              {/* Resend Code Button */}
              <View style={styles.resendContainer}>
                {!isResendDisabled ? (
                  <>
                    <P
                      // @ts-ignore
                      style={[
                        styles.resendText,
                        {
                          marginRight: 8,
                          fontFamily: fonts.dmSansRegular,
                          color: colors.gray1,
                        },
                      ]}
                    >
                      I didn’t receive a code
                    </P>
                    <TouchableOpacity onPress={resendOtp}>
                      <P
                        // @ts-ignore
                        style={[
                          styles.resendText,
                          isResendDisabled && styles.disabledText,
                        ]}
                      >
                        Resend
                      </P>
                    </TouchableOpacity>
                  </>
                ) : (
                  <>
                    <P
                      // @ts-ignore
                      style={[
                        styles.resendText,
                        isResendDisabled && styles.disabledText,
                      ]}
                    >
                      Send code again
                    </P>

                    <P style={styles.timerText}>
                      {`${timer < 10 ? "00:0" : "00:"}${timer}`}
                    </P>
                  </>
                )}
              </View>
            </>
          )}
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "90%",
    backgroundColor: colors.white,
  },
  backButton: {
    alignSelf: "flex-start",
    padding: 10,
    marginTop: 20,
  },
  backButtonText: {
    fontSize: 28,
    fontFamily: fonts.dmSansBold,
  },
  title: {
    fontFamily: fonts.dmSansBold,
    fontSize: 24,
    alignSelf: "flex-start",
    marginTop: 20,
    marginBottom: 18,
  },
  description: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    color: colors.gray1,
    alignSelf: "flex-start",
    marginBottom: 29,
    lineHeight: 22,
  },
  emailText: {
    fontFamily: fonts.dmSansMedium,
  },
  otpWrapper: {
    width: "100%",
  },
  otpContainer: {
    width: "100%",
    justifyContent: "space-between",
  },
  otpInput: {
    width: width * 0.15,
    height: width * 0.15,
    borderWidth: 1,
    borderColor: colors.stroke,
    borderRadius: 10,
    marginHorizontal: 0,
  },
  otpInputError: {
    width: width * 0.15,
    height: width * 0.15,
    borderWidth: 1,
    borderColor: colors.red,
    borderRadius: 10,
    marginHorizontal: 0,
  },
  otpText: {
    color: colors.black,
    fontSize: 28,
    fontFamily: fonts.dmSansMedium,
  },
  otpInputFocused: {
    borderColor: colors.primary,
    backgroundColor: colors.white,
  },
  otpPlaceholder: {
    color: colors.gray1,
    fontSize: 20,
    fontFamily: fonts.dmSansRegular,
  },
  otpInputFilled: {
    borderColor: colors.stroke,
    backgroundColor: colors.white,
  },
  otpInputDisabled: {
    borderColor: colors.stroke,
    backgroundColor: "#F5F5F5",
  },
  resendContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 39,
    marginBottom: 40,
  },
  resendText: {
    fontFamily: fonts.dmSansMedium,
    fontSize: 14,
    color: colors.black,
  },
  disabledText: {
    color: colors.gray1,
  },
  timerText: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    color: colors.gray1,
    marginLeft: 8,
  },
});
