import React, { useEffect, useState, useRef, useContext } from "react";
import {
  View,
  StyleSheet,
  Dimensions,
  Animated,
  Easing,
  StatusBar,
} from "react-native";
import { colors } from "../Config/colors";
import { fonts } from "../Config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../Config/Svg";
import { CredentailsContext } from "../Context/CredentailsContext";

const { width, height } = Dimensions.get("window");

// If you don't have these logo parts in your SVG config, you should add them
const logoTextParts = {
  O: svg.o,
  n: svg.n,
  l: svg.l,
  y: svg.y,
  m: svg.m,
  e: svg.e,
  d: svg.d,
  plusIcon: svg.hb,
};

export default function CustomSplashScreen() {
  // Create animated values for each part of the logo
  const { storedCredentails, setStoredCredentails } =
    useContext(CredentailsContext);
  const oOpacity = useRef(new Animated.Value(0)).current;
  const nOpacity = useRef(new Animated.Value(0)).current;
  const lOpacity = useRef(new Animated.Value(0)).current;
  const yOpacity = useRef(new Animated.Value(0)).current;
  const mOpacity = useRef(new Animated.Value(0)).current;
  const eOpacity = useRef(new Animated.Value(0)).current;
  const dOpacity = useRef(new Animated.Value(0)).current;
  const plusOpacity = useRef(new Animated.Value(0)).current;

  // Progress bar animation
  const progressWidth = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Sequence the animations
    Animated.sequence([
      // Fade in 'O'
      Animated.timing(oOpacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
        easing: Easing.ease,
      }),
      // Fade in 'n'
      Animated.timing(nOpacity, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
        easing: Easing.ease,
      }),
      // Fade in 'l'
      Animated.timing(lOpacity, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
        easing: Easing.ease,
      }),
      // Fade in 'y'
      Animated.timing(yOpacity, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
        easing: Easing.ease,
      }),
      // Fade in 'm'
      Animated.timing(mOpacity, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
        easing: Easing.ease,
      }),
      // Fade in 'e'
      Animated.timing(eOpacity, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
        easing: Easing.ease,
      }),
      // Fade in 'd'
      Animated.timing(dOpacity, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
        easing: Easing.ease,
      }),
      // Fade in the plus icon
      Animated.timing(plusOpacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
        easing: Easing.ease,
      }),
    ]).start();

    // Animate progress bar
    Animated.timing(progressWidth, {
      toValue: 1,
      duration: 2200, // Total duration of all animations above
      useNativeDriver: false,
      easing: Easing.linear,
    }).start();

    // Navigate to next screen after animation completes
    const timer = setTimeout(() => {}, 2500);
    return () => clearTimeout(timer);
  }, []);

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={colors.primary} />

      {/* Logo Container */}
      <View style={styles.logoContainer}>
        <View style={styles.logoTextContainer}>
          {/* O */}
          <Animated.View style={{ opacity: oOpacity }}>
            <SvgXml xml={logoTextParts.O} />
          </Animated.View>

          {/* n */}
          <Animated.View
            style={{ opacity: nOpacity, marginLeft: -2, marginTop: 5 }}
          >
            <SvgXml xml={logoTextParts.n} />
          </Animated.View>

          {/* l */}
          <Animated.View style={{ opacity: lOpacity, marginLeft: -2 }}>
            <SvgXml xml={logoTextParts.l} />
          </Animated.View>

          {/* y */}
          <Animated.View
            style={{ opacity: yOpacity, marginLeft: -2, marginTop: 20 }}
          >
            <SvgXml xml={logoTextParts.y} />
          </Animated.View>

          {/* m */}
          <Animated.View style={{ opacity: mOpacity, marginLeft: -2 }}>
            <SvgXml xml={logoTextParts.m} />
          </Animated.View>

          {/* e */}
          <Animated.View style={{ opacity: eOpacity, marginLeft: -2 }}>
            <SvgXml xml={logoTextParts.e} />
          </Animated.View>

          {/* d */}
          <Animated.View
            style={{ opacity: dOpacity, marginLeft: -2, marginBottom: 10 }}
          >
            <SvgXml xml={logoTextParts.d} />
          </Animated.View>

          {/* Plus icon */}
          <Animated.View
            style={{
              opacity: plusOpacity,
              marginLeft: 2,
              position: "absolute",
              right: -35,
              top: -25,
            }}
          >
            <SvgXml xml={logoTextParts.plusIcon} />
          </Animated.View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.primary || "#1A73E8", // Fallback to blue if primary not defined
    justifyContent: "center",
    alignItems: "center",
  },
  logoContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  logoTextContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    height: 50,
    gap: 3,
  },
  progressBarContainer: {
    position: "absolute",
    bottom: height * 0.05, // 5% from bottom
    width: width * 0.5, // 50% of screen width
    height: 4,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    borderRadius: 2,
    overflow: "hidden",
  },
  progressBar: {
    height: "100%",
    backgroundColor: "#FFFFFF",
  },
});
