/**
 * CloudinaryService.tsx
 * Service for handling Cloudinary uploads (images and documents)
 */

// Replace these with your actual Cloudinary credentials
const CLOUDINARY_CLOUD_NAME = "dgeteyz5o";
const CLOUDINARY_UPLOAD_PRESET = "onlymed";

/**
 * Uploads an image to Cloudinary
 * @param base64Image - Base64 encoded image string (with or without data URI prefix)
 * @returns Promise with the Cloudinary URL or error
 */
export const uploadImageToCloudinary = async (base64Image: string): Promise<string> => {
  try {
    // Ensure the base64 string doesn't include the data URI prefix
    const base64Data = base64Image.includes('base64,')
      ? base64Image.split('base64,')[1]
      : base64Image;

    // Create form data for the upload
    const formData = new FormData();
    formData.append('file', `data:image/jpeg;base64,${base64Data}`);
    formData.append('upload_preset', CLOUDINARY_UPLOAD_PRESET);

    // Upload to Cloudinary
    const response = await fetch(
      `https://api.cloudinary.com/v1_1/${CLOUDINARY_CLOUD_NAME}/image/upload`,
      {
        method: 'POST',
        body: formData,
      }
    );

    // Parse the response
    const data = await response.json();

    if (response.ok) {
      // Return the secure URL of the uploaded image
      return data.secure_url;
    } else {
      throw new Error(data.error?.message || 'Failed to upload image to Cloudinary');
    }
  } catch (error) {
    console.error('Error uploading to Cloudinary:', error);
    throw error;
  }
};

/**
 * Uploads a document file to Cloudinary
 * @param fileUri - URI of the file to upload
 * @param mimeType - MIME type of the file
 * @returns Promise with the Cloudinary URL or error
 */
export const uploadDocumentToCloudinary = async (fileUri: string, mimeType: string): Promise<string> => {
  try {
    // Create a new FormData instance
    const formData = new FormData();

    // Append the file to the form data
    // @ts-ignore - TypeScript doesn't recognize the structure we're creating
    formData.append('file', {
      uri: fileUri,
      type: mimeType,
      name: fileUri.split('/').pop() || 'document',
    });

    // Add the upload preset
    formData.append('upload_preset', CLOUDINARY_UPLOAD_PRESET);

    // Upload to Cloudinary (using the 'auto' resource type to handle different file types)
    const response = await fetch(
      `https://api.cloudinary.com/v1_1/${CLOUDINARY_CLOUD_NAME}/auto/upload`,
      {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    // Parse the response
    const data = await response.json();

    if (response.ok) {
      // Return the secure URL of the uploaded document
      return data.secure_url;
    } else {
      throw new Error(data.error?.message || 'Failed to upload document to Cloudinary');
    }
  } catch (error) {
    console.error('Error uploading document to Cloudinary:', error);
    throw error;
  }
};
