/**
 * authErrorHandler.ts
 * Utility functions for handling authentication errors
 */

import AsyncStorage from "@react-native-async-storage/async-storage";
import { GoogleSignin } from "@react-native-google-signin/google-signin";

// Store a reference to the setStoredCredentials function
let globalSetStoredCredentials: ((value: any) => void) | null = null;

/**
 * Set the global reference to the setStoredCredentials function
 * This should be called from the App component when it initializes
 * @param setCredentials - The setStoredCredentials function from CredentialsContext
 */
export const setAuthHandler = (setCredentials: (value: any) => void) => {
  globalSetStoredCredentials = setCredentials;
};

/**
 * Handle authentication errors (401 Unauthorized)
 * This function will log the user out if a 401 error is received
 * @param error - The error object from the API request
 * @returns The original error for further processing
 */
export const handleAuthError = async (error: any): Promise<any> => {
  // Check if the error is a 401 Unauthorized error
  const isUnauthorized = 
    (error && error.status === 401) || 
    (error && error.originalError && error.originalError.status === 401);

  if (isUnauthorized) {
    console.log("401 Unauthorized error detected, logging out user");
    
    try {
      // Sign out from Google if applicable
      try {
        await GoogleSignin.signOut();
      } catch (googleError) {
        console.error("Error signing out from Google:", googleError);
        // Continue with the logout process even if Google sign-out fails
      }
      
      // Clear auth token from AsyncStorage
      await AsyncStorage.removeItem("cookies##$$");
      
      // Clear credentials from context if the handler is set
      if (globalSetStoredCredentials) {
        globalSetStoredCredentials(null);
      } else {
        console.warn("globalSetStoredCredentials is not set, cannot update auth state");
      }
    } catch (logoutError) {
      console.error("Error during automatic logout:", logoutError);
    }
  }
  
  // Return the original error for further processing
  return error;
};
