// ToastContext.tsx
import React, { createContext, useState, useContext, useRef } from "react";
import { Animated, Dimensions, Platform, StyleSheet, View } from "react-native";
import { SvgXml } from "react-native-svg";
import { colors } from "../Config/colors";
import { fonts } from "../Config/Fonts";
import { svg } from "../Config/Svg";
import P from "../Components/P";
const { width, height } = Dimensions.get("window");
const baseHeight = 802;

interface ToastContextType {
  handleToast: (text: string, type?: "error" | "success") => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider");
  }
  return context;
};

export const ToastProvider = ({ children }: { children: React.ReactNode }) => {
  const [show, setShow] = useState(false);
  const [text, setText] = useState("");
  const [type, setType] = useState("");
  const fadeAnim = useState(new Animated.Value(0))[0];
  const translateY = useState(new Animated.Value(100))[0];
  const toastTimeout = useRef<NodeJS.Timeout | number | null>(null);

  const handleToast = (text: string, type?: "error" | "success") => {
    if (toastTimeout.current) clearTimeout(toastTimeout.current);
    fadeAnim.stopAnimation();
    translateY.stopAnimation();

    setText(text);
    setType(type || "success");
    setShow(true);

    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();

    toastTimeout.current = setTimeout(() => {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(translateY, {
          toValue: 100,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setShow(false);
      });
    }, 4000);
  };

  return (
    <ToastContext.Provider value={{ handleToast }}>
      {children}
      {show && (
        <Animated.View
          style={[styles.popBody, { opacity: fadeAnim }]}
          pointerEvents="box-none"
        >
          <Animated.View
            style={[
              styles.popCard,
              {
                transform: [{ translateY }],
                backgroundColor: colors.white,
                borderLeftColor:
                  type === "error" ? colors.error : colors.success,
              },
              Platform.OS === "ios" ? styles.iosShadow : styles.androidShadow,
            ]}
          >
            {type === "error" ? (
              <SvgXml xml={svg.error} style={{ marginLeft: 5 }} />
            ) : (
              <SvgXml xml={svg.tick} style={{ marginLeft: 5 }} />
            )}
            <View style={{ marginLeft: 12 }}>
              <P
                style={{
                  color: type === "error" ? colors.error : colors.success,
                }}
              >
                {type === "error" ? "Error" : "Success"}
              </P>
              {text && <P style={styles.headerText}>{text}</P>}
            </View>
          </Animated.View>
        </Animated.View>
      )}
    </ToastContext.Provider>
  );
};

const styles = StyleSheet.create({
  popBody: {
    width,
    height,
    flex: 1,
    backgroundColor: "transparent",
    position: "absolute",
    alignItems: "center",
    justifyContent: "flex-start",
    zIndex: 8000000,
  },
  popCard: {
    width: "90%",
    borderRadius: 16,
    alignItems: "center",
    paddingLeft: 24,
    paddingRight: 40,
    borderLeftWidth: 7,
    position: "absolute",
    bottom:
      Platform.OS == "ios"
        ? (70 / baseHeight) * height
        : (50 / baseHeight) * height,
    flexDirection: "row",
    minHeight: 67,
    paddingTop: 5,
    paddingBottom: 5,
  },
  iosShadow: {
    shadowColor: "#00000021",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.5,
    shadowRadius: 4,
  },
  androidShadow: { elevation: 20 },
  headerText: {
    fontSize: 14,
    fontFamily: fonts.dmSansRegular,
    textAlign: "left",
    color: colors.black,
  },
});
