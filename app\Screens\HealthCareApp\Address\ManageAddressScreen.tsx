import React, { useState, useEffect } from "react";
import { StyleSheet, View, ScrollView, Image, Dimensions } from "react-native";
import { colors } from "../../../Config/colors";
import { fonts } from "../../../Config/Fonts";
import Div from "../../../Components/Div";
import P from "../../../Components/P";
import Button from "../../../Components/Button";
import AppHeader from "../../../Components/AppHeader";
import AddressItem from "../../../Components/address/AddressItem";

const { width, height } = Dimensions.get("window");

// Define address interface
interface AddressData {
  id: string;
  fullName: string;
  apt: string;
  streetAddress: string;
  zipCode: string;
  city: string;
  state: string;
  formattedAddress: string;
}

export default function ManageAddressScreen({
  navigation,
  route,
}: {
  navigation: any;
  route: any;
}) {
  const [addresses, setAddresses] = useState<AddressData[]>([]);

  // Load addresses from storage or API
  useEffect(() => {
    // For demo purposes, we'll use dummy data
    // In a real app, you would fetch this from an API or local storage
    const savedAddresses = route.params?.savedAddresses || [];
    setAddresses(savedAddresses);
  }, [route.params?.savedAddresses]);

  const handleAddAddress = () => {
    navigation.navigate("AddAddressScreen", {});
  };

  const handleDeleteAddress = (index: number) => {
    const updatedAddresses = [...addresses];
    updatedAddresses.splice(index, 1);
    setAddresses(updatedAddresses);
  };

  return (
    <View style={styles.mainContainer}>
      <Div>
        {/* Header */}
          <AppHeader
            navigation={navigation}
            showBackButton={true}
            title="Manage Address"
            navStyle={{justifyContent: "flex-start", paddingHorizontal: 16}}
            titleStyle={{color: colors.black}}
          />
        <View style={{ width:"90%", alignSelf: "center"  }}>
        </View>

        <ScrollView
          style={styles.container}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.contentContainer}
        >
          {addresses.length === 0 ? (
            // Empty state
            <View style={styles.emptyStateContainer}>
              <Image
                source={require("../../../assets/address_empty1.png")}
                style={styles.emptyStateImage}
              />
              <P style={styles.emptyStateText}>No Address Saved yet</P>
            </View>
          ) : (
            // Address list
            <View style={styles.addressListContainer}>
              {addresses.map((address, index) => (
                <AddressItem
                  key={index}
                  address={address}
                  index={index}
                  onDelete={() => handleDeleteAddress(index)}
                />
              ))}
            </View>
          )}
        </ScrollView>

        {/* Add Address Button */}
        <View style={styles.buttonContainer}>
          <Button
            btnText="Add Address"
            onPress={handleAddAddress}
            style={styles.addButton}
          />
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "100%",
  },
  contentContainer: {
    flexGrow: 1,
    paddingHorizontal: 16,
    paddingBottom: 100,
  },
  emptyStateContainer: {
    justifyContent: "center",
    alignItems: "center",
    marginTop: (10 * height) / 100,
  },
  emptyStateImage: {
    width: (70 * width) / 100,
    height: (70 * width) / 100,
    marginBottom: 20,
  },
  emptyStateText: {
    fontSize: 16,
    fontFamily: fonts.dmSansMedium,
    color: colors.black,
    marginTop: 16,
  },
  addressListContainer: {
    width: "100%",
    marginTop: 20,
  },
  buttonContainer: {
    position: "absolute",
    bottom: 20,
    left: 0,
    right: 0,
    paddingHorizontal: 16,
  },
  addButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
  },
});
