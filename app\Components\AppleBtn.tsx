import React, { useState, CSSProperties, useEffect, useContext } from "react";
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Platform,
} from "react-native";
import { SvgXml } from "react-native-svg";
import { svg } from "../Config/Svg";
import P from "./P";
import { colors } from "../Config/colors";
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import {
  ValidateAppleToken,
  ValidateGoogleToken,
} from "../RequestHandler.tsx/Auth";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useToast } from "../Context/ToastContext";
import { CredentailsContext } from "../Context/CredentailsContext";
import * as AppleAuthentication from "expo-apple-authentication";

interface PProps {
  contStyle?: CSSProperties;
  onPress?: any;
  navigation?: any;
  isPractitinal: boolean;
}
const baseHeight = 800;
const { width, height } = Dimensions.get("window");
export default function AppleButton({
  contStyle,
  onPress,
  navigation,
  isPractitinal,
}: PProps) {
  const { handleToast } = useToast();
  const [activeDot, setActiveDot] = useState(0);
  const [loading, setLoading] = useState(false);
  const { storedCredentails, setStoredCredentails } =
    useContext(CredentailsContext);
  const dotsArray = [1, 2, 3];
  useEffect(() => {
    if (loading) {
      const interval = setInterval(() => {
        setActiveDot((prevDot) => (prevDot + 1) % dotsArray.length);
      }, 200);
      return () => clearInterval(interval);
    }
  }, [loading]);

  const validateAppleToken = async (firstname: string, lastname: string, idToken: string) => {
    setLoading(true);
    try {
      const body = {
        firstname: firstname,
        lastname: lastname,
        idToken: idToken,
        isPractitional: isPractitinal,
      };

      const validateToken = await ValidateAppleToken(body);

      // Check for role mismatch
      if (isPractitinal && validateToken?.user?.role === "patient") {
        handleToast(
          "You can't login a patient account as practitioner",
          "error"
        );
        setTimeout(() => {
          navigation.navigate("IntroScreen");
        }, 2000);
        return;
      } else if (
        !isPractitinal &&
        validateToken?.user?.role === "practitional"
      ) {
        handleToast(
          "You can't login a practitioner account as patients",
          "error"
        );
        setTimeout(() => {
          navigation.navigate("IntroScreen");
        }, 2000);
        return;
      }

      // Success case - store credentials
      AsyncStorage.setItem("cookies##$$", JSON.stringify(validateToken))
        .then(() => {
          // @ts-ignore
          setStoredCredentails(validateToken);
          handleToast("Login successful", "success");
        })
        .catch((error) => {
          console.log("Error saving credentials:", error);
        });

    } catch (error) {
      console.log(error);
      // Handle API errors
      if (error.message) {
        handleToast(error.message, "error");
      } else {
        handleToast("Failed to authenticate with Apple", "error");
      }
    } finally {
      setLoading(false);
    }
  };
  const SignIn = async () => {
    if (Platform.OS === "android") {
      handleToast("Apple sign in is not supported on Android", "error");
      return;
    }

    try {
      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });

      if (credential.identityToken) {
        // Get the name from the credential
        const firstName = credential.fullName?.givenName || "";
        const lastName = credential.fullName?.familyName || "";

        // Validate the token
        validateAppleToken(
          firstName,
          lastName,
          credential.identityToken
        );
      } else {
        handleToast("Failed to get authentication token", "error");
      }
    } catch (error) {
      console.log("Apple Sign-In Error:", error);

      if (error.code === "ERR_REQUEST_CANCELED") {
        // User canceled the sign-in flow
        console.log("Sign-in canceled by user");
        handleToast("Authentication canceled", "error");
      } else {
        // Other errors
        handleToast(
          error.message || "Failed to sign in with Apple",
          "error"
        );
      }
      setLoading(false);
    }
  };
  return (
    // @ts-ignore
    <View style={[{ alignItems: "center", width: "48%" }, contStyle]}>
      <TouchableOpacity style={styles.socialButton} onPress={SignIn}>
        {loading ? (
          <View style={styles.loaderContainer}>
            {dotsArray.map((dot, index) => (
              <View
                key={dot}
                style={[
                  styles.dot,
                  {
                    backgroundColor:
                      activeDot === index ? colors.black : colors.gray,
                  },
                ]}
              />
            ))}
          </View>
        ) : (
          <>
            <SvgXml xml={svg.appleBtn} style={styles.socialIcon} />
          </>
        )}
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  loaderContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: 30,
  },
  socialButton: {
    width: "100%",
    height: 56,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: colors.stroke,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.white,
  },
  dot: {
    width: 10,
    height: 10,
    borderRadius: 100,
    backgroundColor: colors.gray,
    marginHorizontal: 2,
  },
  socialIcon: {
    width: 24,
    height: 24,
  },
});
