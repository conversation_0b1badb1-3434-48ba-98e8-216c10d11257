import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
} from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import AppHeader from "../../../Components/AppHeader";
import SelectInput from "../../../Components/SelectInput";
import DateInput from "../../../Components/DateInput";
import Loader from "../../../Components/Loader";
import { GetGeneticConditions, GetVaccines } from "../../../RequestHandler.tsx/Auth";
import { UpdateMedicalHistory } from "../../../RequestHandler.tsx/User";
import NetworkErrorView from "../../../Components/NetworkErrorView";
import { getErrorType, getErrorMessage } from "../../../utils/networkErrorHandler";
import { useToast } from "../../../Context/ToastContext";

// Surgical history options (static for now)
const surgicalHistoryOptions = [
  { label: "None", value: "none" },
  { label: "Procedure", value: "procedure" },
  { label: "Year", value: "year" },
  { label: "Outcome", value: "outcome" },
];

export default function MedicalHistoryScreen2({ navigation, route }) {
  // Get medical history data from previous screen
  const { medicalHistory } = route.params || { medicalHistory: {} };

  // Toast context for showing notifications
  const { handleToast } = useToast();

  // State for all selections
  const [surgicalHistory, setSurgicalHistory] = useState<string[]>([]);
  const [geneticConditions, setGeneticConditions] = useState<string[]>([]);
  const [geneticConditionIds, setGeneticConditionIds] = useState<string[]>([]);
  const [vaccines, setVaccines] = useState<string[]>([]);
  const [vaccineIds, setVaccineIds] = useState<string[]>([]);
  const [lastBoosterDate, setLastBoosterDate] = useState<string>("");

  // State for API options
  const [geneticConditionsOptions, setGeneticConditionsOptions] = useState<any[]>([]);
  const [vaccinesOptions, setVaccinesOptions] = useState<any[]>([]);

  // Loading and error states
  const [loading, setLoading] = useState<boolean>(true);
  const [submitting, setSubmitting] = useState<boolean>(false);

  // Network error states
  const [networkError, setNetworkError] = useState<any>(null);
  const [showNetworkError, setShowNetworkError] = useState<boolean>(false);

  // Function to handle network errors
  const handleNetworkError = (error: any) => {
    setNetworkError(error);
    setShowNetworkError(true);
  };

  // Fetch all data from APIs
  const fetchAllData = async () => {
    try {
      setLoading(true);

      // Fetch genetic conditions
      const geneticResponse = await GetGeneticConditions();
      if (geneticResponse && Array.isArray(geneticResponse)) {
        // Transform API response to options format
        const options = geneticResponse.map((item) => ({
          label: item.name,
          value: item.name,
          id: item.id,
        }));
        setGeneticConditionsOptions(options);
      } else {
        handleToast("Failed to load genetic conditions", "error");
      }

      // Fetch vaccines
      const vaccinesResponse = await GetVaccines();
      if (vaccinesResponse && Array.isArray(vaccinesResponse)) {
        // Transform API response to options format
        const options = vaccinesResponse.map((item) => ({
          label: item.name,
          value: item.name,
          id: item.id,
        }));
        setVaccinesOptions(options);
      } else {
        handleToast("Failed to load vaccines", "error");
      }
    } catch (error) {
      console.log("Error fetching data:", error);

      // Check if it's a network error
      if (getErrorType(error) === 'network' || getErrorType(error) === 'timeout') {
        handleNetworkError(error);
      } else {
        handleToast("Failed to load data", "error");
      }
    } finally {
      setLoading(false);
    }
  };

  // Function to retry all API calls
  const handleRetryAllApiCalls = () => {
    setShowNetworkError(false);
    setNetworkError(null);

    // Retry all API calls
    fetchAllData();
  };

  // Call API function on component mount
  useEffect(() => {
    fetchAllData();
  }, []);

  // Handle selection of genetic conditions
  const handleGeneticConditionsSelect = (selected: string[]) => {
    setGeneticConditions(selected);

    // Get the IDs of the selected genetic conditions
    const ids = selected.map(value => {
      const option = geneticConditionsOptions.find(opt => opt.value === value);
      return option ? option.id : null;
    }).filter(id => id !== null);

    setGeneticConditionIds(ids);
  };

  // Handle selection of vaccines
  const handleVaccinesSelect = (selected: string[]) => {
    setVaccines(selected);

    // Get the IDs of the selected vaccines
    const ids = selected.map(value => {
      const option = vaccinesOptions.find(opt => opt.value === value);
      return option ? option.id : null;
    }).filter(id => id !== null);

    setVaccineIds(ids);
  };

  const handleContinue = async () => {
    try {
      setSubmitting(true);

      // Format the data according to the required API format
      const medicalHistoryData = {
        surgicalHistory: surgicalHistory[0] || "none",
        geneticConditions: geneticConditionIds,
        vaccines: vaccineIds,
        dateOfLastBoaster: lastBoosterDate || new Date().toISOString(),
      };
      console.log("Submitting Medical History:", medicalHistoryData);
      const response = await UpdateMedicalHistory(medicalHistoryData);
      if (response.active) {
        handleToast("Medical history updated successfully", "success");
        navigation.navigate("MedicalHistoryScreen3");
      }
    } catch (error) {
      // Check if it's a network error
      if (getErrorType(error) === 'network' || getErrorType(error) === 'timeout') {
        handleNetworkError(error);
      } else {
        handleToast("Failed to update medical history: " + (error.message || "Unknown error"), "error");
      }
    } finally {
      setSubmitting(false);
    }
  };

  // If there's a network error, show the NetworkErrorView
  if (showNetworkError) {
    return (
      <NetworkErrorView
        errorType={getErrorType(networkError)}
        message={getErrorMessage(networkError)}
        onRetry={handleRetryAllApiCalls}
      />
    );
  }

  return (
    <View style={styles.mainContainer}>
      <Div>
        <View style={styles.container}>
          <ScrollView
            style={{ width: "100%" }}
            contentContainerStyle={{ paddingBottom: 100 }}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
          >
            {/* Header */}
            <View style={{ width: "100%" }}>
              <AppHeader navigation={navigation} />
            </View>

            {/* Title */}
            <H4 style={styles.title}>Medical History</H4>

            {/* Surgical History */}
            <SelectInput
              label="Surgical History"
              placeholder="Select"
              options={surgicalHistoryOptions}
              selectedValues={surgicalHistory}
              onSelect={setSurgicalHistory}
              multiSelect={false}
              contStyle={styles.selectInput}
            />

            {/* Genetic Conditions */}
            <SelectInput
              label="Genetic Conditions(Optional)"
              placeholder="Select"
              options={geneticConditionsOptions}
              selectedValues={geneticConditions}
              onSelect={handleGeneticConditionsSelect}
              multiSelect={true}
              maxSelections={10}
              contStyle={styles.selectInput}
            />

            {/* Immunization Records Section */}
            <View style={styles.section}>
              <H4 style={styles.sectionTitle}>Immunization Records</H4>

              {/* Vaccines */}
              <SelectInput
                label="Vaccines Received"
                placeholder="Vaccines"
                options={vaccinesOptions}
                selectedValues={vaccines}
                onSelect={handleVaccinesSelect}
                multiSelect={true}
                maxSelections={10}
                contStyle={styles.selectInput}
              />

              {/* Date of Last Booster */}
              <DateInput
                label="Date of Last Booster"
                placeholder="Select Date"
                value={lastBoosterDate}
                onSelect={setLastBoosterDate}
                contStyle={styles.selectInput}
              />
            </View>

            {/* Continue Button */}
            <View style={styles.buttonContainer}>
              <Button
                btnText={submitting ? "Saving..." : "Continue"}
                onPress={handleContinue}
                disabled={submitting || loading}
              />
            </View>
          </ScrollView>
        </View>
      </Div>
      {(loading || submitting) && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  loadingText: {
    marginTop: 8,
    color: colors.gray,
    fontFamily: fonts.dmSansRegular,
  },
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "90%",
    height: "100%",
    backgroundColor: colors.white,
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontFamily: fonts.dmSansBold,
    marginTop: 16,
    marginBottom: 24,
    alignSelf: "flex-start",
  },
  section: {
    width: "100%",
    marginTop: 24,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: fonts.dmSansBold,
    marginBottom: 16,
  },
  selectInput: {
    marginBottom: 16,
  },
  buttonContainer: {
    width: "100%",
    marginTop: 16,
  },
});
