import React from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";
import { colors } from "../../Config/colors";
import { fonts } from "../../Config/Fonts";
import P from "./../P";

interface SettingsItemProps {
  label: string;
  value: string;
  placeholder?: string;
  onPress?: () => void;
  readOnly?: boolean;
}

export default function SettingsItem({
  label,
  value,
  placeholder = "Add details...",
  onPress,
  readOnly = false,
}: SettingsItemProps) {
  const isEmpty = !value || value.trim() === "";

  // If readOnly is true, render a View instead of TouchableOpacity
  if (readOnly) {
    return (
      <View style={styles.container}>
        <P style={styles.label}>{label}</P>
        <P style={[styles.value, isEmpty && styles.placeholder]}>
          {isEmpty ? placeholder : value}
        </P>
      </View>
    );
  }

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <P style={styles.label}>{label}</P>
      <P style={[styles.value, isEmpty && styles.placeholder]}>
        {isEmpty ? placeholder : value}
      </P>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
    backgroundColor: "#FAFAFA",
    marginBottom: 8,
    paddingHorizontal: 16,
    borderRadius: 14
  },
  label: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    color: colors.navGray,
  },
  value: {
    fontFamily: fonts.dmSansMedium,
    fontSize: 14,
    color: colors.black,
    textAlign: "right",
  },
  placeholder: {
    color: colors.black,
  },
});
