import AsyncStorage from "@react-native-async-storage/async-storage";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./Request";

const request = new RequestHandler();

// Function to get auth token from AsyncStorage
export async function getAuthToken(): Promise<string> {
  try {
    const cookiesString = await AsyncStorage.getItem("cookies##$$");
    if (cookiesString) {
      const cookies = JSON.parse(cookiesString);
      if (
        (cookies && cookies.data && cookies.data.token) ||
        (cookies && cookies.token)
      ) {
        return cookies.data ? cookies.data.token : cookies.token;
      }
    }
    return "";
  } catch (error) {
    console.error("Error getting auth token:", error);
    return "";
  }
}
export async function GetUserProfile(): Promise<any> {
  const token = await getAuthToken();
  console.log(token);
  return request.get("user/profile", token);
}
export async function UpdateUserDetails(body: object): Promise<any> {
  const token = await getAuthToken();
  return request.post("user/update-user-details", body, token);
}
export async function UpdateMedicalHistory(body: object): Promise<any> {
  const token = await getAuthToken();

  return request.post("user/update-medical-history", body, token);
}
export async function GetMedicalHistory(): Promise<any> {
  const token = await getAuthToken();
  return request.get("user/medical-history", token);
}
export async function GetTestReport(): Promise<any> {
  const token = await getAuthToken();
  return request.get("user/test-reports?page=1&limit=100", token);
}
export async function AddTestReport(body: object): Promise<any> {
  const token = await getAuthToken();
  return request.post("user/test-report", body, token);
}
export async function UpdateTestReport(id: string, body: object): Promise<any> {
  const token = await getAuthToken();
  return request.patch(`user/test-report/${id}`, body, token);
}
export async function DeleteTestReport(id: string): Promise<any> {
  const token = await getAuthToken();
  return request.delete(`user/test-report/${id}`, {}, token);
}
export async function AddReminder(body: object): Promise<any> {
  const token = await getAuthToken();
  return request.post("user/reminder", body, token);
}
export async function GetAllReminder(): Promise<any> {
  const token = await getAuthToken();
  return request.get("user/reminder?page=1&limit=100", token);
}
export async function DeleteReminder(id: string): Promise<any> {
  const token = await getAuthToken();
  return request.delete(`user/reminder/${id}`, {}, token);
}
export async function UpdateProssionalDetails(body: object): Promise<any> {
  const token = await getAuthToken();
  return request.post(`user/update-profession-detail`, body, token);
}
export async function UpdateUserIdentity(body: object): Promise<any> {
  const token = await getAuthToken();
  return request.post(`user/update-identity`, body, token);
}

export async function UpdateLanguage(body: object): Promise<any> {
  const token = await getAuthToken();
  return request.post(`user/update-language`, body, token);
}
export async function GetProssionalDetails(): Promise<any> {
  const token = await getAuthToken();
  return request.get("user/professional-details", token);
}
export async function GetPractIdentity(): Promise<any> {
  const token = await getAuthToken();
  return request.get("user/user-identity", token);
}
