import React, { useState } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { colors } from "../../../Config/colors";
import { fonts } from "../../../Config/Fonts";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import Button from "../../../Components/Button";
import AppHeader from "../../../Components/AppHeader";
import Input from "../../../Components/Input";
import DateInput from "../../../Components/DateInput";
import DropdownInput from "../../../Components/DropdownInput";
import { useToast } from "../../../Context/ToastContext";
import * as yup from "yup";
import { Formik } from "formik";

// Gender options for dropdown
const genderOptions = [
  { label: "Male", value: "Male" },
  { label: "Female", value: "Female" },
  { label: "Other", value: "Other" },
  { label: "Prefer not to say", value: "Prefer not to say" },
];

export default function EditField({ navigation, route }) {
  const { field, title, currentValue, onSave } = route.params;
  const [loading, setLoading] = useState(false);
  const { handleToast } = useToast();

  // Create validation schema based on field type
  const getValidationSchema = () => {
    const baseSchema = {
      value: yup.string().required(`${title} is required`),
    };

    // Add specific validation rules based on field type
    if (field === "name") {
      baseSchema.value = yup
        .string()
        .required("Name is required")
        .min(2, "Name must be at least 2 characters");
    } else if (field === "mobile") {
      baseSchema.value = yup
        .string()
        .required("Mobile number is required")
        .matches(/^[0-9+\s-]{10,15}$/, "Please enter a valid mobile number");
    } else if (field === "dob") {
      baseSchema.value = yup
        .string()
        .required("Date of birth is required")
        .matches(
          /^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$/,
          "Date must be in YYYY-MM-DD format"
        );
    } else if (field === "emergencyContact") {
      baseSchema.value = yup
        .string()
        .required("Emergency contact is required")
        .matches(/^[0-9+\s-]{10,15}$/, "Please enter a valid phone number");
    }

    return yup.object().shape(baseSchema);
  };

  const handleSave = (values) => {
    setLoading(true);

    // Simulate API call
    setTimeout(() => {
      if (onSave) {
        onSave(values.value);
      }
      handleToast(`${title} updated successfully`, "success");
      setLoading(false);
      navigation.goBack();
    }, 1000);
  };

  // Render the appropriate input component based on field type
  const renderInputComponent = (formikProps) => {
    if (field === "dob") {
      return (
        <DateInput
          label={title}
          placeholder="YYYY-MM-DD"
          value={formikProps.values.value}
          onSelect={(date) => formikProps.setFieldValue("value", date)}
          error={formikProps.errors.value && formikProps.touched.value}
          errorText={formikProps.errors.value}
        />
      );
    } else if (field === "gender") {
      return (
        <DropdownInput
          label={title}
          placeholder="Select Gender"
          value={formikProps.values.value}
          options={genderOptions}
          onSelect={(gender) => formikProps.setFieldValue("value", gender)}
          error={formikProps.errors.value && formikProps.touched.value}
          errorText={formikProps.errors.value}
        />
      );
    } else {
      return (
        <Input
          label={title}
          placeholder={`Enter your ${title.toLowerCase()}`}
          value={formikProps.values.value}
          onChangeText={formikProps.handleChange("value")}
          onBlur={formikProps.handleBlur("value")}
          error={formikProps.errors.value && formikProps.touched.value}
          errorText={formikProps.errors.value}
          keyboardType={
            field === "mobile" || field === "emergencyContact"
              ? "phone-pad"
              : "default"
          }
        />
      );
    }
  };

  return (
    <View style={styles.container}>
      <Div>
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={{ flex: 1 }}
        >
          <Formik
            initialValues={{ value: currentValue || "" }}
            validationSchema={getValidationSchema()}
            onSubmit={handleSave}
          >
            {(formikProps) => (
              <View style={styles.content}>
                {/* Header */}
                <View style={styles.headerContainer}>
                  <AppHeader
                    navigation={navigation}
                    showBackButton={true}
                    title={"Edit Profile"}
                    navStyle={{
                      justifyContent: "flex-start",
                      alignItems: "flex-start",
                    }}
                  />
                </View>

                <ScrollView
                  style={styles.scrollView}
                  showsVerticalScrollIndicator={false}
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={styles.scrollContent}
                >
                  <H4 style={styles.title}>Update {title}</H4>

                  {/* Input Field */}
                  <View style={styles.inputContainer}>
                    {renderInputComponent(formikProps)}
                  </View>
                </ScrollView>

                {/* Save Button */}
                <View style={styles.buttonContainer}>
                  <Button
                    btnText="Save changes"
                    onPress={formikProps.handleSubmit}
                    loading={loading}
                    style={styles.saveButton}
                  />
                </View>
              </View>
            )}
          </Formik>
        </KeyboardAvoidingView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    flex: 1,
    width: "100%",
  },
  headerContainer: {
    width: "100%",
    paddingHorizontal: 20,
  },
  scrollView: {
    flex: 1,
    width: "100%",
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  title: {
    fontSize: 24,
    fontFamily: fonts.dmSansBold,
    color: colors.black,
    marginTop: 20,
    marginBottom: 24,
  },
  inputContainer: {
    width: "100%",
    marginBottom: 20,
  },
  buttonContainer: {
    position: "absolute",
    bottom: 20,
    left: 0,
    right: 0,
    padding: 20,
    backgroundColor: colors.white,
  },
  saveButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
  },
});
