import AsyncStorage from "@react-native-async-storage/async-storage";
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import { useContext } from "react";
import { CredentailsContext } from "../Context/CredentailsContext";

export const clearLogin = async ({ setStoredCredentails }) => {
  await GoogleSignin.signOut();
  AsyncStorage.removeItem("cookies##$$")
    .then(() => {
      // @ts-ignore
      setStoredCredentails(null);
    })
    .catch((error) => {});
};
