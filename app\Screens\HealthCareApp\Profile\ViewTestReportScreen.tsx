import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Linking,
} from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import AppHeader from "../../../Components/AppHeader";
import Input from "../../../Components/Input";

import * as DocumentPicker from "expo-document-picker";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../Config/Svg";
import * as yup from "yup";
import { Formik } from "formik";
import { useToast } from "../../../Context/ToastContext";
import { uploadDocumentToCloudinary } from "../../../Services/CloudinaryService";
import { UpdateTestReport } from "../../../RequestHandler.tsx/User";
import NetworkErrorView from "../../../Components/NetworkErrorView";
import {
  getErrorMessage,
  getErrorType,
} from "../../../utils/networkErrorHandler";

interface TestReport {
  id: string;
  name: string;
  doctorName: string;
  reportDate: string;
  reportFile: string;
  active: boolean;
}

export default function ViewTestReportScreen({ navigation, route }) {
  const { report }: { report: TestReport } = route.params;
  const [loading, setLoading] = useState(false);
  const [uploadingDocument, setUploadingDocument] = useState(false);
  const { handleToast } = useToast();
  const [documentUploaded, setDocumentUploaded] = useState(true); // Initially true since report exists
  const [documentInfo, setDocumentInfo] = useState<{
    name: string;
    uri: string;
    type: string;
    size: number;
  } | null>({
    name: "Current Report",
    uri: report.reportFile,
    type: "application/pdf",
    size: 0,
  });
  const [cloudinaryUrl, setCloudinaryUrl] = useState(report.reportFile);
  const [networkError, setNetworkError] = useState<any>(null);
  const [showNetworkError, setShowNetworkError] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  // Parse the existing report date and time
  const parseReportDateTime = (reportDate: string) => {
    try {
      const date = new Date(reportDate);
      const formattedDate = date.toISOString().split("T")[0]; // YYYY-MM-DD
      const formattedTime = date.toTimeString().split(" ")[0].substring(0, 5); // HH:MM
      return { date: formattedDate, time: formattedTime };
    } catch (error) {
      console.error("Error parsing report date:", error);
      return { date: "", time: "" };
    }
  };

  const { date: initialDate, time: initialTime } = parseReportDateTime(
    report.reportDate
  );

  // Function to handle network errors
  const handleNetworkError = (error: any) => {
    setNetworkError(error);
    setShowNetworkError(true);
  };

  // Function to retry after network error
  const handleRetry = () => {
    setShowNetworkError(false);
    setNetworkError(null);
  };

  // Validation schema for test report form
  const testReportSchema = yup.object().shape({
    reportName: yup
      .string()
      .required("Report name is required")
      .min(3, "Report name must be at least 3 characters"),
    doctorName: yup
      .string()
      .required("Doctor name is required")
      .min(3, "Doctor name must be at least 3 characters"),
    date: yup
      .string()
      .required("Date is required")
      .matches(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format"),
    time: yup
      .string()
      .required("Time is required")
      .matches(
        /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
        "Time must be in HH:MM format"
      ),
    documentUploaded: yup
      .boolean()
      .oneOf([true], "Please upload your test report")
      .required("Please upload your test report"),
  });

  // Function to handle document upload
  const handleUploadDocument = async (
    setFieldValue: (field: string, value: any) => void
  ) => {
    try {
      // Open document picker for selecting files
      const result = await DocumentPicker.getDocumentAsync({
        type: [
          "application/pdf",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "image/jpeg",
          "image/png",
        ],
        copyToCacheDirectory: true,
        multiple: false,
      });

      // Check if the user selected a document
      if (
        result.canceled === false &&
        result.assets &&
        result.assets.length > 0
      ) {
        const selectedFile = result.assets[0];

        // Check file size (limit to 100MB)
        const fileSize = selectedFile.size || 0;
        const maxSize = 100 * 1024 * 1024; // 100MB in bytes

        if (fileSize > maxSize) {
          handleToast("File size exceeds 100MB limit", "error");
          return;
        }

        // Store document info and update state
        setDocumentInfo({
          name: selectedFile.name,
          uri: selectedFile.uri,
          type: selectedFile.mimeType || "application/octet-stream",
          size: fileSize,
        });

        // Upload to Cloudinary
        try {
          setUploadingDocument(true);
          // Upload the document to Cloudinary
          const cloudinaryDocUrl = await uploadDocumentToCloudinary(
            selectedFile.uri,
            selectedFile.mimeType || "application/octet-stream"
          );

          // Store the Cloudinary URL
          setCloudinaryUrl(cloudinaryDocUrl);
          setUploadingDocument(false);
          handleToast("Document uploaded to cloud successfully", "success");

          // Update Formik state
          setFieldValue("documentUploaded", true);
          setDocumentUploaded(true);
        } catch (uploadError) {
          console.error("Error uploading to Cloudinary:", uploadError);
          setUploadingDocument(false);
          // Check if it's a network error
          if (
            getErrorType(uploadError) === "network" ||
            getErrorType(uploadError) === "timeout"
          ) {
            handleNetworkError(uploadError);
          } else {
            handleToast(
              "Failed to upload document to cloud. Please try again.",
              "error"
            );
          }
        }
      }
    } catch (error) {
      console.error("Error picking document:", error);
      handleToast("Failed to upload document. Please try again.", "error");
    }
  };

  // Function to view the current report
  const viewCurrentReport = () => {
    if (report.reportFile) {
      Alert.alert(
        "View Report",
        "Would you like to open this report in your browser?",
        [
          {
            text: "Cancel",
            style: "cancel",
          },
          {
            text: "Open",
            onPress: () => {
              try {
                Linking.openURL(report.reportFile);
                handleToast("Opening report in browser", "success");
              } catch (error) {
                console.error("Error opening URL:", error);
                handleToast("Failed to open report", "error");
              }
            },
          },
        ]
      );
    } else {
      handleToast("Report file not available", "error");
    }
  };

  // Function to handle form submission
  const handleUpdateReport = async (values: {
    reportName: string;
    doctorName: string;
    date: string;
    time: string;
    documentUploaded: boolean;
  }) => {
    try {
      setLoading(true);

      // Combine date and time for the reportDate field
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      const timeRegex = /^\d{2}:\d{2}$/;

      // Validate date and time format
      if (!dateRegex.test(values.date)) {
        handleToast("Date must be in YYYY-MM-DD format", "error");
        setLoading(false);
        return;
      }

      if (!timeRegex.test(values.time)) {
        handleToast("Time must be in HH:MM format", "error");
        setLoading(false);
        return;
      }

      // Create a valid ISO date string
      const reportDate = `${values.date}T${values.time}:00.000Z`;

      // Create test report object for API
      const requestBody = {
        name: values.reportName,
        doctorName: values.doctorName,
        reportDate: reportDate,
        reportFile: cloudinaryUrl,
      };

      console.log("Updating test report:", requestBody);
      const response = await UpdateTestReport(report.id, requestBody);
      console.log("Update test report response:", response);

      handleToast("Test report updated successfully", "success");
      setIsEditing(false);

      // Navigate back to test reports list
      navigation.goBack();
    } catch (error) {
      console.error("Error updating test report:", error);
      // Check if it's a network error
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        handleNetworkError(error);
      } else {
        handleToast("Failed to update test report. Please try again.", "error");
      }
    } finally {
      setLoading(false);
    }
  };

  // Format date for display
  const formatDateTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString() + " " + date.toLocaleTimeString();
    } catch (error) {
      return dateString;
    }
  };

  // If there's a network error, show the NetworkErrorView
  if (showNetworkError) {
    return (
      <NetworkErrorView
        errorType={
          getErrorType(networkError) as
            | "network"
            | "timeout"
            | "server"
            | "generic"
        }
        message={getErrorMessage(networkError)}
        onRetry={handleRetry}
      />
    );
  }

  return (
    <View style={styles.mainContainer}>
      <Div>
        <View style={styles.container}>
          <ScrollView
            style={{ width: "100%" }}
            automaticallyAdjustKeyboardInsets={true}
            contentContainerStyle={{
              alignItems: "center",
              paddingBottom: 100,
            }}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
          >
            {/* Header */}
            <View style={{ width: "100%" }}>
              <AppHeader navigation={navigation} />
            </View>

            {/* Title */}
            <H4 style={styles.title}>
              {isEditing ? "Edit Test Report" : "View Test Report"}
            </H4>

            {/* View Mode */}
            {!isEditing ? (
              <View style={styles.viewContainer}>
                <View style={styles.reportCard}>
                  <H4 style={styles.reportTitle}>{report.name}</H4>

                  <View style={styles.detailRow}>
                    <P style={styles.detailLabel}>Doctor Name:</P>
                    <P style={styles.detailValue}>{report.doctorName}</P>
                  </View>

                  <View style={styles.detailRow}>
                    <P style={styles.detailLabel}>Report Date:</P>
                    <P style={styles.detailValue}>
                      {formatDateTime(report.reportDate)}
                    </P>
                  </View>

                  <View style={styles.detailRow}>
                    <P style={styles.detailLabel}>Report File:</P>
                    <TouchableOpacity onPress={viewCurrentReport}>
                      <P style={styles.linkText}>View Document</P>
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={styles.actionButtons}>
                  <View style={{ width: "45%" }}>
                    <Button
                      btnText="Edit Report"
                      onPress={() => setIsEditing(true)}
                    />
                  </View>
                  <View style={{ width: "45%" }}>
                    <Button
                      btnText="View Document"
                      onPress={viewCurrentReport}
                    />
                  </View>
                </View>
              </View>
            ) : (
              /* Edit Mode */
              <Formik
                initialValues={{
                  reportName: report.name,
                  doctorName: report.doctorName,
                  date: initialDate,
                  time: initialTime,
                  documentUploaded: true,
                }}
                validationSchema={testReportSchema}
                onSubmit={(values) => {
                  handleUpdateReport(values);
                }}
              >
                {(formikProps) => (
                  <View style={styles.formContainer}>
                    {/* Report Name */}
                    <Input
                      label="Report Name"
                      placeholder="Enter report name"
                      value={formikProps.values.reportName}
                      onChangeText={formikProps.handleChange("reportName")}
                      onBlur={formikProps.handleBlur("reportName")}
                      error={
                        formikProps.errors.reportName &&
                        formikProps.touched.reportName
                      }
                      errorText={formikProps.errors.reportName}
                    />

                    {/* Doctor Name */}
                    <Input
                      contStyle={{ marginTop: 16 }}
                      label="Doctor Name"
                      placeholder="Enter doctor name"
                      value={formikProps.values.doctorName}
                      onChangeText={formikProps.handleChange("doctorName")}
                      onBlur={formikProps.handleBlur("doctorName")}
                      error={
                        formikProps.errors.doctorName &&
                        formikProps.touched.doctorName
                      }
                      errorText={formikProps.errors.doctorName}
                    />

                    {/* Date and Time */}
                    <View style={styles.dateTimeContainer}>
                      <View style={styles.dateContainer}>
                        <Input
                          label="Date"
                          placeholder="YYYY-MM-DD"
                          value={formikProps.values.date}
                          onChangeText={(text) => {
                            // Format date input with hyphens (YYYY-MM-DD)
                            const cleaned = text.replace(/[^0-9]/g, "");
                            let formatted = cleaned;

                            if (cleaned.length > 4) {
                              formatted = `${cleaned.slice(
                                0,
                                4
                              )}-${cleaned.slice(4)}`;
                            }
                            if (cleaned.length > 6) {
                              formatted = `${formatted.slice(
                                0,
                                7
                              )}-${formatted.slice(7, 9)}`;
                            }
                            // Limit to 10 characters (YYYY-MM-DD)
                            formatted = formatted.slice(0, 10);
                            formikProps.setFieldValue("date", formatted);
                          }}
                          onBlur={formikProps.handleBlur("date")}
                          error={
                            formikProps.errors.date && formikProps.touched.date
                          }
                          errorText={formikProps.errors.date}
                          keyboardType="numeric"
                        />
                      </View>
                      <View style={styles.timeContainer}>
                        <Input
                          label="Time"
                          placeholder="HH:MM"
                          value={formikProps.values.time}
                          onChangeText={(text) => {
                            // Format time input with colon (HH:MM)
                            const cleaned = text.replace(/[^0-9]/g, "");
                            let formatted = cleaned;

                            if (cleaned.length > 2) {
                              formatted = `${cleaned.slice(
                                0,
                                2
                              )}:${cleaned.slice(2)}`;
                            }

                            // Limit to 5 characters (HH:MM)
                            formatted = formatted.slice(0, 5);

                            formikProps.setFieldValue("time", formatted);
                          }}
                          onBlur={formikProps.handleBlur("time")}
                          error={
                            formikProps.errors.time && formikProps.touched.time
                          }
                          errorText={formikProps.errors.time}
                          keyboardType="numeric"
                        />
                      </View>
                    </View>

                    {/* Upload Documentation */}
                    <View style={styles.uploadSection}>
                      <P style={styles.uploadLabel}>Update Report Document</P>
                      <TouchableOpacity
                        style={[
                          styles.uploadContainer,
                          documentUploaded && styles.uploadedContainer,
                          formikProps.errors.documentUploaded &&
                            formikProps.touched.documentUploaded &&
                            styles.uploadError,
                        ]}
                        onPress={() =>
                          handleUploadDocument(formikProps.setFieldValue)
                        }
                        onBlur={() =>
                          formikProps.setFieldTouched("documentUploaded")
                        }
                      >
                        {uploadingDocument ? (
                          <>
                            <ActivityIndicator
                              size="large"
                              color={colors.primary}
                            />
                            <P style={styles.uploadText}>Uploading...</P>
                          </>
                        ) : (
                          <>
                            <SvgXml
                              xml={
                                svg.upload ||
                                '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="#F0A500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M17 8L12 3L7 8" stroke="#F0A500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 3V15" stroke="#F0A500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
                              }
                              width={24}
                              height={24}
                            />
                            <P style={styles.uploadText}>
                              {documentUploaded
                                ? "Document Ready - Tap to Change"
                                : "Drag & Drop your Test Report here or Browse Files"}
                            </P>
                            {documentUploaded && documentInfo && (
                              <P
                                style={[
                                  styles.uploadSubtext,
                                  { color: colors.primary },
                                ]}
                              >
                                {documentInfo.name}
                              </P>
                            )}
                            <P style={styles.uploadSubtext}>
                              Upload lab results, medical reports, or scans
                              (PDF, DOC, DOCX below 100MB)
                            </P>
                          </>
                        )}
                      </TouchableOpacity>
                    </View>

                    {/* Action Buttons */}
                    <View style={styles.editActionButtons}>
                      <Button
                        btnText="Update Report"
                        onPress={formikProps.handleSubmit}
                        loading={loading}
                      />
                    </View>
                  </View>
                )}
              </Formik>
            )}
          </ScrollView>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "90%",
    height: "100%",
    backgroundColor: colors.white,
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontFamily: fonts.dmSansBold,
    marginTop: 16,
    marginBottom: 24,
    alignSelf: "flex-start",
  },
  viewContainer: {
    width: "100%",
    flex: 1,
  },
  reportCard: {
    width: "100%",
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 20,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: colors.stroke,
  },
  reportTitle: {
    fontSize: 20,
    fontFamily: fonts.dmSansBold,
    color: colors.black,
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 12,
    flexWrap: "wrap",
  },
  detailLabel: {
    fontSize: 14,
    fontFamily: fonts.dmSansMedium,
    color: colors.navGray,
    flex: 1,
  },
  detailValue: {
    fontSize: 14,
    fontFamily: fonts.dmSansRegular,
    color: colors.black,
    flex: 2,
    textAlign: "right",
  },
  linkText: {
    fontSize: 14,
    fontFamily: fonts.dmSansMedium,
    color: colors.primary,
    textDecorationLine: "underline",
  },
  actionButtons: {
    flexDirection: "row",
    justifyContent: "space-around",
    width: "100%",
    marginTop: 20,
  },
  editButton: {
    backgroundColor: colors.primary,
  },
  viewButton: {
    backgroundColor: colors.primary,
  },
  formContainer: {
    width: "100%",
    marginTop: 10,
  },
  dateTimeContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 16,
  },
  dateContainer: {
    flex: 1,
    marginRight: 8,
  },
  timeContainer: {
    flex: 1,
    marginLeft: 8,
  },
  uploadSection: {
    width: "100%",
    marginTop: 32,
  },
  uploadLabel: {
    fontFamily: fonts.dmSansMedium,
    fontSize: 14,
    color: colors.black,
    marginBottom: 8,
  },
  uploadContainer: {
    width: "100%",
    height: 120,
    borderWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.white,
    paddingHorizontal: 16,
  },
  uploadedContainer: {
    borderColor: colors.primary,
    backgroundColor: colors.primaryLight_100,
  },
  uploadError: {
    borderColor: colors.error || "red",
  },
  uploadText: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    color: colors.black,
    textAlign: "center",
    marginTop: 8,
  },
  uploadSubtext: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    color: colors.navGray,
    textAlign: "center",
    marginTop: 4,
  },
  editActionButtons: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
    marginTop: 32,
  },
  cancelButton: {
    backgroundColor: colors.navGray,
  },
  updateButton: {
    backgroundColor: colors.primary,
  },
});
