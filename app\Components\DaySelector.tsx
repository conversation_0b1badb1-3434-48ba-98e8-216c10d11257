import React from 'react';
import { View, StyleSheet, TouchableOpacity, Text } from 'react-native';
import { colors } from '../Config/colors';
import { fonts } from '../Config/Fonts';
import P from './P';

interface DaySelectorProps {
  selectedDays: string[];
  onDaySelect: (days: string[]) => void;
  label?: string;
  contStyle?: object;
}

const DaySelector: React.FC<DaySelectorProps> = ({
  selectedDays,
  onDaySelect,
  label,
  contStyle,
}) => {
  // Days of the week
  const daysOfWeek = [
    { key: 'mon', label: 'M' },
    { key: 'tue', label: 'T' },
    { key: 'wed', label: 'W' },
    { key: 'thu', label: 'T' },
    { key: 'fri', label: 'F' },
    { key: 'sat', label: 'S' },
    { key: 'sun', label: 'S' },
  ];

  const toggleDay = (day: string) => {
    if (selectedDays.includes(day)) {
      // Remove day if already selected
      onDaySelect(selectedDays.filter(d => d !== day));
    } else {
      // Add day if not selected
      onDaySelect([...selectedDays, day]);
    }
  };

  return (
    <View style={[styles.container, contStyle]}>
      {label && <P style={styles.label}>{label}</P>}
      <View style={styles.daysContainer}>
        {daysOfWeek.map((day, index) => (
          <TouchableOpacity
            key={day.key}
            style={[
              styles.dayButton,
              selectedDays.includes(day.key) && styles.selectedDayButton,
              // Add different styling for the second Tuesday (index 1)
              day.key === 'tue' && index === 1 && styles.tuesdayButton,
              // Add different styling for the second Sunday (index 6)
              day.key === 'sun' && index === 6 && styles.sundayButton,
            ]}
            onPress={() => toggleDay(day.key)}
          >
            <Text
              style={[
                styles.dayText,
                selectedDays.includes(day.key) && styles.selectedDayText,
              ]}
            >
              {day.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontFamily: fonts.dmSansMedium,
    color: colors.black,
    marginBottom: 8,
  },
  daysContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dayButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.stroke,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  selectedDayButton: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  tuesdayButton: {
    // Special styling for Tuesday if needed
  },
  sundayButton: {
    // Special styling for Sunday if needed
  },
  dayText: {
    fontSize: 14,
    fontFamily: fonts.dmSansMedium,
    color: colors.black,
  },
  selectedDayText: {
    color: colors.white,
  },
});

export default DaySelector;
