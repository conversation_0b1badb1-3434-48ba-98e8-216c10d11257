import React, { CSSProperties, useState } from "react";
import {
  Dimensions,
  Pressable,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
  Text,
} from "react-native";
import P from "./P"; // Ensure P is correctly implemented or replace with a Text component
import { fonts } from "../Config/Fonts";
import { colors } from "../Config/colors";
import { SvgXml } from "react-native-svg";
import { svg } from "../Config/Svg";

interface PProps {
  label?: any;
  error?: boolean | any; // Changed from just boolean to accept any type
  value?: string;
  onChangeText?: (text: string) => void;
  onBlur?: (e) => void;
  placeholder?: string;
  secureTextEntry?: boolean;
  type?: "password" | "phone";
  contStyle?: CSSProperties;
  inputStyle?: CSSProperties;
  leftIcon?: any;
  rightIcon?: any;
  editable?: boolean;
  keyboardType?: any;
  customInputStyle?: CSSProperties;
  labelStyle?: CSSProperties;
  numberOfLines?: number;
  onTogglePasswordVisibility?: () => void;
  onPress?: () => void;
  autoCapitalize?: string;
  maxLenght?: number;
  defaultCountryCode?: string;
  errorText?: any;
  onDefualtCodePress?: () => void;
  multiline?: boolean;
  textAlignVertical?: "auto" | "center" | "bottom" | "top";
}

const baseHeight = 800;
const baseWidth = 360;
const { width, height } = Dimensions.get("window");
export default function Input({
  label,
  error,
  value,
  onBlur,
  onChangeText,
  placeholder,
  secureTextEntry = false,
  type,
  contStyle,
  inputStyle,
  leftIcon,
  rightIcon,
  editable,
  keyboardType,
  customInputStyle,
  numberOfLines,
  onTogglePasswordVisibility,
  onPress,
  labelStyle,
  autoCapitalize,
  maxLenght,
  defaultCountryCode,
  errorText,
  onDefualtCodePress,
  multiline,
  textAlignVertical,
}: PProps) {
  const [focus, setFocus] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);

  const togglePasswordVisibility = () => {
    if (onTogglePasswordVisibility) {
      // If the onTogglePasswordVisibility prop is provided, call it
      onTogglePasswordVisibility();
    } else {
      // Otherwise, toggle the password visibility as usual
      setIsPasswordVisible(!isPasswordVisible);
    }
  };

  return (
    // @ts-ignore
    <View style={[styles.inputCont, contStyle]} onPress={onPress}>
      {/* @ts-ignore */}
      {label && <P style={[styles.label, labelStyle]}>{label}</P>}
      <View
        style={[
          styles.customInput,
          {
            borderColor: error
              ? colors.red
              : focus
              ? colors.primary
              : colors.stroke,
          },
          // @ts-ignore
          customInputStyle,
        ]}
      >
        {type === "phone" ? (
          <>
            <TouchableOpacity
              style={styles.countryCodeContainer}
              onPress={onDefualtCodePress}
            >
              <Text style={styles.countryCodeText}>{defaultCountryCode}</Text>
            </TouchableOpacity>
            <View style={styles.divider} />

            {/* Phone number input */}
            <TextInput
              style={[styles.input, { width: "80%" }]}
              value={value}
              onChangeText={onChangeText}
              placeholder={placeholder}
              keyboardType="phone-pad"
              onFocus={() => setFocus(true)}
              onBlur={(e) => {
                setFocus(false);

                if (onBlur) onBlur(e);
              }}
              placeholderTextColor={colors.gray1}
              cursorColor={colors.black}
            />
          </>
        ) : (
          <>
            {leftIcon}
            <TextInput
              value={value}
              numberOfLines={numberOfLines}
              onBlur={(e) => {
                setFocus(false);

                if (onBlur) onBlur(e);
              }}
              onChangeText={onChangeText}
              secureTextEntry={type === "password" && !isPasswordVisible}
              onFocus={() => setFocus(true)}
              placeholder={placeholder}
              placeholderTextColor={colors.gray1}
              cursorColor={colors.black}
              editable={editable}
              keyboardType={keyboardType}
              autoCapitalize="none"
              maxLength={maxLenght}
              multiline={multiline}
              textAlignVertical={textAlignVertical}
              style={[
                styles.input,
                {
                  width: type === "password" ? "85%" : "100%",
                  pointerEvents: editable === false ? "none" : "auto",
                },
                // @ts-ignore
                inputStyle,
                {
                  width:
                    rightIcon && leftIcon
                      ? "75%"
                      : leftIcon || rightIcon
                      ? "88%"
                      : "95%",
                },
              ]}
            />
            {rightIcon}
            {type === "password" && (
              <TouchableOpacity
                onPress={togglePasswordVisibility}
                style={{
                  right: 12,
                  position: "absolute",
                  width: 20,
                  height: 20,
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <SvgXml
                  xml={isPasswordVisible ? svg.eye : svg.eyeOff}
                  style={{}}
                  pointerEvents="none"
                />
              </TouchableOpacity>
            )}
          </>
        )}
      </View>
      {error && <P style={styles.errorText}>{errorText}</P>}
    </View>
  );
}

const styles = StyleSheet.create({
  inputCont: {
    width: "100%",
  },
  label: {
    fontSize: 14,
    fontFamily: fonts.dmSansRegular,
    lineHeight: 14,
    marginBottom: 6,
  },
  customInput: {
    width: "100%",
    borderWidth: 1,
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "center",
    borderColor: colors.stroke,
    backgroundColor: colors.white,
  },
  input: {
    height: 59,
    paddingLeft: (14 / baseWidth) * width,
    fontSize: 14,
    fontFamily: fonts.dmSansRegular,
    alignItems: "center",
    color: colors.black,
  },
  countryCodeContainer: {
    paddingHorizontal: 12,
    justifyContent: "center",
  },
  countryCodeText: {
    fontSize: 14,
    color: colors.black,
    fontFamily: fonts.dmSansRegular,
  },
  divider: {
    width: 1,
    height: "50%",
    backgroundColor: "#D8DADC",
  },
  errorText: {
    fontSize: 14,
    color: colors.red,
    fontFamily: fonts.dmSansRegular,
    marginTop: 4,
  },
});
