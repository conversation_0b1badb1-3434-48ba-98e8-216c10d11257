<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>211734170318-fj0eagc400tgcjcq3ucilvt9l02d5156.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.211734170318-fj0eagc400tgcjcq3ucilvt9l02d5156</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>211734170318-co83m3v6m7chjs2ph3mpcg0pepf2acsn.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyCymjfC_ClFdtcWyRvbdPvyYdnZmeGXhMA</string>
	<key>GCM_SENDER_ID</key>
	<string>211734170318</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.health.OnlyMed</string>
	<key>PROJECT_ID</key>
	<string>only-med</string>
	<key>STORAGE_BUCKET</key>
	<string>only-med.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:211734170318:ios:56524abbca3c4c89e5de9f</string>
</dict>
</plist>