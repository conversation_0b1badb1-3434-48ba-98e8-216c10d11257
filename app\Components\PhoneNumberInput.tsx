import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from "react-native";

interface Props {
  value: string;
  onChange: (text: string) => void;
  isPhone: boolean;
}

const PhoneNumberInput: React.FC<Props> = ({ value, onChange, isPhone }) => {
  return (
    <View style={styles.container}>
      <View style={styles.inputContainer}>
        {isPhone && (
          <>
            <Text style={styles.countryCode}>+234</Text>
            <View
              style={{
                paddingVertical: 0,
                paddingRight: 3,
                borderRightWidth: 1,
                borderColor: "grey",
                height: 18,
              }}
            />
          </>
        )}
        <TextInput
          style={[styles.input, isPhone && styles.phoneInput]}
          placeholder={isPhone ? "0 00 00 00 00" : "<EMAIL>"}
          keyboardType={isPhone ? "phone-pad" : "email-address"}
          value={value}
          onChangeText={onChange}
          autoCapitalize="none"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
  },
  toggleContainer: {
    flexDirection: "row",
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 10,
    overflow: "hidden",
    marginBottom: 10,
  },
  toggleButton: {
    flex: 1,
    padding: 12,
    alignItems: "center",
  },
  activeTab: {
    backgroundColor: "#007BFF",
  },
  activeText: {
    color: "#fff",
    fontWeight: "bold",
  },
  inactiveText: {
    color: "#333",
  },
  label: {
    fontSize: 14,
    color: "#333",
    marginBottom: 5,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 8,
    paddingHorizontal: 10,
    height: 50,
  },
  countryCode: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#333",
    marginRight: 8,
  },
  input: {
    flex: 1,
    paddingLeft: 8,
    fontSize: 14,
    color: "#333",
  },
  phoneInput: {
    letterSpacing: 2, // Format phone input spacing
  },
});

export default PhoneNumberInput;
