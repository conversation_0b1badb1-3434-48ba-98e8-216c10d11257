import React, { useState } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
  ScrollView,
} from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../Config/Svg";
import AppHeader from "../../../Components/AppHeader";

const { width, height } = Dimensions.get("window");

export default function ForgottenPasswordScreen({ navigation }) {
  const [selectedMethod, setSelectedMethod] = useState("email"); // Default to email selected
  const [activeTab, setActiveTab] = useState<"Email" | "Phone">("Email");
  // Sample masked data - in a real app, this would come from your user data
  const maskedPhone = "+234 808 7****249";
  const maskedEmail = "henr****@gmail.com";

  const handleContinue = () => {
    // Navigate to the verification screen
    navigation.navigate("ForgottenPassWordScreen2",
       { 
      method: selectedMethod,
      activeTab: activeTab 
    });
  };

  return (
    <View style={styles.mainContainer}>
      <Div>
        <View style={styles.container}>
          <ScrollView
            style={{ width: "100%" }}
            automaticallyAdjustContentInsets={true}
            contentContainerStyle={{ alignItems: "center", minHeight: "100%" }}
            showsVerticalScrollIndicator={false}
          >
            {/* Back Button */}
            <View style={{ width: "100%" }}>
              <AppHeader navigation={navigation} />
            </View>

            {/* Title Section */}
            <View style={styles.titleContainer}>
              <H4 style={styles.title}>Forgotten Password</H4>
              <P style={styles.subtitle}>Select contact for verification</P>
            </View>

            {/* Contact Options */}
            <View style={styles.optionsContainer}>
              {/* Phone Option */}
              <TouchableOpacity
                style={[
                  styles.optionCard,
                  selectedMethod === "phone" && styles.selectedCard,
                ]}
                onPress={() => {
                  setSelectedMethod("phone");
                  setActiveTab("Phone");
                }}
              >
                <View
                  style={[
                    styles.iconContainer,
                    selectedMethod === "phone" && styles.selectedIconContainer,
                  ]}
                >
                  <SvgXml xml={svg.chat} width={24} height={24} />
                </View>
                <View style={styles.optionTextContainer}>
                  <P style={styles.optionTitle}>Via Phone</P>
                  <P style={styles.optionValue}>
                    Verify with your phone number
                  </P>
                </View>
              </TouchableOpacity>

              {/* Email Option */}
              <TouchableOpacity
                style={[
                  styles.optionCard,
                  selectedMethod === "email" && styles.selectedCard,
                ]}
                onPress={() => {
                  setSelectedMethod("email");
                  setActiveTab("Email");
                }}
              >
                <View
                  style={[
                    styles.iconContainer,
                    selectedMethod === "email" && styles.selectedIconContainer,
                  ]}
                >
                  <SvgXml xml={svg.mail} width={24} height={24} />
                </View>
                <View style={styles.optionTextContainer}>
                  <P style={styles.optionTitle}>Via Email</P>
                  <P style={styles.optionValue}>
                    Verify with your email address
                  </P>
                </View>
              </TouchableOpacity>
            </View>

            {/* Continue Button */}
            <View style={styles.buttonContainer}>
              <Button
                btnText="Continue"
                onPress={handleContinue}
                style={styles.continueButton}
              />
            </View>
          </ScrollView>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "90%",
    height: "100%",
    backgroundColor: colors.white,
    alignItems: "center",
  },
  backButton: {
    alignSelf: "flex-start",
    marginTop: (3 * height) / 100,
    padding: 5,
  },
  titleContainer: {
    width: "100%",
    alignItems: "flex-start",
    marginTop: (2 * height) / 100,
    marginBottom: (5 * height) / 100,
  },
  title: {
    fontFamily: fonts.dmSansBold,
    fontSize: 24,
    marginBottom: 8,
  },
  subtitle: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    color: colors.gray1,
  },
  optionsContainer: {
    width: "100%",
    marginBottom: (4 * height) / 100,
  },
  optionCard: {
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    padding: 16,
    backgroundColor: colors.white,
    borderRadius: 16,
    marginBottom: 16,
    // borderWidth: 1,
    // borderColor: colors.stroke,
  },
  selectedCard: {
    borderColor: colors.primary || "#1A73E8",
    borderWidth: 1,
    backgroundColor: colors.primaryLight,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: "center",
    borderWidth: 1,
    borderColor: colors.stroke,
    alignItems: "center",
    marginRight: 12,
  },
  selectedIconContainer: {
    backgroundColor: colors.primaryLight_100 || "#D5E5FF",
    borderWidth: 0,
    borderColor: "transparent",
  },
  optionTextContainer: {
    flex: 1,
  },
  optionTitle: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    color: colors.gray1 || "#757575",
    marginBottom: 4,
  },
  optionValue: {
    fontFamily: fonts.dmSansMedium,
    fontSize: 14,
    color: colors.black,
  },
  buttonContainer: {
    width: "100%",
    position: "absolute",
    bottom: (5 * height) / 100,
  },
  continueButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
  },
});
