import { Text, StyleProp, TextStyle } from "react-native";
import React from "react";
import { fonts } from "../Config/Fonts";

interface PProps {
  children: any;
  style?: StyleProp<TextStyle>;
  numberOfLines?: number;
  onPress?: any;
}

export default function P({ children, style, numberOfLines, onPress }: PProps) {
  return (
    <Text
      onPress={onPress}
      numberOfLines={numberOfLines}
      style={[
        {
          fontFamily: fonts.dmSansMedium,
          fontSize: 14,
          color: "rgba(22, 24, 23, 1)",
          lineHeight: 18,
          letterSpacing: 0,
        },
        style as TextStyle,
      ]}
    >
      {children}
    </Text>
  );
}
