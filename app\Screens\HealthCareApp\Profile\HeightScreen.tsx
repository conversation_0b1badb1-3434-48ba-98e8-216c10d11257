import React, { useState } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  ScrollView,
  StatusBar,
} from "react-native";
import { colors } from "../../../Config/colors";
import { fonts } from "../../../Config/Fonts";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import Button from "../../../Components/Button";
import AppHeader from "../../../Components/AppHeader";
import { useToast } from "../../../Context/ToastContext";
import TabSelector from "../../../Components/profile/ProfileTabSelector";
import { tabs } from "./tabs";
import Div from "../../../Components/Div";
import SliderVisualization from "../../../Components/SliderVisualization";
import { UpdateMedicalHistory } from "../../../RequestHandler.tsx/User";
import Loader from "../../../Components/Loader";
import NetworkErrorView from "../../../Components/NetworkErrorView";
import {
  getErrorType,
  getErrorMessage,
} from "../../../utils/networkErrorHandler";

const { height } = Dimensions.get("window");

// Height unit options
const heightUnitOptions = [
  { id: 1, label: "Cm", value: "cm" },
  { id: 2, label: "Ft", value: "ft" },
  { id: 3, label: "In", value: "in" },
];

export default function HeightScreen({ navigation, route }) {
  // Get current height from route params if available
  const { currentValue, onSave } = route.params || {
    currentValue: "",
    onSave: null,
  };
  // Define min and max values for each unit
  const minMaxValues = {
    cm: { min: 0, max: 305 },
    ft: { min: 0, max: 10 }, // Realistic height range in feet with decimals
    in: { min: 0, max: 120 }, // 3ft to 7ft in inches
  };

  const [selectedUnit, setSelectedUnit] = useState("cm");
  const [heightValue, setHeightValue] = useState(
    currentValue ? parseFloat(currentValue) : 170
  ); // Default height in cm
  const [loading, setLoading] = useState(false);
  const [networkError, setNetworkError] = useState<any>(null);
  const [showNetworkError, setShowNetworkError] = useState(false);
  const { handleToast } = useToast();

  // Function to handle network errors
  const handleNetworkError = (error: any) => {
    setNetworkError(error);
    setShowNetworkError(true);
  };

  // Function to retry after network error
  const handleRetry = () => {
    setShowNetworkError(false);
    setNetworkError(null);
    handleSaveChanges();
  };

  // Handle unit selection
  const handleUnitSelect = (value: string) => {
    // Store current height value before conversion
    const currentHeight = heightValue;

    // Convert height value based on selected unit
    let newHeight: number;
    if (value === "cm" && selectedUnit === "ft") {
      // Convert from ft to cm
      newHeight = currentHeight * 30.48;
    } else if (value === "ft" && selectedUnit === "cm") {
      // Convert from cm to ft (with decimal precision)
      newHeight = Math.round((currentHeight / 30.48) * 10) / 10;
    } else if (value === "in" && selectedUnit === "cm") {
      // Convert from cm to inches
      newHeight = currentHeight / 2.54;
    } else if (value === "cm" && selectedUnit === "in") {
      // Convert from inches to cm
      newHeight = currentHeight * 2.54;
    } else if (value === "ft" && selectedUnit === "in") {
      // Convert from inches to feet (with decimal precision)
      newHeight = Math.round((currentHeight / 12) * 10) / 10;
    } else if (value === "in" && selectedUnit === "ft") {
      // Convert from feet to inches
      newHeight = currentHeight * 12;
    } else {
      newHeight = currentHeight;
    }

    // Update selected unit
    setSelectedUnit(value);

    // Update height value
    setHeightValue(newHeight);
  };

  // Format height value for display
  const getFormattedHeight = () => {
    if (selectedUnit === "ft") {
      // Format feet with decimal places (e.g., 5.8 ft)
      return `${heightValue.toFixed(1)} ft`;
    } else {
      // Format as regular number for cm and inches
      return `${Math.round(heightValue)} ${selectedUnit}`;
    }
  };

  // Handle save changes
  const handleSaveChanges = async () => {
    if (!heightValue) {
      handleToast("Please select a height", "error");
      return;
    }

    setLoading(true);

    try {
      // Format the height value with the unit for display
      const formattedHeight = getFormattedHeight();
      // Prepare the request body with the numeric height value and unit
      const requestBody = {
        height: Math.round(heightValue),
        heightUnit: selectedUnit,
      };
      console.log("Updating height:", requestBody);
      // Call the API to update medical history
      const response = await UpdateMedicalHistory(requestBody);
      console.log("Update response:", response);
      // Call the onSave callback if provided
      if (onSave) {
        onSave(formattedHeight);
      }
      handleToast("Height updated successfully", "success");
      // Navigate back to profile screen
      navigation.pop();
    } catch (error) {
      console.error("Error updating height:", error);
      // Check if it's a network error
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        handleNetworkError(error);
      } else {
        handleToast("Failed to update height. Please try again.", "error");
      }
    } finally {
      setLoading(false);
    }
  };

  // If there's a network error, show the NetworkErrorView
  if (showNetworkError) {
    return (
      <NetworkErrorView
        errorType={getErrorType(networkError)}
        message={getErrorMessage(networkError)}
        onRetry={handleRetry}
      />
    );
  }

  return (
    <View style={styles.container}>
      <Div>
        {/* Header */}
        <View style={styles.headerContainer}>
          <AppHeader
            navigation={navigation}
            showBackButton={true}
            title={"Henry Osuji"}
            navStyle={{
              justifyContent: "flex-start",
              alignItems: "flex-start",
            }}
            subtitle={`20% profile completed`}
            subtitleStyle={styles.completionText}
          />
        </View>

        {/* Tabs */}
        {/* <TabSelector
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={handleTabChange}
        /> */}

        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
        >
          <View style={styles.contentContainer}>
            {/* Title */}
            <H4 style={styles.title}>Add Height</H4>

            {/* Unit Options */}
            <View style={styles.unitOptionsContainer}>
              {heightUnitOptions.map((option) => (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.unitButton,
                    selectedUnit === option.value && styles.selectedUnit,
                  ]}
                  onPress={() => handleUnitSelect(option.value)}
                >
                  <P
                    style={[
                      styles.unitText,
                      selectedUnit === option.value && styles.selectedUnitText,
                    ]}
                  >
                    {option.label}
                  </P>
                </TouchableOpacity>
              ))}
            </View>

            {/* Height Value Display */}
            {/* <View style={styles.valueDisplayContainer}>
              <H4 style={styles.valueText}>{getFormattedHeight()}</H4>
            </View> */}
            {/* Height Slider Visualization */}
            <View
              style={{
                width: "100%",
                alignItems: "center",
                marginTop: 10,
                marginBottom: 30,
              }}
            >
              <SliderVisualization
                value={heightValue}
                minValue={minMaxValues[selectedUnit].min}
                maxValue={minMaxValues[selectedUnit].max}
                unit={selectedUnit}
                step={selectedUnit === "ft" ? 0.1 : 1} // 0.1 for feet, 1 for cm and inches
                onValueChange={(newValue) => {
                  setHeightValue(newValue);
                }}
              />
            </View>
          </View>
        </ScrollView>

        {/* Save Button */}
        <View style={styles.buttonContainer}>
          <Button
            btnText="Save changes"
            onPress={handleSaveChanges}
            loading={loading}
            style={styles.saveButton}
          />
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerContainer: {
    width: "100%",
    paddingHorizontal: 20,
  },
  completionText: {
    color: "#D4AF37", // Gold color for completion percentage
    fontSize: 12,
    fontFamily: fonts.dmSansRegular,
    marginTop: 4,
  },
  scrollView: {
    flex: 1,
    width: "100%",
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingTop: (15 * height) / 100,
    paddingBottom: 100,
    alignItems: "center",
  },
  title: {
    fontFamily: fonts.dmSansBold,
    color: colors.black,
    marginBottom: (2.8 * height) / 100,
    textAlign: "center",
    fontSize: 16,
  },
  unitOptionsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    marginBottom: 40,
  },
  unitButton: {
    width: "30%",
    height: 56,
    borderRadius: 28,
    borderWidth: 1,
    borderColor: colors.b2Brown,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.white,
  },
  selectedUnit: {
    backgroundColor: colors.b2Brown,
  },
  unitText: {
    fontSize: 12,
    fontFamily: fonts.dmSansMedium,
    color: colors.b2Brown,
  },
  selectedUnitText: {
    color: colors.white,
  },
  valueDisplayContainer: {
    alignItems: "center",
    marginBottom: 30,
  },
  valueText: {
    fontFamily: fonts.dmSansBold,
    color: colors.black,
    fontSize: 16
  },
  buttonContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
    paddingBottom: 40,
    backgroundColor: colors.white,
  },
  saveButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
  },
});
