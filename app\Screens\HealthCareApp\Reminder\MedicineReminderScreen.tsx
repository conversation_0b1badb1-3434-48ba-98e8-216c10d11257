import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  Image,
  TouchableOpacity,
  Dimensions,
  Alert,
  ActivityIndicator,
} from "react-native";
import { colors } from "../../../Config/colors";
import { fonts } from "../../../Config/Fonts";
import Div from "../../../Components/Div";
import P from "../../../Components/P";
import Button from "../../../Components/Button";
import AppHeader from "../../../Components/AppHeader";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../Config/Svg";
import { GetAllReminder, DeleteReminder } from "../../../RequestHandler.tsx/User";
import { useToast } from "../../../Context/ToastContext";
import NetworkErrorView from "../../../Components/NetworkErrorView";
import { getErrorMessage, getErrorType } from "../../../utils/networkErrorHandler";


const { width, height } = Dimensions.get("window");

// Define reminder interface
interface MedicineReminder {
  id: string;
  medicineName: string;
  dosage: string;
  form: string;
  time: string;
  repeat: string;
  customRepeat: string;
  guidiance: string;
}

export default function MedicineReminderScreen({ navigation }) {
  const { handleToast } = useToast();
  const [reminders, setReminders] = useState<MedicineReminder[]>([]);
  const [loading, setLoading] = useState(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [networkError, setNetworkError] = useState<any>(null);
  const [showNetworkError, setShowNetworkError] = useState(false);

  // Function to handle network errors
  const handleNetworkError = (error: any) => {
    setNetworkError(error);
    setShowNetworkError(true);
  };

  // Function to retry after network error
  const handleRetry = () => {
    setShowNetworkError(false);
    setNetworkError(null);
    loadReminders();
  };

  // Load reminders from API
  useEffect(() => {
    loadReminders();
  }, []);

  // Load reminders when returning to this screen
  useEffect(() => {
    const unsubscribe = navigation.addListener("focus", () => {
      loadReminders();
    });

    return unsubscribe;
  }, [navigation]);

  const loadReminders = async () => {
    try {
      setLoading(true);
      const res = await GetAllReminder();
      console.log("Reminders response:", res);
      if (res.items) {
        setReminders(res.items);
      }
    } catch (error) {
      console.error("Failed to load reminders:", error);

      // Check if it's a network error
      if (getErrorType(error) === 'network' || getErrorType(error) === 'timeout') {
        handleNetworkError(error);
      } else {
        handleToast("Failed to load reminders", "error");
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCreateReminder = () => {
    navigation.navigate("CreateReminderScreen");
  };

  const confirmDeleteReminder = (id: string) => {
    Alert.alert(
      "Delete Reminder",
      "Are you sure you want to delete this reminder?",
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => handleDeleteReminder(id)
        }
      ]
    );
  };

  const handleDeleteReminder = async (id: string) => {
    try {
      setDeletingId(id);

      // Call the API to delete the reminder
      const response = await DeleteReminder(id);
      console.log("Delete reminder response:", response);

      // Update the local state by removing the deleted reminder
      setReminders(prevReminders => prevReminders.filter(reminder => reminder.id !== id));

      // Show success message
      handleToast("Reminder deleted successfully", "success");
    } catch (error) {
      console.error("Failed to delete reminder:", error);

      // Check if it's a network error
      if (getErrorType(error) === 'network' || getErrorType(error) === 'timeout') {
        handleNetworkError(error);
      } else {
        handleToast("Failed to delete reminder", "error");
      }
    } finally {
      setDeletingId(null);
    }
  };

  const formatRepeatText = (repeat: string) => {
    if (repeat === "Mon-Sun") {
      return "Daily";
    }
    return repeat;
  };

  // Format time from 24-hour format (15:10:00) to 12-hour format (3:10 PM)
  const formatTime = (time: string): string => {
    if (!time) return '';

    try {
      // Extract hours and minutes from the time string
      const timeParts = time.split(':');
      if (timeParts.length < 2) return time;

      let hours = parseInt(timeParts[0], 10);
      const minutes = parseInt(timeParts[1], 10);

      // Determine AM/PM
      const period = hours >= 12 ? 'PM' : 'AM';

      // Convert hours to 12-hour format
      hours = hours % 12;
      hours = hours === 0 ? 12 : hours; // Convert 0 to 12 for 12 AM

      // Format the time string
      return `${hours}:${minutes.toString().padStart(2, '0')} ${period}`;
    } catch (error) {
      console.error('Error formatting time:', error);
      return time;
    }
  };

  // If there's a network error, show the NetworkErrorView
  if (showNetworkError) {
    // Map the error type to one of the expected values
    const errorTypeValue = getErrorType(networkError);
    let mappedErrorType: "network" | "timeout" | "server" | "generic" = "generic";

    if (errorTypeValue === 'network') {
      mappedErrorType = "network";
    } else if (errorTypeValue === 'timeout') {
      mappedErrorType = "timeout";
    } else if (errorTypeValue === 'server') {
      mappedErrorType = "server";
    }

    return (
      <NetworkErrorView
        errorType={mappedErrorType}
        message={getErrorMessage(networkError)}
        onRetry={handleRetry}
      />
    );
  }

  return (
    <View style={styles.mainContainer}>
      <Div>
        {/* Header */}
        <AppHeader
          navigation={navigation}
          showBackButton={true}
          title="Medicine Reminder"
          navStyle={{ justifyContent: "flex-start", paddingHorizontal: 16 }}
          titleStyle={{ color: colors.black }}
        />

        {/* Show loading indicator when fetching reminders */}
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : (
          <ScrollView
            style={styles.container}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.contentContainer}
          >
            {reminders.length === 0 ? (
              // Empty state
              <View style={styles.emptyStateContainer}>
                <Image
                  source={require("../../../assets/reminder_empty.png")}
                  style={styles.emptyStateImage}
                />
                <P style={styles.emptyStateText}>No Reminders yet</P>
              </View>
            ) : (
              // Reminders list
              <View style={styles.remindersContainer}>
                {reminders.map((reminder) => (
                  <View key={reminder.id} style={styles.reminderItem}>
                    <View style={styles.reminderContent}>
                      <View style={styles.reminderHeader}>
                        <P style={styles.reminderTime}>{formatTime(reminder.time)}</P>
                        <P style={styles.reminderName}>
                          {reminder.medicineName}
                          {reminder.dosage ? ` - ${reminder.dosage}` : ""}
                        </P>
                      </View>
                      <P style={styles.reminderRepeat}>
                        {formatRepeatText(
                          reminder.customRepeat
                            ? reminder.customRepeat
                            : reminder.repeat
                        )}
                        {reminder.guidiance ? ` - ${reminder?.guidiance}` : ""}
                      </P>
                    </View>
                    <TouchableOpacity
                      style={styles.deleteButton}
                      onPress={() => confirmDeleteReminder(reminder.id)}
                      disabled={deletingId === reminder.id}
                    >
                      {deletingId === reminder.id ? (
                        <ActivityIndicator size="small" color={colors.error || "red"} />
                      ) : (
                        <SvgXml
                          xml={
                            svg.trash ||
                            '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 6H5H21" stroke="#FF0000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="#FF0000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
                          }
                          width={20}
                          height={20}
                        />
                      )}
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            )}
          </ScrollView>
        )}

        {/* Create Reminder Button */}
        <View style={styles.buttonContainer}>
          <Button
            btnText="Create Reminder"
            onPress={handleCreateReminder}
            style={styles.createButton}
          />
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "100%",
  },
  contentContainer: {
    flexGrow: 1,
    paddingHorizontal: 16,
    paddingBottom: 100,
  },
  emptyStateContainer: {
    justifyContent: "center",
    alignItems: "center",
    marginTop: (10 * height) / 100,
  },
  emptyStateImage: {
    width: (80 * width) / 100,
    height: (80 * width) / 100,
    resizeMode: "contain",
    marginBottom: 20,
  },
  emptyStateText: {
    fontSize: 16,
    fontFamily: fonts.dmSansMedium,
    color: colors.black,
    marginTop: 16,
  },
  remindersContainer: {
    width: "100%",
    marginTop: 20,
  },
  reminderItem: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.stroke,
  },
  reminderContent: {
    flex: 1,
  },
  reminderHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  reminderTime: {
    fontSize: 14,
    fontFamily: fonts.dmSansBold,
    color: colors.black,
    marginRight: 8,
  },
  reminderName: {
    fontSize: 14,
    fontFamily: fonts.dmSansMedium,
    color: colors.black,
  },
  reminderRepeat: {
    fontSize: 14,
    fontFamily: fonts.dmSansRegular,
    color: colors.gray1,
  },
  deleteButton: {
    padding: 8,
  },
  buttonContainer: {
    position: "absolute",
    bottom: 20,
    left: 0,
    right: 0,
    paddingHorizontal: 16,
  },
  createButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    height: (60 * height) / 100,
    paddingTop: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
    color: colors.primary,
    fontFamily: fonts.dmSansMedium,
  },
});
