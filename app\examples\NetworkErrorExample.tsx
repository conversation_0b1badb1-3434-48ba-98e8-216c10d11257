import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ActivityIndicator } from 'react-native';
import { colors } from '../Config/colors';
import NetworkErrorView from '../Components/NetworkErrorView';
import { getErrorType, getErrorMessage } from '../utils/networkErrorHandler';
import Button from '../Components/Button';
import H4 from '../Components/H4';
import Div from '../Components/Div';

/**
 * Example component showing how to use the NetworkErrorView component
 * This can be used as a reference for implementing network error handling in other screens
 */
const NetworkErrorExample = ({ navigation }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<any>(null);
  const [data, setData] = useState<any>(null);

  // Example function to simulate an API call
  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate different types of errors
      const errorType = Math.floor(Math.random() * 4);
      
      if (errorType === 0) {
        // Simulate successful response
        setData({ message: 'Data loaded successfully!' });
      } else if (errorType === 1) {
        // Simulate network error
        throw new Error('Network request failed');
      } else if (errorType === 2) {
        // Simulate timeout error
        throw new Error('Request timed out');
      } else {
        // Simulate server error
        const error: any = new Error('Internal Server Error');
        error.status = 500;
        throw error;
      }
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  // Retry handler for the NetworkErrorView
  const handleRetry = () => {
    fetchData();
  };

  // Initial data fetch
  useEffect(() => {
    fetchData();
  }, []);

  // Show loading indicator while fetching data
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  // Show error view if there's an error
  if (error) {
    return (
      <NetworkErrorView
        errorType={getErrorType(error)}
        message={getErrorMessage(error)}
        onRetry={handleRetry}
      />
    );
  }

  // Show data if available
  return (
    <Div>
      <H4>Data Loaded Successfully</H4>
      <View style={styles.buttonContainer}>
        <Button
          btnText="Simulate Another Request"
          onPress={fetchData}
        />
      </View>
    </Div>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  buttonContainer: {
    marginTop: 20,
  },
});

export default NetworkErrorExample;
