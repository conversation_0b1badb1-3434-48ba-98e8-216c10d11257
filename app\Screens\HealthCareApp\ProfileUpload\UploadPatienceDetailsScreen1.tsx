import React, { useEffect, useState } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
  ScrollView,
  Image,
  Alert,
  Platform,
} from "react-native";
import * as ImagePicker from "expo-image-picker";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../Config/Svg";
import AppHeader from "../../../Components/AppHeader";
import Input from "../../../Components/Input";
import DateInput from "../../../Components/DateInput";
import * as yup from "yup";
import { Formik } from "formik";
import { useToast } from "../../../Context/ToastContext";
import DropdownInput from "../../../Components/DropdownInput";
import { uploadImageToCloudinary } from "../../../Services/CloudinaryService";
import { UpdateUserDetails } from "../../../RequestHandler.tsx/User";

const { height } = Dimensions.get("window");

// Gender options for dropdown
const genderOptions = [
  { label: "Male", value: "male" },
  { label: "Female", value: "female" },
];

export default function UploadPatienceDetailsScreen1({ navigation, route }) {
  const [loading, setLoading] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);
  const { handleToast } = useToast();
  const [avatar, setAvatar] = useState<any>(
    require("../../../assets/user.png")
  );
  const [profileImageUri, setProfileImageUri] = useState<string | null>(null);
  const [cloudinaryUrl, setCloudinaryUrl] = useState<string | null>(null);
  const userAvatar = route?.params?.avatar || "";

  const profileSchema = yup.object().shape({
    firstName: yup.string().required("First name is required"),
    lastName: yup.string().required("Last name is required"),
    dateOfBirth: yup.string().required("Date of birth is required"),
    gender: yup.string().required("Gender is required"),
    address: yup.string().required("Address is required"),
    pushNotification: yup.boolean(),
    emailNotification: yup.boolean(),
  });

  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        quality: 0.5,
        base64: true,
      });

      if (!result.canceled && result.assets?.length > 0) {
        const imageUri = "data:image/jpeg;base64," + result.assets[0].base64;
        setProfileImageUri(imageUri);
        setAvatar({ uri: imageUri });

        // Upload to Cloudinary
        try {
          setUploadingImage(true);
          handleToast("Uploading image...", "success");

          // Upload the image to Cloudinary
          const cloudinaryImageUrl = await uploadImageToCloudinary(
            result.assets[0].base64
          );
          // Store the Cloudinary URL
          setCloudinaryUrl(cloudinaryImageUrl);
          setUploadingImage(false);
          handleToast("Profile picture uploaded successfully", "success");
        } catch (uploadError) {
          console.error("Error uploading to Cloudinary:", uploadError);
          setUploadingImage(false);
          handleToast(
            "Failed to upload image to cloud. Please try again.",
            "error"
          );

          // Still keep the local image for preview
          setProfileImageUri(imageUri);
          setAvatar({ uri: imageUri });
        }
      }
    } catch (error) {
      console.error("Error picking image:", error);
      handleToast("Failed to pick image. Please try again.", "error");
    }
  };

  const handleContinue = async (values: {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    gender: string;
    address: string;
    pushNotification: boolean;
    emailNotification: boolean;
  }) => {
    setLoading(true);

    try {
      // Prepare request body for the API
      const requestBody = {
        firstname: values.firstName.trim(),
        lastname: values.lastName.trim(),
        dob: values.dateOfBirth,
        gender: values.gender,
        address: values.address.trim(),
        pushNotification: true,
        emailNotification: true,
        avatar: cloudinaryUrl || "",
      };
      // Call the appropriate API based on whether we have an avatar
      const response = await UpdateUserDetails(requestBody);
      console.log("res", response);
      handleToast("Profile details saved successfully", "success");
      // Navigate to next screen with patient profile data
      navigation.navigate("MedicalHistoryScreen1");
    } catch (error) {
      console.error("Error updating profile:", error);
      const errorMessage =
        error?.message || "Failed to update profile. Please try again.";
      handleToast(errorMessage, "error");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (userAvatar) {
      setAvatar({ uri: userAvatar });
      setCloudinaryUrl(userAvatar);
    }
  }, []);

  return (
    <View style={styles.mainContainer}>
      <Div>
        <Formik
          initialValues={{
            firstName: "",
            lastName: "",
            dateOfBirth: "",
            gender: "",
            address: "",
            pushNotification: true,
            emailNotification: true,
          }}
          validationSchema={profileSchema}
          onSubmit={(values) => {
            handleContinue(values);
          }}
        >
          {(formikProps) => (
            <View style={styles.container}>
              <ScrollView
                style={{ width: "100%" }}
                automaticallyAdjustKeyboardInsets={true}
                contentContainerStyle={{
                  alignItems: "center",
                  paddingBottom: 120,
                }}
                showsVerticalScrollIndicator={false}
              >
                {/* Header */}
                <View style={{ width: "100%" }}>
                  <AppHeader navigation={navigation} />
                </View>

                {/* Profile Picture Section */}
                <View style={styles.profileImageSection}>
                  <View style={styles.profileImageContainer}>
                    <TouchableOpacity
                      style={styles.profileImage}
                      onPress={pickImage}
                    >
                      <Image
                        source={avatar}
                        style={styles.profileImagePlaceholder}
                      />
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={styles.editImageButton}
                      onPress={pickImage}
                      disabled={uploadingImage}
                    >
                      {uploadingImage ? (
                        <View style={styles.loadingIndicator}>
                          <Text style={styles.loadingText}>...</Text>
                        </View>
                      ) : (
                        <SvgXml
                          xml={
                            svg.edit ||
                            '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>'
                          }
                          width={16}
                          height={16}
                        />
                      )}
                    </TouchableOpacity>
                  </View>
                </View>

                {/* Form Fields */}
                <View style={styles.formContainer}>
                  <Input
                    label="First Name"
                    value={formikProps.values.firstName}
                    onChangeText={formikProps.handleChange("firstName")}
                    onBlur={formikProps.handleBlur("firstName")}
                    error={
                      formikProps.errors.firstName &&
                      formikProps.touched.firstName
                    }
                    errorText={formikProps.errors.firstName}
                  />

                  <Input
                    contStyle={{ marginTop: 16 }}
                    label="Last Name"
                    value={formikProps.values.lastName}
                    onChangeText={formikProps.handleChange("lastName")}
                    onBlur={formikProps.handleBlur("lastName")}
                    error={
                      formikProps.errors.lastName &&
                      formikProps.touched.lastName
                    }
                    errorText={formikProps.errors.lastName}
                  />

                  <View style={styles.rowContainer}>
                    <DateInput
                      contStyle={{ flex: 1, marginRight: 8 }}
                      label="Date of Birth"
                      placeholder="YYYY-MM-DD"
                      value={formikProps.values.dateOfBirth}
                      onSelect={(date) =>
                        formikProps.setFieldValue("dateOfBirth", date)
                      }
                      error={
                        formikProps.errors.dateOfBirth &&
                        formikProps.touched.dateOfBirth
                      }
                      errorText={formikProps.errors.dateOfBirth}
                    />

                    <DropdownInput
                      contStyle={{ flex: 1, marginLeft: 8 }}
                      label="Gender"
                      placeholder="Select"
                      value={formikProps.values.gender}
                      options={genderOptions}
                      onSelect={(gender) =>
                        formikProps.setFieldValue("gender", gender)
                      }
                      error={
                        formikProps.errors.gender && formikProps.touched.gender
                      }
                      errorText={formikProps.errors.gender}
                    />
                  </View>

                  <Input
                    contStyle={{ marginTop: 16 }}
                    label="Address"
                    placeholder="Enter Address"
                    value={formikProps.values.address}
                    onChangeText={formikProps.handleChange("address")}
                    onBlur={formikProps.handleBlur("address")}
                    error={
                      formikProps.errors.address && formikProps.touched.address
                    }
                    errorText={formikProps.errors.address}
                  />
                  {/* Removed notification toggles as requested */}
                </View>

                {/* Removed button from ScrollView */}
              </ScrollView>
              {/* Continue Button - Fixed at bottom */}
              <View style={styles.buttonContainer}>
                <Button
                  btnText="Continue"
                  onPress={formikProps.handleSubmit}
                  loading={loading}
                  style={styles.continueButton}
                />
              </View>
            </View>
          )}
        </Formik>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "90%",
    height: "100%",
    backgroundColor: colors.white,
    alignItems: "center",
  },
  profileImageSection: {
    alignItems: "center",
    // marginTop: (2 * height) / 100,
    marginBottom: (2 * height) / 100,
  },
  profileImageContainer: {
    position: "relative",
  },
  profileImage: {
    width: 150,
    height: 150,
    borderRadius: 100,
    borderWidth: 3,
    borderColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
    overflow: "hidden",
  },
  profileImagePlaceholder: {
    width: 150,
    height: 150,
    objectFit: "cover",
    backgroundColor: colors.primaryLight,
  },
  editImageButton: {
    position: "absolute",
    bottom: 10,
    right: 10,
    backgroundColor: colors.primary,
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: colors.white,
  },
  formContainer: {
    width: "100%",
    marginTop: (2 * height) / 100,
  },
  rowContainer: {
    flexDirection: "row",
    marginTop: 16,
  },
  // Removed unused notification styles
  buttonContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    width: "100%",
    paddingHorizontal: 20,
    paddingBottom: 30,
    paddingTop: 10,
    backgroundColor: colors.white,
  },
  continueButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
  },
  loadingIndicator: {
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
    height: "100%",
  },
  loadingText: {
    color: colors.white,
    fontSize: 14,
    fontWeight: "bold",
  },
});
