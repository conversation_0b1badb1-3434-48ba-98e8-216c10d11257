import * as React from "react";
import { NavigationContainer } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import BottomTabNavigator from "./BottomTabNavigator";
import { CredentailsContext } from "../Context/CredentailsContext";
import IntroScreen from "../Screens/IntroScreen";
import OnboardingScreen from "../Screens/HealthCareApp/OnboardingScreen";
import UploadPatienceDetailScreen1 from "../Screens/HealthCareApp/ProfileUpload/UploadPatienceDetailsScreen1";
import UploadDoctorDetailsScreen1 from "../Screens/Practitioner/UploadDetails/UploadDoctorDetailsScreen1";
import VerifyQualificationsScreen from "../Screens/Practitioner/UploadDetails/VerifyQualificationsScreen";
import IdentificationScreen from "../Screens/Practitioner/UploadDetails/IdentificationScreen";
import ProfileDetailsScreen from "../Screens/Practitioner/UploadDetails/ProfileDetailsScreen";
import LanguageDetailsScreen from "../Screens/Practitioner/UploadDetails/LanguageDetailsScreen";
import AsyncStorage from "@react-native-async-storage/async-storage";
import PractBottomTabNavigator from "./PractBottomTabNavigator";
import SignUpScreen from "../Screens/Authentication/SignUp/SignupScreen";
import VerifyEmailScreen from "../Screens/Authentication/SignUp/VerifyEmailScreen";
import LoginScreen from "../Screens/Authentication/SignIn/LoginScreen";
import ForgottenPasswordScreen from "../Screens/Authentication/SignIn/ForgetPasswordScreen";
import ForgottenPasswordScreen2 from "../Screens/Authentication/SignIn/ForgetPasswordScreen2";
import ForgottenPassWordVerify from "../Screens/Authentication/SignIn/ForgetPasswordVerify";
import ResetPasswordScreen from "../Screens/Authentication/SignIn/ResetPasswordScreen";
import MedicalHistoryScreen from "../Screens/HealthCareApp/ProfileUpload/MedicalHistoryScreen1";
import MedicalHistoryScreen1 from "../Screens/HealthCareApp/ProfileUpload/MedicalHistoryScreen1";
import MedicalHistoryScreen2 from "../Screens/HealthCareApp/ProfileUpload/MedicalHistoryScreen2";
import MedicalHistoryScreen3 from "../Screens/HealthCareApp/ProfileUpload/MedicalHistoryScreen3";
import ProfileSettingsScreen from "../Screens/HealthCareApp/Profile/ProfileSettingsScreen";
import MaritalStatusScreen from "../Screens/HealthCareApp/Profile/MaritalStatusScreen";
import BloodGroupScreen from "../Screens/HealthCareApp/Profile/BloodGroupScreen";
import HeightScreen from "../Screens/HealthCareApp/Profile/HeightScreen";
import WeightScreen from "../Screens/HealthCareApp/Profile/WeightScreen";
import ProfileMedicalScreen from "../Screens/HealthCareApp/Profile/ProfileMedicalScreen";
import EditField from "../Screens/HealthCareApp/Profile/EditField";
import AddTestReportScreen from "../Screens/HealthCareApp/Profile/AddTestReport";
import ViewTestReportScreen from "../Screens/HealthCareApp/Profile/ViewTestReportScreen";
import ManageAddressScreen from "../Screens/HealthCareApp/Address/ManageAddressScreen";
import AddAddressScreen from "../Screens/HealthCareApp/Address/AddAddressScreen";
import MedicineReminderScreen from "../Screens/HealthCareApp/Reminder/MedicineReminderScreen";
import CreateReminderScreen from "../Screens/HealthCareApp/Reminder/CreateReminderScreen";
import PractProfileSettingsScreen from "../Screens/Practitioner/Profile/PractProfileSettingScreen";
import LanguageDetailsScreenEdit from "../Screens/Practitioner/Profile/LanguageDetailsScreenEdit";

const Stack = createStackNavigator();

export default function MainStack() {
  const navigationRef = React.useRef(null);
  return (
    <CredentailsContext.Consumer>
      {({ storedCredentails }) => (
        <NavigationContainer ref={navigationRef}>
          <Stack.Navigator id={undefined}>
            {storedCredentails ? (
              // Stack when the user is logged in
              <>
                {/* @ts-ignore */}
                {storedCredentails.user?.role === "patient" ? (
                  <Stack.Screen
                    name="BottomTabNavigator"
                    component={BottomTabNavigator}
                    options={{
                      headerTransparent: true,
                      headerShadowVisible: false,
                      headerShown: false,
                    }}
                  />
                ) : (
                  <Stack.Screen
                    name="PractBottomTabNavigator"
                    component={PractBottomTabNavigator}
                    options={{
                      headerTransparent: true,
                      headerShadowVisible: false,
                      headerShown: false,
                    }}
                  />
                )}

                <Stack.Screen
                  name="UploadPatienceDetailsScreen1"
                  component={UploadPatienceDetailScreen1}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ProfileSettingsScreen"
                  component={ProfileSettingsScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="MaritalStatusScreen"
                  component={MaritalStatusScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="BloodGroupScreen"
                  component={BloodGroupScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="HeightScreen"
                  component={HeightScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="WeightScreen"
                  component={WeightScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ProfileMedicalScreen"
                  component={ProfileMedicalScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="EditField"
                  component={EditField}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="AddTestReportScreen"
                  component={AddTestReportScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ViewTestReportScreen"
                  component={ViewTestReportScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ManageAddressScreen"
                  component={ManageAddressScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="AddAddressScreen"
                  component={AddAddressScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="MedicineReminderScreen"
                  component={MedicineReminderScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="CreateReminderScreen"
                  component={CreateReminderScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />

                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* practitioners ////////////////////////////// */}
                {/* ////////////////////////////////////////////// */}
                {/* ///////////////////////////////////////////////// */}
                {/* /////////////////////////////////////practitoners */}
                <Stack.Screen
                  name="UploadDoctorDetailsScreen1"
                  component={UploadDoctorDetailsScreen1}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="VerifyQualificationsScreen"
                  component={VerifyQualificationsScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="IdentificationScreen"
                  component={IdentificationScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ProfileDetailsScreen"
                  component={ProfileDetailsScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="PractProfileSettingsScreen"
                  component={PractProfileSettingsScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="LanguageDetailsScreen"
                  component={LanguageDetailsScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="LanguageDetailsScreenEdit"
                  component={LanguageDetailsScreenEdit}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="MedicalHistoryScreen1"
                  component={MedicalHistoryScreen1}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="MedicalHistoryScreen2"
                  component={MedicalHistoryScreen2}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="MedicalHistoryScreen3"
                  component={MedicalHistoryScreen3}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
              </>
            ) : (
              // Stack when the user is not logged in
              <>
                <Stack.Screen
                  name="OnboardingScreen"
                  component={OnboardingScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="IntroScreen"
                  component={IntroScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="SignUpScreen"
                  component={SignUpScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="VerifyEmailScreen"
                  component={VerifyEmailScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="LoginScreen"
                  component={LoginScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ForgottenPassWordScreen"
                  component={ForgottenPasswordScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ForgottenPassWordScreen2"
                  component={ForgottenPasswordScreen2}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ForgottenPassWordVerify"
                  component={ForgottenPassWordVerify}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ResetPasswordScreen"
                  component={ResetPasswordScreen}
                  options={{
                    headerTransparent: true,
                    headerShadowVisible: false,
                    headerShown: false,
                  }}
                />
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* /////////////////////////////////////////// */}
                {/* practitioners ////////////////////////////// */}
                {/* ////////////////////////////////////////////// */}
                {/* ///////////////////////////////////////////////// */}
                {/* /////////////////////////////////////practitoners */}
              </>
            )}
          </Stack.Navigator>
        </NavigationContainer>
      )}
    </CredentailsContext.Consumer>
  );
}
