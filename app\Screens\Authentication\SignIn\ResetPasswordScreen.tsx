import React, { useState } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  Keyboard,
  ScrollView,
} from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../Config/Svg";
import AppHeader from "../../../Components/AppHeader";
import Input from "../../../Components/Input";
import * as yup from "yup";
import { Formik } from "formik";
import { ResetPassword } from "../../../RequestHandler.tsx/Auth";
import { useToast } from "../../../Context/ToastContext";

const { width, height } = Dimensions.get("window");

export default function ResetPasswordScreen({ navigation, route }) {
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const { value } = route?.params || "";
  const [loading, setLoading] = useState(false);
  const { handleToast } = useToast();
  const handleContinue = () => {
    // Navigate to the verification screen
    navigation.navigate("LoginScreen");
  };

  const resetSchema = yup.object().shape({
    newPassword: yup
      .string()
      .required("New password is required")
      .matches(
        /^(?=.*[0-9])(?=.*[*?!#$%&@^()\-_=+\\|[\]{};:/?.>])(?=.*[a-z])(?=.*[A-Z])[a-zA-Z0-9*?!#$%&@^()\-_=+\\|[\]{};:/?.>]{8,}$/,
        "Password must contain at least 8 characters, including letters, numbers, and special characters, with at least one uppercase letter"
      ),
    confirmPassword: yup
      .string()
      .required("Type your password again")
      .oneOf([yup.ref("newPassword"), null], "Passwords must match"),
  });

  return (
    <View style={styles.mainContainer}>
      <Div>
        <Formik
          initialValues={{
            emailOrPhone: value, // email is being passed as a prop to the initial value
            newPassword: "",
            confirmPassword: "",
          }}
          validationSchema={resetSchema}
          onSubmit={async (values, actions) => {
            Keyboard.dismiss();
            setLoading(true);

            try {
              const res = await ResetPassword(values);
              // Success case
              handleToast(res.message || "Password reset successful", "success");
              setTimeout(() => {
                navigation.navigate("LoginScreen");
              }, 2000);
            } catch (error) {
              console.log(error);
              // Handle API errors
              if (error.message) {
                handleToast(error.message, "error");
              } else {
                handleToast("Failed to reset password. Please try again.", "error");
              }
            } finally {
              setLoading(false);
            }
          }}
        >
          {(formikProps) => (
            <View style={styles.container}>
              <ScrollView
                style={{ width: "100%" }}
                automaticallyAdjustKeyboardInsets={true}
                contentContainerStyle={{
                  alignItems: "center",
                  minHeight: "100%",
                }}
                showsVerticalScrollIndicator={false}
              >
                {/* Back Button */}
                <View style={{ width: "100%" }}>
                  <AppHeader navigation={navigation} />
                </View>

                {/* Title Section */}
                <View style={styles.titleContainer}>
                  <H4 style={styles.title}>Create New Password</H4>
                </View>

                <View style={styles.formContainer}>
                  <Input
                    contStyle={{ marginTop: 16 }}
                    label={"New Password"}
                    type="password"
                    autoCapitalize="none"
                    onChangeText={formikProps.handleChange("newPassword")}
                    value={formikProps.values.newPassword}
                    onBlur={formikProps.handleBlur("newPassword")}
                    error={
                      formikProps.errors.newPassword &&
                      formikProps.touched.newPassword
                    }
                    errorText={formikProps.errors.newPassword}
                  />
                  <Input
                    contStyle={{ marginTop: 16 }}
                    label={"Confirm New Password"}
                    type="password"
                    autoCapitalize="none"
                    onChangeText={formikProps.handleChange("confirmPassword")}
                    value={formikProps.values.confirmPassword}
                    onBlur={formikProps.handleBlur("confirmPassword")}
                    error={
                      formikProps.errors.confirmPassword &&
                      formikProps.touched.confirmPassword
                    }
                    errorText={formikProps.errors.confirmPassword}
                  />
                </View>

                {/* Continue Button */}
                <View style={styles.buttonContainer}>
                  <Button
                    btnText="Continue"
                    onPress={() => {
                      formikProps.handleSubmit();
                    }}
                    loading={loading}
                    style={styles.continueButton}
                  />
                </View>
              </ScrollView>
            </View>
          )}
        </Formik>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "90%",
    height: "100%",
    backgroundColor: colors.white,
    alignItems: "center",
  },
  backButton: {
    alignSelf: "flex-start",
    marginTop: (3 * height) / 100,
    padding: 5,
  },
  titleContainer: {
    width: "100%",
    alignItems: "flex-start",
    marginTop: (2 * height) / 100,
    marginBottom: (3 * height) / 100,
  },
  title: {
    fontFamily: fonts.dmSansBold,
    fontSize: 24,
    marginBottom: 8,
  },
  subtitle: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    color: colors.gray1,
  },
  optionsContainer: {
    width: "100%",
    marginBottom: (4 * height) / 100,
  },
  optionCard: {
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    padding: 16,
    backgroundColor: colors.white,
    borderRadius: 16,
    marginBottom: 16,
    // borderWidth: 1,
    // borderColor: colors.stroke,
  },
  selectedCard: {
    borderColor: colors.primary || "#1A73E8",
    borderWidth: 1,
    backgroundColor: colors.primaryLight,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: "center",
    borderWidth: 1,
    borderColor: colors.stroke,
    alignItems: "center",
    marginRight: 12,
  },
  selectedIconContainer: {
    backgroundColor: colors.primaryLight_100 || "#D5E5FF",
    borderWidth: 0,
    borderColor: "transparent",
  },
  optionTextContainer: {
    flex: 1,
  },
  optionTitle: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    color: colors.gray1 || "#757575",
    marginBottom: 4,
  },
  optionValue: {
    fontFamily: fonts.dmSansMedium,
    fontSize: 14,
    color: colors.black,
  },
  buttonContainer: {
    width: "100%",
    position: "absolute",
    bottom: (5 * height) / 100,
  },
  continueButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
  },
  formContainer: {
    width: "100%",
  },
  inputLabel: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    marginBottom: 8,
  },
});
