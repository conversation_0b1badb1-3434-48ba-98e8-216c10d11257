import React, { useState } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
  ScrollView,
  Image,
  Platform,
  ActivityIndicator,
} from "react-native";
import * as DocumentPicker from "expo-document-picker";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../Config/Svg";
import AppHeader from "../../../Components/AppHeader";
import Input from "../../../Components/Input";
import SelectInput from "../../../Components/SelectInput";
import * as yup from "yup";
import { Formik } from "formik";
import { useToast } from "../../../Context/ToastContext";
import { uploadDocumentToCloudinary } from "../../../Services/CloudinaryService";
import { AddTestReport } from "../../../RequestHandler.tsx/User";
import NetworkErrorView from "../../../Components/NetworkErrorView";
import {
  getErrorMessage,
  getErrorType,
} from "../../../utils/networkErrorHandler";

const { width, height } = Dimensions.get("window");

export default function AddTestReportScreen({ navigation, route }) {
  const [loading, setLoading] = useState(false);
  const [uploadingDocument, setUploadingDocument] = useState(false);
  const [cloudinaryUrl, setCloudinaryUrl] = useState<string | null>(null);
  const { handleToast } = useToast();
  const [documentUploaded, setDocumentUploaded] = useState(false);
  const [documentInfo, setDocumentInfo] = useState<{
    name: string;
    uri: string;
    type: string;
    size: number;
  } | null>(null);
  const [networkError, setNetworkError] = useState<any>(null);
  const [showNetworkError, setShowNetworkError] = useState(false);

  // Function to handle network errors
  const handleNetworkError = (error: any) => {
    setNetworkError(error);
    setShowNetworkError(true);
  };

  // Function to retry after network error
  const handleRetry = () => {
    setShowNetworkError(false);
    setNetworkError(null);
  };

  // Validation schema for test report form
  const testReportSchema = yup.object().shape({
    reportName: yup
      .string()
      .required("Report name is required")
      .min(3, "Report name must be at least 3 characters"),
    doctorName: yup
      .string()
      .required("Doctor name is required")
      .min(3, "Doctor name must be at least 3 characters"),
    date: yup
      .string()
      .required("Date is required")
      .matches(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format"),
    time: yup
      .string()
      .required("Time is required")
      .matches(
        /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
        "Time must be in HH:MM format"
      ),
    documentUploaded: yup
      .boolean()
      .oneOf([true], "Please upload your test report")
      .required("Please upload your test report"),
  });

  const handleUploadDocument = async (
    setFieldValue: (field: string, value: any) => void
  ) => {
    try {
      // Open document picker for selecting files
      const result = await DocumentPicker.getDocumentAsync({
        type: [
          "application/pdf",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "image/jpeg",
          "image/png",
        ],
        copyToCacheDirectory: true,
        multiple: false,
      });
      // Check if the user selected a document
      if (
        result.canceled === false &&
        result.assets &&
        result.assets.length > 0
      ) {
        const selectedFile = result.assets[0];
        // Check file size (limit to 100MB)
        const fileSize = selectedFile.size || 0;
        const maxSize = 100 * 1024 * 1024; // 100MB in bytes

        if (fileSize > maxSize) {
          handleToast("File size exceeds 100MB limit", "error");
          return;
        }

        // Store document info and update state
        setDocumentInfo({
          name: selectedFile.name,
          uri: selectedFile.uri,
          type: selectedFile.mimeType,
          size: fileSize,
        });

        // Update Formik state
        setFieldValue("documentUploaded", true);
        setDocumentUploaded(true);
        handleToast("Document selected successfully", "success");

        // Upload to Cloudinary
        try {
          setUploadingDocument(true);
          // Upload the document to Cloudinary
          const cloudinaryDocUrl = await uploadDocumentToCloudinary(
            selectedFile.uri,
            selectedFile.mimeType || "application/octet-stream"
          );

          // Store the Cloudinary URL
          setCloudinaryUrl(cloudinaryDocUrl);
          setUploadingDocument(false);
          handleToast("Document uploaded to cloud successfully", "success");
        } catch (uploadError) {
          console.error("Error uploading to Cloudinary:", uploadError);
          setUploadingDocument(false);
          // Check if it's a network error
          if (
            getErrorType(uploadError) === "network" ||
            getErrorType(uploadError) === "timeout"
          ) {
            handleNetworkError(uploadError);
          } else {
            handleToast(
              "Failed to upload document to cloud. Please try again.",
              "error"
            );
          }

          // Still keep the local document info for preview
          setFieldValue("documentUploaded", true);
          setDocumentUploaded(true);
        }
      }
    } catch (error) {
      console.error("Error picking document:", error);
      handleToast("Failed to select document. Please try again.", "error");
    }
  };

  const handleContinue = async (values: {
    reportName: string;
    doctorName: string;
    date: string;
    time: string;
    documentUploaded: boolean;
  }) => {
    // Formik validation will handle the required fields
    if (!cloudinaryUrl) {
      handleToast("Please wait for document upload to complete", "error");
      return;
    }

    setLoading(true);

    try {
      // Combine date and time for the reportDate field
      // Ensure the date is in the correct format (YYYY-MM-DD)
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      const timeRegex = /^\d{2}:\d{2}$/;

      // Validate date and time format
      if (!dateRegex.test(values.date)) {
        handleToast("Date must be in YYYY-MM-DD format", "error");
        setLoading(false);
        return;
      }

      if (!timeRegex.test(values.time)) {
        handleToast("Time must be in HH:MM format", "error");
        setLoading(false);
        return;
      }

      // Create a valid ISO date string
      const reportDate = `${values.date}T${values.time}:00.000Z`;

      // Create test report object for API
      const requestBody = {
        name: values.reportName,
        doctorName: values.doctorName,
        reportDate: reportDate,
        reportFile: cloudinaryUrl,
      };
      console.log("Submitting test report:", requestBody);
      const response = await AddTestReport(requestBody);
      console.log("Test report response:", response);
      handleToast("Test report submitted successfully", "success");
      // Navigate back to previous screen or test reports list
      navigation.goBack();
    } catch (error) {
      console.error("Error submitting test report:", error);
      // Check if it's a network error
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        handleNetworkError(error);
      } else {
        handleToast("Failed to submit test report. Please try again.", "error");
      }
    } finally {
      setLoading(false);
    }
  };

  // If there's a network error, show the NetworkErrorView
  if (showNetworkError) {
    return (
      <NetworkErrorView
        errorType={getErrorType(networkError)}
        message={getErrorMessage(networkError)}
        onRetry={handleRetry}
      />
    );
  }

  return (
    <View style={styles.mainContainer}>
      <Div>
        <Formik
          initialValues={{
            reportName: "",
            doctorName: "",
            date: "",
            time: "",
            documentUploaded: false,
          }}
          validationSchema={testReportSchema}
          onSubmit={(values) => {
            handleContinue(values);
          }}
        >
          {(formikProps) => (
            <View style={styles.container}>
              <ScrollView
                style={{ width: "100%" }}
                automaticallyAdjustKeyboardInsets={true}
                contentContainerStyle={{
                  alignItems: "center",
                  paddingBottom: 100,
                }}
                showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}
              >
                {/* Header */}
                <View style={{ width: "100%" }}>
                  <AppHeader
                    navigation={navigation}
                    showBackButton={true}
                    title={"Henry Osuji"}
                    navStyle={{
                      justifyContent: "flex-start",
                      alignItems: "flex-start",
                    }}
                    subtitle={`20% profile completed`}
                    subtitleStyle={styles.completionText}
                  />
                </View>

                {/* Form Fields */}
                <View style={styles.formContainer}>
                  {/* Report Name */}
                  <Input
                    label="Report Name"
                    placeholder="Enter report name"
                    value={formikProps.values.reportName}
                    onChangeText={formikProps.handleChange("reportName")}
                    onBlur={formikProps.handleBlur("reportName")}
                    error={
                      formikProps.errors.reportName &&
                      formikProps.touched.reportName
                    }
                    errorText={formikProps.errors.reportName}
                  />
                  {/* Doctor Name */}
                  <Input
                    label="Doctor Name"
                    contStyle={{ marginTop: 16 }}
                    placeholder="Enter doctor name"
                    value={formikProps.values.doctorName}
                    onChangeText={formikProps.handleChange("doctorName")}
                    onBlur={formikProps.handleBlur("doctorName")}
                    error={
                      formikProps.errors.doctorName &&
                      formikProps.touched.doctorName
                    }
                    errorText={formikProps.errors.doctorName}
                  />

                  <View
                    style={{
                      width: "100%",
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "space-between",
                      marginTop: 16,
                    }}
                  >
                    <View style={{ width: "48%" }}>
                      <Input
                        label="Date"
                        placeholder="YYYY-MM-DD"
                        value={formikProps.values.date}
                        onChangeText={(text) => {
                          // Format date input with hyphens (YYYY-MM-DD)
                          const cleaned = text.replace(/[^0-9]/g, "");
                          let formatted = cleaned;

                          if (cleaned.length > 4) {
                            formatted = `${cleaned.slice(0, 4)}-${cleaned.slice(
                              4
                            )}`;
                          }
                          if (cleaned.length > 6) {
                            formatted = `${formatted.slice(
                              0,
                              7
                            )}-${formatted.slice(7, 9)}`;
                          }
                          // Limit to 10 characters (YYYY-MM-DD)
                          formatted = formatted.slice(0, 10);
                          formikProps.setFieldValue("date", formatted);
                        }}
                        onBlur={formikProps.handleBlur("date")}
                        error={
                          formikProps.errors.date && formikProps.touched.date
                        }
                        errorText={formikProps.errors.date}
                        keyboardType="numeric"
                      />
                    </View>
                    <View style={{ width: "48%" }}>
                      <Input
                        label="Time"
                        placeholder="HH:MM"
                        value={formikProps.values.time}
                        onChangeText={(text) => {
                          // Format time input with colon (HH:MM)
                          const cleaned = text.replace(/[^0-9]/g, "");
                          let formatted = cleaned;

                          if (cleaned.length > 2) {
                            formatted = `${cleaned.slice(0, 2)}:${cleaned.slice(
                              2
                            )}`;
                          }

                          // Limit to 5 characters (HH:MM)
                          formatted = formatted.slice(0, 5);

                          formikProps.setFieldValue("time", formatted);
                        }}
                        onBlur={formikProps.handleBlur("time")}
                        error={
                          formikProps.errors.time && formikProps.touched.time
                        }
                        errorText={formikProps.errors.time}
                        keyboardType="numeric"
                      />
                    </View>
                  </View>
                  {/* Upload Documentation */}
                  <View style={styles.uploadSection}>
                    <P style={styles.uploadLabel}>Upload report</P>
                    <TouchableOpacity
                      style={[
                        styles.uploadContainer,
                        documentUploaded && styles.uploadedContainer,
                        formikProps.errors.documentUploaded &&
                          formikProps.touched.documentUploaded &&
                          styles.uploadError,
                      ]}
                      onPress={() =>
                        handleUploadDocument(formikProps.setFieldValue)
                      }
                      onBlur={() =>
                        formikProps.setFieldTouched("documentUploaded")
                      }
                    >
                      {uploadingDocument ? (
                        <View>
                          <ActivityIndicator
                            size="large"
                            color={colors.primary}
                          />
                          <Text
                            style={{ marginTop: 20, color: colors.primary }}
                          >
                            Uploading document...
                          </Text>{" "}
                        </View>
                      ) : (
                        <>
                          <SvgXml
                            xml={
                              svg.upload ||
                              '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="#F0A500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M17 8L12 3L7 8" stroke="#F0A500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 3V15" stroke="#F0A500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
                            }
                            width={24}
                            height={24}
                          />

                          <P style={styles.uploadText}>
                            {documentUploaded
                              ? "Test Report Uploaded Successfully"
                              : "Drag & Drop your Test Report here or Browse Files"}
                          </P>
                          {documentUploaded && documentInfo && (
                            <P
                              style={[
                                styles.uploadSubtext,
                                { color: colors.primary },
                              ]}
                            >
                              {documentInfo.name}
                            </P>
                          )}
                          <P style={styles.uploadSubtext}>
                            Upload lab results, medical reports, or scans (PDF,
                            DOC, DOCX below 100MB)
                          </P>
                        </>
                      )}
                    </TouchableOpacity>
                  </View>
                </View>

                {/* Continue Button */}
                <View style={styles.buttonContainer}>
                  <Button
                    btnText="Update Report"
                    onPress={formikProps.handleSubmit}
                    loading={loading}
                    style={styles.continueButton}
                  />
                </View>
              </ScrollView>
            </View>
          )}
        </Formik>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "90%",
    height: "100%",
    backgroundColor: colors.white,
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontFamily: fonts.dmSansBold,
    marginTop: 16,
    marginBottom: 24,
    alignSelf: "flex-start",
  },
  formContainer: {
    width: "100%",
    marginTop: (2 * height) / 100,
  },
  selectInput: {
    marginTop: 16,
  },
  uploadSection: {
    width: "100%",
    marginTop: 32,
  },
  uploadLabel: {
    fontFamily: fonts.dmSansMedium,
    fontSize: 14,
    color: colors.black,
    marginBottom: 8,
  },
  uploadContainer: {
    width: "100%",
    height: 120,
    borderWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  uploadedContainer: {
    borderColor: colors.primary,
    backgroundColor: colors.primaryLight,
  },
  uploadText: {
    fontFamily: fonts.dmSansMedium,
    fontSize: 14,
    color: colors.b2Brown,
    marginTop: 8,
    textAlign: "center",
  },
  uploadSubtext: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    color: colors.gray1,
    marginTop: 4,
    textAlign: "center",
  },
  buttonContainer: {
    width: "100%",
    marginTop: (5 * height) / 100,
  },
  continueButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
  },
  uploadError: {
    borderColor: colors.error || "red",
  },
  errorText: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    color: colors.error || "red",
    marginTop: 4,
  },
  completionText: {
    color: "#D4AF37", // Gold color for completion percentage
    fontSize: 14,
    fontFamily: fonts.dmSansRegular,
    marginTop: 4,
  },
});
