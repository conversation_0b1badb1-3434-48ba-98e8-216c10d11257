import React, { useState, useEffect } from "react";
import {
  Dimensions,
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import AppHeader from "../../../Components/AppHeader";
import SelectInput from "../../../Components/SelectInput";
import * as yup from "yup";
import { Formik, FieldArray, FormikErrors } from "formik";
import { useToast } from "../../../Context/ToastContext";
import { SvgXml } from "react-native-svg";
import { GetLanguage } from "../../../RequestHandler.tsx/Auth";
import { UpdateProssionalDetails, GetProssionalDetails } from "../../../RequestHandler.tsx/User";
import NetworkErrorView from "../../../Components/NetworkErrorView";
import {
  getErrorType,
  getErrorMessage,
} from "../../../utils/networkErrorHandler";

const { height } = Dimensions.get("window");

// Default language options (will be replaced with API data)
const defaultLanguageOptions = [
  { label: "Loading languages...", value: "" },
];

// Fluency options
const fluencyOptions = [
  { label: "Native", value: "Native Speaker" },
  { label: "Fluent", value: "Fluent" },
  { label: "Intermediate", value: "Intermediate" },
  { label: "Beginner", value: "Beginner" },
  { label: "Basic", value: "Basic Understanding" },
];

// Language entry interface
interface LanguageEntry {
  language: string;
  fluency: string;
}

export default function LanguageDetailsScreenEdit({ navigation, route }) {
  const [loading, setLoading] = useState(false);
  const [fetchingLanguages, setFetchingLanguages] = useState(true);
  const [fetchingProfessionalDetails, setFetchingProfessionalDetails] = useState(true);
  const { handleToast } = useToast();
  const [languageOptions, setLanguageOptions] = useState<Array<{label: string, value: string}>>(
    defaultLanguageOptions
  );
  const [initialLanguages, setInitialLanguages] = useState<LanguageEntry[]>([
    { language: "", fluency: "" }
  ]);
  const [networkError, setNetworkError] = useState<any>(null);
  const [showNetworkError, setShowNetworkError] = useState(false);

  // Fetch languages and professional details from API
  useEffect(() => {
    fetchLanguages();
    fetchProfessionalDetails();
  }, []);

  // Function to fetch languages
  const fetchLanguages = async () => {
    try {
      setFetchingLanguages(true);
      const response = await GetLanguage();

      if (response && Array.isArray(response)) {
        // Transform API response to the format needed for SelectInput
        const options = response.map(item => ({
          label: item.name,
          value: item.id
        }));

        setLanguageOptions(options);
      } else {
        handleToast("Failed to load languages", "error");
      }
    } catch (error) {
      console.error("Error fetching languages:", error);

      // Check if it's a network error
      if (getErrorType(error) === 'network' || getErrorType(error) === 'timeout') {
        setNetworkError(error);
        setShowNetworkError(true);
      } else {
        handleToast("Failed to load languages", "error");
      }
    } finally {
      setFetchingLanguages(false);
    }
  };

  // Function to fetch professional details
  const fetchProfessionalDetails = async () => {
    try {
      setFetchingProfessionalDetails(true);
      const response = await GetProssionalDetails();

      if (response && response.data && response.data.languageFluencies && Array.isArray(response.data.languageFluencies)) {
        // Transform the existing language data to the format needed for the form
        const existingLanguages = response.data.languageFluencies.map((item: any) => ({
          language: item.language.id, // Use the language ID
          fluency: item.fluency
        }));

        // If there are existing languages, use them; otherwise, use default empty entry
        setInitialLanguages(existingLanguages.length > 0 ? existingLanguages : [{ language: "", fluency: "" }]);
      } else {
        // No existing languages, use default
        setInitialLanguages([{ language: "", fluency: "" }]);
      }
    } catch (error) {
      console.error("Error fetching professional details:", error);
      // Don't show error for this, just use default values
      setInitialLanguages([{ language: "", fluency: "" }]);
    } finally {
      setFetchingProfessionalDetails(false);
    }
  };

  // Function to retry after network error
  const handleRetry = () => {
    setShowNetworkError(false);
    setNetworkError(null);
    fetchLanguages();
    fetchProfessionalDetails();
  };

  // Validation schema for a single language entry
  const languageEntrySchema = yup.object().shape({
    language: yup.string().required("Language is required"),
    fluency: yup.string().required("Fluency level is required"),
  });

  // Validation schema for the form
  const languageDetailsSchema = yup.object().shape({
    languages: yup
      .array()
      .of(languageEntrySchema)
      .min(1, "At least one language is required"),
  });

  const handleSubmit = async (values: { languages: LanguageEntry[] }) => {
    try {
      setLoading(true);

      // Format the language data according to the API requirements
      const languageFluencies = values.languages.map(entry => ({
        languageId: entry.language,
        fluency: entry.fluency
      }));

      // Prepare the request body for the API
      const requestBody = {
        languageFluencies: languageFluencies
      };
      console.log("Sending language data:", requestBody);
      // Call the API to update professional details
      const response = await UpdateProssionalDetails(requestBody);
      console.log("Language update response:", response);
      // Show success message
      handleToast("Language details saved successfully", "success");
      // Navigate back to profile settings
      navigation.goBack();
    } catch (error) {
      console.error("Error updating language details:", error);

      // Check if it's a network error
      if (getErrorType(error) === "network" || getErrorType(error) === "timeout") {
        setNetworkError(error);
        setShowNetworkError(true);
      } else {
        // Show error message
        handleToast(
          getErrorMessage(error) || "Failed to update language details",
          "error"
        );
      }
    } finally {
      setLoading(false);
    }
  };

  // If there's a network error, show the NetworkErrorView
  if (showNetworkError) {
    return (
      <NetworkErrorView
        errorType={getErrorType(networkError) as "network" | "timeout" | "server" | "generic"}
        message={getErrorMessage(networkError)}
        onRetry={handleRetry}
      />
    );
  }

  // Show loading while fetching initial data
  if (fetchingLanguages || fetchingProfessionalDetails) {
    return (
      <View style={styles.mainContainer}>
        <Div>
          <View style={styles.container}>
            <View style={{ width: "100%" }}>
              <AppHeader navigation={navigation} />
            </View>
            <H4 style={styles.title}>Language Details</H4>
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
              <P style={styles.loadingText}>Loading language details...</P>
            </View>
          </View>
        </Div>
      </View>
    );
  }

  return (
    <View style={styles.mainContainer}>
      <Div>
        <Formik
          initialValues={{
            languages: initialLanguages,
          }}
          enableReinitialize={true}
          validationSchema={languageDetailsSchema}
          onSubmit={(values) => {
            handleSubmit(values);
          }}
        >
          {(formikProps) => (
            <View style={styles.container}>
              <ScrollView
                style={{ width: "100%" }}
                automaticallyAdjustKeyboardInsets={true}
                contentContainerStyle={{
                  alignItems: "center",
                  paddingBottom: 100,
                }}
                showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}
              >
                {/* Header */}
                <View style={{ width: "100%" }}>
                  <AppHeader navigation={navigation} />
                </View>

                {/* Title */}
                <H4 style={styles.title}>Language Details</H4>

                {/* Form Fields */}
                <FieldArray
                  name="languages"
                  render={(arrayHelpers) => (
                    <View style={styles.formContainer}>
                      {formikProps.values.languages.map((_, index) => (
                        <View key={index} style={styles.languageEntry}>
                          {/* Language Selection */}
                          <View style={{ position: 'relative' }}>
                            <SelectInput
                              label={`Spoken Language ${index + 1}`}
                              placeholder={fetchingLanguages ? "Loading languages..." : "Select"}
                              options={languageOptions}
                              selectedValues={
                                formikProps.values.languages[index].language
                                  ? [formikProps.values.languages[index].language]
                                  : []
                              }
                              onSelect={(values) => {
                                if (values.length > 0) {
                                  formikProps.setFieldValue(
                                    `languages[${index}].language`,
                                    values[0]
                                  );
                                }
                              }}
                              multiSelect={false}
                              error={
                                formikProps.touched.languages?.[index] &&
                                formikProps.errors.languages?.[index] &&
                                typeof formikProps.errors.languages[index] !==
                                  "string" &&
                                "language" in formikProps.errors.languages[index]
                              }
                              errorText={
                                formikProps.touched.languages?.[index] &&
                                formikProps.errors.languages?.[index] &&
                                typeof formikProps.errors.languages[index] !==
                                  "string" &&
                                "language" in formikProps.errors.languages[index]
                                  ? (formikProps.errors.languages[index] as any)
                                      .language
                                  : ""
                              }
                            />
                            {fetchingLanguages && (
                              <ActivityIndicator
                                size="small"
                                color={colors.primary}
                                style={styles.loadingIndicator}
                              />
                            )}
                          </View>

                          {/* Fluency Selection */}
                          <SelectInput
                            label="Fluency"
                            placeholder="Select"
                            options={fluencyOptions}
                            selectedValues={
                              formikProps.values.languages[index].fluency
                                ? [formikProps.values.languages[index].fluency]
                                : []
                            }
                            onSelect={(values) => {
                              if (values.length > 0) {
                                formikProps.setFieldValue(
                                  `languages[${index}].fluency`,
                                  values[0]
                                );
                              }
                            }}
                            multiSelect={false}
                            contStyle={{ marginTop: 16 }}
                            error={
                              formikProps.touched.languages?.[index] &&
                              formikProps.errors.languages?.[index] &&
                              typeof formikProps.errors.languages[index] !==
                                "string" &&
                              "fluency" in formikProps.errors.languages[index]
                            }
                            errorText={
                              formikProps.touched.languages?.[index] &&
                              formikProps.errors.languages?.[index] &&
                              typeof formikProps.errors.languages[index] !==
                                "string" &&
                              "fluency" in formikProps.errors.languages[index]
                                ? (formikProps.errors.languages[index] as any)
                                    .fluency
                                : ""
                            }
                          />

                          {/* Remove button for additional entries */}
                          {index > 0 && (
                            <TouchableOpacity
                              style={styles.removeButton}
                              onPress={() => arrayHelpers.remove(index)}
                            >
                              <P style={styles.removeButtonText}>Remove</P>
                            </TouchableOpacity>
                          )}
                        </View>
                      ))}

                      {/* Add More Languages Button */}
                      <TouchableOpacity
                        style={styles.addButton}
                        onPress={() =>
                          arrayHelpers.push({ language: "", fluency: "" })
                        }
                      >
                        <SvgXml
                          xml={
                            '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 5V19" stroke="#F0A500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M5 12H19" stroke="#F0A500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
                          }
                          width={20}
                          height={20}
                        />
                        <P style={styles.addButtonText}>
                          More Language Entries
                        </P>
                      </TouchableOpacity>

                      {/* Submit Button */}
                      <View style={styles.buttonContainer}>
                        <Button
                          btnText="Save Languages"
                          onPress={formikProps.handleSubmit}
                          loading={loading}
                          style={styles.submitButton}
                        />
                      </View>
                    </View>
                  )}
                />
              </ScrollView>
            </View>
          )}
        </Formik>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "90%",
    height: "100%",
    backgroundColor: colors.white,
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontFamily: fonts.dmSansBold,
    marginTop: 16,
    marginBottom: 24,
    alignSelf: "flex-start",
  },
  formContainer: {
    width: "100%",
    marginTop: (2 * height) / 100,
  },
  languageEntry: {
    width: "100%",
    marginBottom: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.stroke,
  },
  removeButton: {
    alignSelf: "flex-end",
    marginTop: 8,
    padding: 8,
  },
  removeButtonText: {
    color: colors.error || "red",
    fontFamily: fonts.dmSansMedium,
    fontSize: 14,
  },
  addButton: {
    alignSelf: "center",
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
    marginBottom: 24,
  },
  addButtonText: {
    color: colors.b2Brown,
    fontFamily: fonts.dmSansMedium,
    fontSize: 14,
    marginLeft: 8,
  },
  buttonContainer: {
    width: "100%",
    marginTop: (5 * height) / 100,
  },
  submitButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
  },
  loadingIndicator: {
    position: 'absolute',
    right: 16,
    top: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 50,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 14,
    fontFamily: fonts.dmSansRegular,
    color: colors.navGray,
  },
});
