import React, { useState } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import Button from "../../../Components/Button";
import AppHeader from "../../../Components/AppHeader";
import Input from "../../../Components/Input";
import { useToast } from "../../../Context/ToastContext";
import * as yup from "yup";
import { Formik } from "formik";

export default function AddAddressScreen({ navigation, route }) {
  const [loading, setLoading] = useState(false);
  const { handleToast } = useToast();
  const { address, isEditing } = route.params || {
    address: null,
    isEditing: false,
  };

  // Validation schema
  const addressSchema = yup.object().shape({
    fullName: yup.string().required("Full name is required"),
    apt: yup.string(),
    streetAddress: yup.string().required("Street address is required"),
    zipCode: yup.string().required("Zip code is required"),
    city: yup.string().required("City is required"),
    state: yup.string().required("State is required"),
  });

  // Define address interface
  interface AddressData {
    id: string;
    fullName: string;
    apt: string;
    streetAddress: string;
    zipCode: string;
    city: string;
    state: string;
    formattedAddress: string;
  }

  const handleSubmit = (values: {
    fullName: string;
    apt: string;
    streetAddress: string;
    zipCode: string;
    city: string;
    state: string;
  }) => {
    setLoading(true);

    // Create address object
    const addressData = {
      fullName: values.fullName,
      apt: values.apt,
      streetAddress: values.streetAddress,
      zipCode: values.zipCode,
      city: values.city,
      state: values.state,
      formattedAddress: `${values.streetAddress}, ${values.city}, ${values.state} ${values.zipCode}`,
    };

    // Simulate API call
    setTimeout(() => {
      setLoading(false);

      // Get existing addresses from route params or use empty array
      const existingAddresses: AddressData[] =
        route.params?.savedAddresses || [];

      // Add new address or update existing one
      let updatedAddresses: AddressData[];
      if (isEditing && address) {
        // Update existing address
        const addressIndex = existingAddresses.findIndex(
          (addr: AddressData) => addr.id === address.id
        );
        updatedAddresses = [...existingAddresses];
        updatedAddresses[addressIndex] = {
          ...addressData,
          id: address.id,
        } as AddressData;
      } else {
        // Add new address
        updatedAddresses = [
          ...existingAddresses,
          {
            ...addressData,
            id: Date.now().toString(), // Simple ID generation
          } as AddressData,
        ];
      }

      // Show success message
      handleToast(
        isEditing
          ? "Address updated successfully"
          : "Address added successfully",
        "success"
      );

      // Navigate back to manage address screen with updated addresses
      navigation.navigate("ManageAddressScreen", {
        savedAddresses: updatedAddresses,
      });
    }, 1000);
  };

  return (
    <View style={styles.mainContainer}>
      <Div>
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={{ flex: 1, width: "100%" }}
        >
          <AppHeader
            navigation={navigation}
            showBackButton={true}
            title="Manage Address"
            navStyle={{ justifyContent: "flex-start", paddingHorizontal: 16 }}
            titleStyle={{ color: colors.black }}
          />
          <Formik
            initialValues={{
              fullName: address?.fullName || "",
              apt: address?.apt || "",
              streetAddress: address?.streetAddress || "",
              zipCode: address?.zipCode || "",
              city: address?.city || "",
              state: address?.state || "",
            }}
            validationSchema={addressSchema}
            onSubmit={handleSubmit}
          >
            {(formikProps) => (
              <ScrollView
                style={styles.container}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.contentContainer}
              >
                {/* Form Fields */}
                <View style={styles.formContainer}>
                  <Input
                    label="Full Name"
                    placeholder=""
                    value={formikProps.values.fullName}
                    onChangeText={formikProps.handleChange("fullName")}
                    onBlur={formikProps.handleBlur("fullName")}
                    error={
                      !!(
                        formikProps.errors.fullName &&
                        formikProps.touched.fullName
                      )
                    }
                    errorText={formikProps.errors.fullName}
                  />

                  <Input
                    label="APT"
                    placeholder=""
                    contStyle={{ marginTop: 16 }}
                    value={formikProps.values.apt}
                    onChangeText={formikProps.handleChange("apt")}
                    onBlur={formikProps.handleBlur("apt")}
                    error={
                      !!(formikProps.errors.apt && formikProps.touched.apt)
                    }
                    errorText={formikProps.errors.apt}
                  />

                  <Input
                    label="Street Address"
                    placeholder=""
                    contStyle={{ marginTop: 16 }}
                    value={formikProps.values.streetAddress}
                    onChangeText={formikProps.handleChange("streetAddress")}
                    onBlur={formikProps.handleBlur("streetAddress")}
                    error={
                      !!(
                        formikProps.errors.streetAddress &&
                        formikProps.touched.streetAddress
                      )
                    }
                    errorText={formikProps.errors.streetAddress}
                  />

                  <Input
                    label="Zip Code(Postal Code)"
                    placeholder=""
                    contStyle={{ marginTop: 16 }}
                    value={formikProps.values.zipCode}
                    onChangeText={formikProps.handleChange("zipCode")}
                    onBlur={formikProps.handleBlur("zipCode")}
                    error={
                      !!(
                        formikProps.errors.zipCode &&
                        formikProps.touched.zipCode
                      )
                    }
                    errorText={formikProps.errors.zipCode}
                    keyboardType="numeric"
                  />

                  <View style={styles.rowContainer}>
                    <Input
                      label="City"
                      placeholder=""
                      contStyle={{ flex: 1, marginRight: 8, marginTop: 16 }}
                      value={formikProps.values.city}
                      onChangeText={formikProps.handleChange("city")}
                      onBlur={formikProps.handleBlur("city")}
                      error={
                        !!(formikProps.errors.city && formikProps.touched.city)
                      }
                      errorText={formikProps.errors.city}
                    />

                    <Input
                      label="State"
                      placeholder=""
                      contStyle={{ flex: 1, marginLeft: 8, marginTop: 16 }}
                      value={formikProps.values.state}
                      onChangeText={formikProps.handleChange("state")}
                      onBlur={formikProps.handleBlur("state")}
                      error={
                        !!(
                          formikProps.errors.state && formikProps.touched.state
                        )
                      }
                      errorText={formikProps.errors.state}
                    />
                  </View>
                </View>

                {/* Update/Add Button */}
                <View style={styles.buttonContainer}>
                  <Button
                    btnText={isEditing ? "Update Address" : "Add Address"}
                    onPress={formikProps.handleSubmit}
                    loading={loading}
                    style={styles.submitButton}
                  />
                </View>
              </ScrollView>
            )}
          </Formik>
        </KeyboardAvoidingView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "100%",
  },
  contentContainer: {
    flexGrow: 1,
    paddingHorizontal: 16,
    paddingBottom: 100, // Space for button
  },
  formContainer: {
    width: "100%",
    marginTop: 20,
  },
  rowContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
  buttonContainer: {
    position: "absolute",
    bottom: 20,
    left: 0,
    right: 0,
    paddingHorizontal: 16,
  },
  submitButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
  },
});
