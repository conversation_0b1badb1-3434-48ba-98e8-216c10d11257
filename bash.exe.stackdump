Stack trace:
Frame         Function      Args
0007FFFFB6A0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFB6A0, 0007FFFFA5A0) msys-2.0.dll+0x2118E
0007FFFFB6A0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6A0  0002100469F2 (00021028DF99, 0007FFFFB558, 0007FFFFB6A0, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6A0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB6A0  00021006A545 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF957650000 ntdll.dll
7FF9555D0000 KERNEL32.DLL
7FF954BC0000 KERNELBASE.dll
7FF9557B0000 USER32.dll
7FF954850000 win32u.dll
7FF9555A0000 GDI32.dll
7FF954A20000 gdi32full.dll
7FF954FA0000 msvcp_win.dll
7FF954900000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF957550000 advapi32.dll
7FF955320000 msvcrt.dll
7FF956A40000 sechost.dll
7FF955040000 bcrypt.dll
7FF955BF0000 RPCRT4.dll
7FF953F30000 CRYPTBASE.DLL
7FF954B40000 bcryptPrimitives.dll
7FF955A50000 IMM32.DLL
