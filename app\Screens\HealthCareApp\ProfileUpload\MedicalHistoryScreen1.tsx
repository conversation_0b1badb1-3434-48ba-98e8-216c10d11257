import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import AppHeader from "../../../Components/AppHeader";
import SelectInput from "../../../Components/SelectInput";
import Input from "../../../Components/Input";
import {
  GetDrugAllergies,
  GetChronicIllnesses,
  GetCognitiveConditions,
  GetPhysicalDisabilities,
  GetDrugReactions,
} from "../../../RequestHandler.tsx/Auth";
import { UpdateMedicalHistory } from "../../../RequestHandler.tsx/User";
import { useToast } from "../../../Context/ToastContext";
import NetworkErrorView from "../../../Components/NetworkErrorView";
import {
  getErrorType,
  getErrorMessage,
} from "../../../utils/networkErrorHandler";

// Severity level options removed as per owner's request

// Substances options - Commented out as not needed for now
/* const substancesOptions = [
  { label: "Latex", value: "latex" },
  { label: "Pollen", value: "pollen" },
  { label: "Dust Mites", value: "dust_mites" },
  { label: "Animal Dander", value: "animal_dander" },
  { label: "Mold", value: "mold" },
  { label: "Food Allergens", value: "food" },
]; */

// No longer needed - removed

export default function MedicalHistoryScreen1({ navigation }) {
  // Toast context for showing notifications
  const { handleToast } = useToast();
  // State for all selections - simplified drug allergies to text input
  const [drugAllergies, setDrugAllergies] = useState<string>("");
  const [reactionType, setReactionType] = useState<string[]>([]);
  const [reactionTypeIds, setReactionTypeIds] = useState<string[]>([]);
  // Substances state - Commented out as not needed for now
  // const [substances, setSubstances] = useState<string[]>([]);
  const [physicalDisabilities, setPhysicalDisabilities] = useState<string[]>(
    []
  );
  const [physicalDisabilityIds, setPhysicalDisabilityIds] = useState<string[]>(
    []
  );
  const [cognitiveConditions, setCognitiveConditions] = useState<string[]>([]);
  const [cognitiveConditionIds, setCognitiveConditionIds] = useState<string[]>(
    []
  );
  const [chronicIllnesses, setChronicIllnesses] = useState<string[]>([]);
  const [chronicIllnessIds, setChronicIllnessIds] = useState<string[]>([]);

  // State for API options (drug allergen options removed)
  const [physicalDisabilityOptions, setPhysicalDisabilityOptions] = useState<
    any[]
  >([]);
  const [cognitiveConditionOptions, setCognitiveConditionOptions] = useState<
    any[]
  >([]);
  const [chronicIllnessesOptions, setChronicIllnessesOptions] = useState<any[]>(
    []
  );
  const [reactionTypeOptions, setReactionTypeOptions] = useState<any[]>([]);

  // Loading and error states (drug allergies loading removed)
  const [loadingPhysical, setLoadingPhysical] = useState<boolean>(true);
  const [loadingCognitive, setLoadingCognitive] = useState<boolean>(true);
  const [loadingIllnesses, setLoadingIllnesses] = useState<boolean>(true);
  const [loadingReactions, setLoadingReactions] = useState<boolean>(true);
  const [submitting, setSubmitting] = useState<boolean>(false);

  // Network error state
  const [networkError, setNetworkError] = useState<any>(null);
  const [showNetworkError, setShowNetworkError] = useState<boolean>(false);

  const [physicalError, setPhysicalError] = useState<string>(""); // Drug allergy error removed
  const [cognitiveError, setCognitiveError] = useState<string>("");
  const [illnessError, setIllnessError] = useState<string>("");
  const [reactionError, setReactionError] = useState<string>("");

  // Function to handle network errors
  const handleNetworkError = (error: any) => {
    setNetworkError(error);
    setShowNetworkError(true);
  };

  // Function to retry all API calls
  const handleRetryAllApiCalls = () => {
    setShowNetworkError(false);
    setNetworkError(null);

    // Retry all API calls (drug allergies removed)
    fetchChronicIllnesses();
    fetchCognitiveConditions();
    fetchPhysicalDisabilities();
    fetchDrugReactions();
  };

  // Drug allergies fetch removed - now using text input

  // Drug allergies fetch removed - now using text input

  // Fetch chronic illnesses from API
  const fetchChronicIllnesses = async () => {
    try {
      setLoadingIllnesses(true);
      setIllnessError("");
      const response = await GetChronicIllnesses();

      if (response && Array.isArray(response)) {
        // Transform API response to options format
        const options = response.map((item) => ({
          label: item.name,
          value: item.name,
          id: item.id,
        }));

        setChronicIllnessesOptions(options);
      } else {
        setIllnessError("Failed to load chronic illnesses");
      }
    } catch (error) {
      console.log("Error fetching chronic illnesses:", error);
      setIllnessError("Failed to load chronic illnesses");

      // Check if it's a network error
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        handleNetworkError(error);
      }
    } finally {
      setLoadingIllnesses(false);
    }
  };

  // Call fetchChronicIllnesses on component mount
  useEffect(() => {
    fetchChronicIllnesses();
  }, []);

  // Fetch cognitive conditions from API
  const fetchCognitiveConditions = async () => {
    try {
      setLoadingCognitive(true);
      setCognitiveError("");
      const response = await GetCognitiveConditions();

      if (response && Array.isArray(response)) {
        // Transform API response to options format
        const options = response.map((item) => ({
          label: item.name,
          value: item.name,
          id: item.id,
        }));

        setCognitiveConditionOptions(options);
      } else {
        setCognitiveError("Failed to load cognitive conditions");
      }
    } catch (error) {
      console.log("Error fetching cognitive conditions:", error);
      setCognitiveError("Failed to load cognitive conditions");

      // Check if it's a network error
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        handleNetworkError(error);
      }
    } finally {
      setLoadingCognitive(false);
    }
  };

  // Call fetchCognitiveConditions on component mount
  useEffect(() => {
    fetchCognitiveConditions();
  }, []);

  // Fetch physical disabilities from API
  const fetchPhysicalDisabilities = async () => {
    try {
      setLoadingPhysical(true);
      setPhysicalError("");
      const response = await GetPhysicalDisabilities();

      if (response && Array.isArray(response)) {
        // Transform API response to options format
        const options = response.map((item) => ({
          label: item.name,
          value: item.name,
          id: item.id,
        }));

        setPhysicalDisabilityOptions(options);
      } else {
        setPhysicalError("Failed to load physical disabilities");
      }
    } catch (error) {
      console.log("Error fetching physical disabilities:", error);
      setPhysicalError("Failed to load physical disabilities");

      // Check if it's a network error
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        handleNetworkError(error);
      }
    } finally {
      setLoadingPhysical(false);
    }
  };

  // Call fetchPhysicalDisabilities on component mount
  useEffect(() => {
    fetchPhysicalDisabilities();
  }, []);

  // Fetch drug reactions from API
  const fetchDrugReactions = async () => {
    try {
      setLoadingReactions(true);
      setReactionError("");
      const response = await GetDrugReactions();

      if (response && Array.isArray(response)) {
        // Transform API response to options format
        const options = response.map((item) => ({
          label: item.name,
          value: item.name,
          id: item.id,
        }));

        setReactionTypeOptions(options);
      } else {
        setReactionError("Failed to load drug reactions");
      }
    } catch (error) {
      console.log("Error fetching drug reactions:", error);
      setReactionError("Failed to load drug reactions");

      // Check if it's a network error
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        handleNetworkError(error);
      }
    } finally {
      setLoadingReactions(false);
    }
  };

  // Call fetchDrugReactions on component mount
  useEffect(() => {
    fetchDrugReactions();
  }, []);

  // Drug allergen handlers removed - now using text input

  // Handle chronic illness selection with IDs
  const handleChronicIllnessSelect = (values: string[]) => {
    setChronicIllnesses(values);

    // Find and store the IDs of selected chronic illnesses
    const selectedIds = values
      .map((value) => {
        const option = chronicIllnessesOptions.find(
          (opt) => opt.value === value
        );
        return option ? option.id : null;
      })
      .filter((id) => id !== null);

    setChronicIllnessIds(selectedIds);
  };

  // Handle cognitive condition selection with IDs
  const handleCognitiveConditionSelect = (values: string[]) => {
    setCognitiveConditions(values);

    // Find and store the IDs of selected cognitive conditions
    const selectedIds = values
      .map((value) => {
        const option = cognitiveConditionOptions.find(
          (opt) => opt.value === value
        );
        return option ? option.id : null;
      })
      .filter((id) => id !== null);

    setCognitiveConditionIds(selectedIds);
  };

  // Handle physical disability selection with IDs
  const handlePhysicalDisabilitySelect = (values: string[]) => {
    setPhysicalDisabilities(values);

    // Find and store the IDs of selected physical disabilities
    const selectedIds = values
      .map((value) => {
        const option = physicalDisabilityOptions.find(
          (opt) => opt.value === value
        );
        return option ? option.id : null;
      })
      .filter((id) => id !== null);

    setPhysicalDisabilityIds(selectedIds);
  };

  // Handle reaction type selection with IDs
  const handleReactionTypeSelect = (values: string[]) => {
    setReactionType(values);

    // Find and store the IDs of selected reaction types
    const selectedIds = values
      .map((value) => {
        const option = reactionTypeOptions.find((opt) => opt.value === value);
        return option ? option.id : null;
      })
      .filter((id) => id !== null);

    setReactionTypeIds(selectedIds);
  };

  const handleContinue = async () => {
    try {
      setSubmitting(true);
      const requestBody = {
        drugAllergies: drugAllergies, // Simple text field
        drugReactions: reactionTypeIds,
        cognitiveConditions: cognitiveConditionIds,
        disabilities: physicalDisabilityIds,
        illnesses: chronicIllnessIds,
      };
      console.log("Sending medical history to API:", requestBody);
      const response = await UpdateMedicalHistory(requestBody);
      if (response.active) {
        handleToast("Medical history updated successfully", "success");
        navigation.navigate("MedicalHistoryScreen2");
      }
    } catch (error) {
      console.error("Error updating medical history:", error);
      // Check if it's a network error
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        handleNetworkError(error);
      } else {
        handleToast(
          error?.message || "Failed to update medical history",
          "error"
        );
      }
    } finally {
      setSubmitting(false);
    }
  };

  // If there's a network error, show the NetworkErrorView
  if (showNetworkError) {
    return (
      <NetworkErrorView
        errorType={getErrorType(networkError)}
        message={getErrorMessage(networkError)}
        onRetry={handleRetryAllApiCalls}
      />
    );
  }

  return (
    <View style={styles.mainContainer}>
      <Div>
        <View style={styles.container}>
          <ScrollView
            style={{ width: "100%" }}
            contentContainerStyle={{ paddingBottom: 120 }} // Increased padding to account for fixed button
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
          >
            {/* Header */}
            <View style={{ width: "100%" }}>
              <AppHeader navigation={navigation} />
            </View>

            {/* Title */}
            <H4 style={styles.title}>Medical History</H4>

            {/* Allergies Section */}
            <View style={styles.section}>
              <H4 style={styles.sectionTitle}>Allergies</H4>

              {/* Drug Allergens */}
              <Input
                label="Drug Allergies"
                placeholder="Enter drug allergies (e.g., Penicillin, Aspirin, etc.)"
                value={drugAllergies}
                onChangeText={setDrugAllergies}
                contStyle={styles.selectInput}
                multiline={true}
                numberOfLines={3}
                textAlignVertical="top"
              />

              {/* Reaction Type */}
              {loadingReactions ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="small" color={colors.primary} />
                </View>
              ) : reactionError ? (
                <View style={styles.errorContainer}>
                  <Text style={styles.errorText}>{reactionError}</Text>
                </View>
              ) : (
                <SelectInput
                  label="Reaction Type"
                  placeholder="Select"
                  options={reactionTypeOptions}
                  selectedValues={reactionType}
                  onSelect={handleReactionTypeSelect}
                  multiSelect={true}
                  maxSelections={5}
                  searchPlaceholder="Search for Reaction Types"
                  contStyle={styles.selectInput}
                />
              )}

              {/* Severity levels removed as per owner's request */}

              {/* Substances - Commented out as not needed for now
              <SelectInput
                label="Substances"
                placeholder="Select"
                options={substancesOptions}
                selectedValues={substances}
                onSelect={setSubstances}
                multiSelect={true}
                maxSelections={10}
                contStyle={styles.selectInput}
              /> */}
            </View>

            {/* Chronic Conditions Section */}
            <View style={styles.section}>
              <H4 style={styles.sectionTitle}>Chronic Conditions</H4>

              {/* Physical Disabilities */}
              {loadingPhysical ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="small" color={colors.primary} />
                </View>
              ) : physicalError ? (
                <View style={styles.errorContainer}>
                  <Text style={styles.errorText}>{physicalError}</Text>
                </View>
              ) : (
                <SelectInput
                  label="Physical Disabilities"
                  placeholder="Select"
                  options={physicalDisabilityOptions}
                  selectedValues={physicalDisabilities}
                  onSelect={handlePhysicalDisabilitySelect}
                  multiSelect={true}
                  maxSelections={5}
                  contStyle={styles.selectInput}
                />
              )}

              {/* Cognitive Conditions and Chronic Illnesses in a row */}
              <View style={styles.rowContainer}>
                <View style={styles.halfColumn}>
                  {loadingCognitive ? (
                    <View style={styles.loadingContainerSmall}>
                      <ActivityIndicator size="small" color={colors.primary} />
                    </View>
                  ) : cognitiveError ? (
                    <View style={styles.errorContainerSmall}>
                      <Text style={styles.errorTextSmall}>
                        {cognitiveError}
                      </Text>
                    </View>
                  ) : (
                    <SelectInput
                      label="Cognitive conditions"
                      placeholder="Select"
                      options={cognitiveConditionOptions}
                      selectedValues={cognitiveConditions}
                      onSelect={handleCognitiveConditionSelect}
                      multiSelect={true}
                      maxSelections={3}
                      contStyle={styles.selectInput}
                    />
                  )}
                </View>

                <View style={styles.halfColumn}>
                  {loadingIllnesses ? (
                    <View style={styles.loadingContainerSmall}>
                      <ActivityIndicator size="small" color={colors.primary} />
                    </View>
                  ) : illnessError ? (
                    <View style={styles.errorContainerSmall}>
                      <Text style={styles.errorTextSmall}>{illnessError}</Text>
                    </View>
                  ) : (
                    <SelectInput
                      label="Chronic Illnesses"
                      placeholder="Select"
                      options={chronicIllnessesOptions}
                      selectedValues={chronicIllnesses}
                      onSelect={handleChronicIllnessSelect}
                      multiSelect={true}
                      maxSelections={5}
                      contStyle={styles.selectInput}
                    />
                  )}
                </View>
              </View>
            </View>
          </ScrollView>

          {/* Continue Button - Fixed at bottom */}
          <View style={styles.buttonContainer}>
            <Button
              btnText={submitting ? "Saving..." : "Continue"}
              onPress={handleContinue}
              disabled={submitting}
              loading={submitting}
            />
          </View>
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "90%",
    height: "100%",
    backgroundColor: colors.white,
    alignItems: "center",
  },
  title: {
    fontSize: 18,
    fontFamily: fonts.dmSansBold,
    marginTop: 16,
    marginBottom: 24,
    alignSelf: "flex-start",
  },
  section: {
    width: "100%",
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: fonts.dmSansBold,
    marginBottom: 16,
  },
  fieldLabel: {
    fontSize: 12,
    fontFamily: fonts.dmSansRegular,
    marginBottom: 8,
  },
  selectInput: {
    marginBottom: 16,
  },
  rowContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
  halfColumn: {
    width: "48%",
  },
  severitySection: {
    marginTop: 16,
    padding: 16,
    backgroundColor: "#F8F9FA",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#E9ECEF",
  },
  severityTitle: {
    fontSize: 12,
    fontFamily: fonts.dmSansBold,
    color: colors.black,
    marginBottom: 12,
  },
  severityItem: {
    marginBottom: 8,
  },
  buttonContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    width: "100%",
    paddingHorizontal: 20,
    paddingBottom: 20,
    paddingTop: 10,
    backgroundColor: colors.white,
  },
  // Loading and error styles
  loadingContainer: {
    padding: 16,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#F5F5F5", // Light gray background
    borderRadius: 8,
    marginBottom: 16,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 12,
    fontFamily: fonts.dmSansRegular,
    color: "#757575", // Dark gray text
  },
  errorContainer: {
    padding: 16,
    backgroundColor: "#FFEBEE", // Light red background
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 12,
    fontFamily: fonts.dmSansRegular,
    color: "#D32F2F", // Red text
  },
  // Small loading and error styles for half columns
  loadingContainerSmall: {
    padding: 8,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#F5F5F5", // Light gray background
    borderRadius: 8,
    marginBottom: 16,
    height: 80, // Fixed height to match the SelectInput
  },
  loadingTextSmall: {
    marginTop: 4,
    fontSize: 12,
    fontFamily: fonts.dmSansRegular,
    color: "#757575", // Dark gray text
  },
  errorContainerSmall: {
    padding: 8,
    backgroundColor: "#FFEBEE", // Light red background
    borderRadius: 8,
    marginBottom: 16,
    height: 80, // Fixed height to match the SelectInput
  },
  errorTextSmall: {
    fontSize: 12,
    fontFamily: fonts.dmSansRegular,
    color: "#D32F2F", // Red text
  },
});
