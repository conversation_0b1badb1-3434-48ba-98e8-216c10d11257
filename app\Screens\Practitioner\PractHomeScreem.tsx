import React, { useContext, useEffect, useState } from "react";
import {
  Dimensions,
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
  ScrollView,
  ImageBackground,
  RefreshControl,
} from "react-native";
import { colors } from "../../Config/colors";
import Div from "../../Components/Div";
import H4 from "../../Components/H4";
import P from "../../Components/P";
import { fonts } from "../../Config/Fonts";
import Button from "../../Components/Button";
import Icon from "react-native-vector-icons/Ionicons";
import { SvgXml } from "react-native-svg";
import { svg } from "../../Config/Svg";
import { clearLogin } from "../../utils/ClearLogin";
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { CredentailsContext } from "../../Context/CredentailsContext";
import { useToast } from "../../Context/ToastContext";
import {
  GetPractIdentity,
  GetProssionalDetails,
  GetUserProfile,
} from "../../RequestHandler.tsx/User";
import { getErrorMessage, getErrorType } from "../../utils/networkErrorHandler";
import NetworkErrorView from "../../Components/NetworkErrorView";
import Loader from "../../Components/Loader";

const { width, height } = Dimensions.get("window");
const MAX_WIDTH = 500;

export default function PractHomeScreen({ navigation }) {
  const { storedCredentails, setStoredCredentails } =
    useContext(CredentailsContext);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [networkError, setNetworkError] = useState<any>(null);
  const [showNetworkError, setShowNetworkError] = useState(false);
  const [userProfile, setUserProfile] = useState<any>(null);
  const [name, setName] = useState("....");
  const [avatar, setAvatar] = useState(null);
  const { handleToast } = useToast();
  const getUserProfile = async () => {
    setLoading(true);
    setNetworkError(null);
    setShowNetworkError(false);
    try {
      const res = await GetUserProfile();
      const pRes = await GetProssionalDetails();
      const ires = await GetPractIdentity();
      console.log("res", res);
      console.log("p", pRes);
      console.log("i", ires);
      setUserProfile(res);
      setName(`${res.firstname} ${res.lastname}`);
      setAvatar(res?.avatar);
      if (res.accountSetup === false) {
        navigation.navigate("UploadDoctorDetailsScreen1", {
          avatar: res.avatar,
        });
      } else if (pRes?.data === null) {
        navigation.navigate("VerifyQualificationsScreen");
      } else if (ires?.data === null) {
        navigation.navigate("IdentificationScreen");
      } else if (pRes?.data?.about === null) {
        navigation.navigate("ProfileDetailsScreen");
      } else if (pRes?.data?.languageFluencies.length === 0) {
        navigation.navigate("LanguageDetailsScreen");
      }
    } catch (error) {
      console.log("Error fetching user profile:", error);
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        setNetworkError(error);
        setShowNetworkError(true);
      } else {
        handleToast("Failed to load profile. Please try again.", "error");
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Handle pull-to-refresh
  const onRefresh = () => {
    setRefreshing(true);
    getUserProfile();
  };

  // Handle retry when network error occurs
  const handleRetry = () => {
    setShowNetworkError(false);
    getUserProfile();
  };

  useEffect(() => {
    setLoading(true);
    getUserProfile();
  }, []);

  // If there's a network error, show the NetworkErrorView
  if (showNetworkError) {
    return (
      <NetworkErrorView
        errorType={getErrorType(networkError)}
        message={getErrorMessage(networkError)}
        onRetry={handleRetry}
      />
    );
  }
  return (
    <View style={styles.mainContainer}>
      <Div>
        <ScrollView
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.scrollContainer}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
        >
          {/* Header Section */}
          <View style={styles.headerContainer}>
            <View style={styles.headerContent}>
              <View>
                <View style={styles.locationRow}>
                  <Icon
                    name="location-outline"
                    size={18}
                    color={colors.primary}
                  />
                  <P style={styles.locationText}>Nsukka, Enugu</P>
                </View>
                <H4 style={styles.userName}>{name}</H4>
                <View style={styles.taglineRow}>
                  <P style={styles.taglineText}>An apple a day... </P>
                  <Text style={styles.appleEmoji}>🍎</Text>
                </View>
              </View>
              <View style={styles.headerIcons}>
                <TouchableOpacity style={styles.iconButton}>
                  <SvgXml xml={svg.mail} />
                </TouchableOpacity>
                <TouchableOpacity style={styles.iconButton}>
                  <SvgXml xml={svg.qrCode} />
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* Balance Card */}
          <View style={styles.balanceCardContainer}>
            <ImageBackground
              style={styles.balanceCardBackground}
              source={require("../../assets/cardBg.png")}
              resizeMode="cover"
            >
              <View style={styles.balanceCardContent}>
                <View style={styles.avatarContainer}>
                  <Image
                    source={
                      avatar !== null
                        ? { uri: avatar }
                        : require("../../assets/avatar.png")
                    }
                    style={styles.avatarImage}
                  />
                </View>
                <P style={styles.balanceLabel}>Your available balance is</P>
                <Text style={styles.balanceAmount}>NGN 0.00</Text>
                <P style={styles.noEarningsText}>
                  No earning to account for yet
                </P>
                <TouchableOpacity style={styles.nextButton}>
                  <SvgXml xml={svg.purpleArrow} />
                </TouchableOpacity>
              </View>
            </ImageBackground>
          </View>

          {/* Calendar Section */}
          <View style={styles.calendarContainer}>
            <H4 style={styles.sectionTitle}>Calendar</H4>
            <View
              style={{
                width: "100%",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <TouchableOpacity style={styles.createAvailabilityButton}>
                <Text style={styles.createAvailabilityText}>
                  Create Availability
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Bookings Section */}
          <View style={styles.bookingsContainer}>
            <H4 style={styles.sectionTitle}>Bookings</H4>
            <View style={styles.bookingsContent}>
              <View style={styles.calendarImageContainer}>
                <Image
                  source={require("../../assets/calender.png")}
                  style={styles.calendarImage}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </Div>
      {loading && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: 100,
  },
  headerContainer: {
    width: "100%",
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  headerContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    width: "100%",
  },
  locationRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 5,
  },
  locationText: {
    color: colors.primary,
    marginLeft: 5,
    fontSize: 14,
    fontFamily: fonts.dmSansSemibold,
  },
  userName: {
    fontSize: 18,
    fontFamily: fonts.dmSansBold,
    marginBottom: 4,
  },
  taglineRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  taglineText: {
    fontSize: 14,
    color: colors.black,
    fontFamily: fonts.dmSansRegular,
  },
  appleEmoji: {
    fontSize: 14,
  },
  headerIcons: {
    flexDirection: "row",
    alignItems: "center",
  },
  iconButton: {
    marginLeft: 15,
    width: 40,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
  },
  balanceCardContainer: {
    marginTop: 24,
    width: "90%",
    alignSelf: "center",
    borderRadius: 25,
    overflow: "hidden",
  },
  balanceCardBackground: {
    width: "100%",
    minHeight: 236,
    justifyContent: "center",
    position: "relative",
  },
  balanceCardContent: {
    padding: 20,
    alignItems: "center",
    position: "relative",
  },
  avatarContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "#fff",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 10,
    overflow: "hidden",
  },
  avatarImage: {
    width: "100%",
    height: "100%",
    borderRadius: 25,
  },
  balanceLabel: {
    color: colors.white,
    fontSize: 11,
    fontFamily: fonts.dmSansMedium,
    marginBottom: 5,
  },
  balanceAmount: {
    color: colors.white,
    fontSize: 28,
    fontFamily: fonts.dmSansBold,
    marginBottom: 5,
  },
  noEarningsText: {
    color: colors.white,
    fontSize: 11,
    fontFamily: fonts.dmSansMedium,
  },
  nextButton: {
    position: "absolute",
    right: 30,
    top: 30,
    width: 30,
    height: 30,
    justifyContent: "center",
    alignItems: "center",
    marginTop: -15,
    backgroundColor: colors.primary,
    borderRadius: 100,
  },
  calendarContainer: {
    marginTop: 28,
    width: "90%",
    alignSelf: "center",
    marginBottom: 14,
  },
  sectionTitle: {
    fontSize: 14,
    fontFamily: fonts.dmSansBold,
    marginBottom: 15,
  },
  createAvailabilityButton: {
    backgroundColor: "#D9EBFF",
    borderRadius: 16,
    paddingVertical: 10,
    paddingHorizontal: 16,
  },
  createAvailabilityText: {
    color: colors.deepBlue,
    fontFamily: fonts.dmSansMedium,
    fontSize: 14,
  },
  bookingsContainer: {
    marginTop: 28,
    marginHorizontal: 20,
  },
  bookingsContent: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 20,
  },
  calendarImageContainer: {
    alignItems: "center",
  },
  calendarImage: {
    width: 250,
    height: 250,
  },
});
