import React, { useState } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  ScrollView,
  StatusBar,
} from "react-native";
import { colors } from "../../../Config/colors";
import { fonts } from "../../../Config/Fonts";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import Button from "../../../Components/Button";
import AppHeader from "../../../Components/AppHeader";
import { useToast } from "../../../Context/ToastContext";
import TabSelector from "../../../Components/profile/ProfileTabSelector";
import { tabs } from "./tabs";
import Div from "../../../Components/Div";
import { UpdateMedicalHistory } from "../../../RequestHandler.tsx/User";
import Loader from "../../../Components/Loader";
import NetworkErrorView from "../../../Components/NetworkErrorView";
import { getErrorType, getErrorMessage } from "../../../utils/networkErrorHandler";

const { width, height } = Dimensions.get("window");

// Blood group options
const bloodGroupOptions = [
  { id: 1, label: "A+", value: "A+" },
  { id: 2, label: "A-", value: "A-" },
  { id: 3, label: "B+", value: "B+" },
  { id: 4, label: "B-", value: "B-" },
  { id: 5, label: "AB+", value: "AB+" },
  { id: 6, label: "AB-", value: "AB-" },
  { id: 7, label: "O+", value: "O+" },
  { id: 8, label: "O-", value: "O-" },
];

export default function BloodGroupScreen({ navigation, route }) {
  // Get current blood group from route params if available
  const { currentValue, onSave } = route.params || {
    currentValue: "",
    onSave: null,
  };

  const [selectedBloodGroup, setSelectedBloodGroup] = useState(
    currentValue || ""
  );
  const [activeTab, setActiveTab] = useState("PERSONAL");
  const [loading, setLoading] = useState(false);
  const [networkError, setNetworkError] = useState<any>(null);
  const [showNetworkError, setShowNetworkError] = useState<boolean>(false);
  const { handleToast } = useToast();

  // Function to handle network errors
  const handleNetworkError = (error: any) => {
    setNetworkError(error);
    setShowNetworkError(true);
  };

  // Function to retry after network error
  const handleRetry = () => {
    setShowNetworkError(false);
    setNetworkError(null);
    handleSaveChanges();
  };

  // Handle tab change
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  // Handle blood group selection
  const handleBloodGroupSelect = (value: string) => {
    setSelectedBloodGroup(value);
  };

  // Handle save changes
  const handleSaveChanges = async () => {
    if (!selectedBloodGroup) {
      handleToast("Please select a blood group", "error");
      return;
    }

    setLoading(true);

    try {
      // Prepare the request body
      const requestBody = {
        bloodGroup: selectedBloodGroup
      };
      console.log("Updating blood group:", requestBody);
      // Call the API to update medical history
      const response = await UpdateMedicalHistory(requestBody);
      console.log("Update response:", response);
      // Call the onSave callback if provided
      if (onSave) {
        onSave(selectedBloodGroup);
      }
      handleToast("Blood group updated successfully", "success");
      // Navigate back to profile screen
      navigation.pop();
    } catch (error) {
      console.error("Error updating blood group:", error);
      // Check if it's a network error
      if (getErrorType(error) === 'network' || getErrorType(error) === 'timeout') {
        handleNetworkError(error);
      } else {
        handleToast("Failed to update blood group. Please try again.", "error");
      }
    } finally {
      setLoading(false);
    }
  };

  // If there's a network error, show the NetworkErrorView
  if (showNetworkError) {
    return (
      <NetworkErrorView
        errorType={getErrorType(networkError)}
        message={getErrorMessage(networkError)}
        onRetry={handleRetry}
      />
    );
  }

  return (
    <View style={styles.container}>
      <Div>
        {/* Header */}
        <View style={styles.headerContainer}>
          <AppHeader
            navigation={navigation}
            showBackButton={true}
            title={"Henry Osuji"}
            navStyle={{
              justifyContent: "flex-start",
              alignItems: "flex-start",
            }}
            subtitle={`20% profile completed`}
            subtitleStyle={styles.completionText}
          />
        </View>

        {/* Tabs */}
        {/* <TabSelector
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={handleTabChange}
        /> */}

        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
        >
          <View style={styles.contentContainer}>
            {/* Title */}
            <H4 style={styles.title}>Add Blood Group</H4>

            {/* Blood Group Options */}
            <View style={styles.optionsContainer}>
              {bloodGroupOptions.map((option) => (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.optionButton,
                    selectedBloodGroup === option.value &&
                      styles.selectedOption,
                  ]}
                  onPress={() => handleBloodGroupSelect(option.value)}
                >
                  <P
                    style={[
                      styles.optionText,
                      selectedBloodGroup === option.value &&
                        styles.selectedOptionText,
                    ]}
                  >
                    {option.label}
                  </P>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </ScrollView>

        {/* Save Button */}
        <View style={styles.buttonContainer}>
          <Button
            btnText="Save changes"
            onPress={handleSaveChanges}
            loading={loading}
            style={styles.saveButton}
          />
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerContainer: {
    width: "100%",
    paddingHorizontal: 20,
  },
  completionText: {
    color: "#D4AF37", // Gold color for completion percentage
    fontSize: 14,
    fontFamily: fonts.dmSansRegular,
    marginTop: 4,
  },
  scrollView: {
    flex: 1,
    width: "100%",
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingTop: (5 * height) / 100,
    paddingBottom: 100,
  },
  title: {
    fontSize: 24,
    fontFamily: fonts.dmSansBold,
    color: colors.black,
    marginBottom: (2.8 * height) / 100,
    textAlign: "center",
  },
  optionsContainer: {
    marginTop: 10,
  },
  optionButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
    borderWidth: 1,
    borderColor: colors.b2Brown,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
    backgroundColor: colors.white,
  },
  selectedOption: {
    backgroundColor: colors.b2Brown,
  },
  optionText: {
    fontSize: 14,
    fontFamily: fonts.dmSansMedium,
    color: colors.b2Brown,
  },
  selectedOptionText: {
    color: colors.white,
  },
  buttonContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
    paddingBottom: 28,
    backgroundColor: colors.white,
  },
  saveButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
  },
});
