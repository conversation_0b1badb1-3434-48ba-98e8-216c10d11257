import React, { useState } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  ScrollView,
  StatusBar,
} from "react-native";
import { colors } from "../../../Config/colors";
import { fonts } from "../../../Config/Fonts";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import Button from "../../../Components/Button";
import AppHeader from "../../../Components/AppHeader";
import { useToast } from "../../../Context/ToastContext";
import { CredentailsContext } from "../../../Context/CredentailsContext";
import { useContext } from "react";
import TabSelector from "../../../Components/profile/ProfileTabSelector";
import { tabs } from "./tabs";
import { UpdateMedicalHistory } from "../../../RequestHandler.tsx/User";
import Loader from "../../../Components/Loader";
import NetworkErrorView from "../../../Components/NetworkErrorView";
import { getErrorType, getErrorMessage } from "../../../utils/networkErrorHandler";

const { width, height } = Dimensions.get("window");

// Marital status options
const maritalStatusOptions = [
  { id: 1, label: "Single", value: "single" },
  { id: 2, label: "Widowed", value: "widowed" },
  { id: 3, label: "Divorced", value: "divorced" },
  { id: 4, label: "Married", value: "married" },
];

export default function MaritalStatusScreen({ navigation, route }) {
  const [selectedMaritalStatus, setSelectedMaritalStatus] = useState("");
  const [activeTab, setActiveTab] = useState("PERSONAL");
  const [loading, setLoading] = useState(false);
  const [networkError, setNetworkError] = useState<any>(null);
  const [showNetworkError, setShowNetworkError] = useState<boolean>(false);
  const { handleToast } = useToast();

  // Function to handle network errors
  const handleNetworkError = (error: any) => {
    setNetworkError(error);
    setShowNetworkError(true);
  };

  // Function to retry after network error
  const handleRetry = () => {
    setShowNetworkError(false);
    setNetworkError(null);
    handleSaveChanges();
  };
  // Handle tab change
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };
  // Handle marital status selection
  const handleMaritalStatusSelect = (value: string) => {
    setSelectedMaritalStatus(value);
  };

  // Handle save changes
  const handleSaveChanges = async () => {
    if (!selectedMaritalStatus) {
      handleToast("Please select a marital status", "error");
      return;
    }

    setLoading(true);
    try {
      // Prepare the request body
      const requestBody = {
        maritalStatus: selectedMaritalStatus
      };
      console.log("Updating marital status:", requestBody);
      // Call the API to update medical history
      const response = await UpdateMedicalHistory(requestBody);
      console.log("Update response:", response);
      handleToast("Marital status updated successfully", "success");
      // Navigate back to profile screen
      navigation.pop();
    } catch (error) {
      console.error("Error updating marital status:", error);

      // Check if it's a network error
      if (getErrorType(error) === 'network' || getErrorType(error) === 'timeout') {
        handleNetworkError(error);
      } else {
        handleToast("Failed to update marital status. Please try again.", "error");
      }
    } finally {
      setLoading(false);
    }
  };

  // If there's a network error, show the NetworkErrorView
  if (showNetworkError) {
    return (
      <NetworkErrorView
        errorType={getErrorType(networkError)}
        message={getErrorMessage(networkError)}
        onRetry={handleRetry}
      />
    );
  }

  return (
    <View style={styles.container}>
      {loading && <Loader />}
      {/* Header */}
      <View style={styles.headerContainer}>
        <AppHeader
          navigation={navigation}
          showBackButton={true}
          title={"Henry Osuji"}
          navStyle={{ justifyContent: "flex-start", alignItems: "flex-start" }}
          subtitle={`20% profile completed`}
          subtitleStyle={styles.completionText}
        />
      </View>

      {/* Tabs
      <TabSelector
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={(tab) => {
          if (tab == "PERSONAL") {
            navigation.navigate("ProfileSettingsScreen", { activeTab: tab });
          } else if (tab == "MEDICAL") {
            navigation.navigate("ProfileSettingsScreen", { activeTab: tab });
          }
        }}
      /> */}

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
      >
        <View style={styles.contentContainer}>
          {/* Title */}
          <H4 style={styles.title}>Add Marital Status</H4>

          {/* Marital Status Options */}
          <View style={styles.optionsContainer}>
            {maritalStatusOptions.map((option) => (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.optionButton,
                  selectedMaritalStatus === option.value &&
                    styles.selectedOption,
                ]}
                onPress={() => handleMaritalStatusSelect(option.value)}
              >
                <P
                  style={[
                    styles.optionText,
                    selectedMaritalStatus === option.value &&
                      styles.selectedOptionText,
                  ]}
                >
                  {option.label}
                </P>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>

      {/* Save Button */}
      <View style={styles.buttonContainer}>
        <Button
          btnText="Save changes"
          onPress={handleSaveChanges}
          loading={loading}
          style={styles.saveButton}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerContainer: {
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 10,
  },
  completionText: {
    color: "#D4AF37", // Gold color for completion percentage
    fontSize: 14,
    fontFamily: fonts.dmSansRegular,
    marginTop: 4,
  },
  tabsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.stroke || "#E0E0E0",
  },
  tab: {
    paddingVertical: 15,
    flex: 1,
    alignItems: "center",
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  tabText: {
    fontSize: 14,
    fontFamily: fonts.dmSansMedium,
    color: colors.gray1 || "#757575",
  },
  activeTabText: {
    color: colors.primary,
    fontFamily: fonts.dmSansBold,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingTop: (10 * height) / 100,
    paddingBottom: 100,
  },
  title: {
    fontSize: 16,
    fontFamily: fonts.dmSansBold,
    color: colors.black,
    marginBottom: (2.8 * height) / 100,
    textAlign: "center",
  },
  optionsContainer: {
    marginTop: 10,
  },
  optionButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
    borderWidth: 1,
    borderColor: colors.b2Brown,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 18,
    backgroundColor: colors.white,
  },
  selectedOption: {
    backgroundColor: colors.b2Brown,
  },
  optionText: {
    fontSize: 14,
    fontFamily: fonts.dmSansMedium,
    color: colors.b2Brown,
  },
  selectedOptionText: {
    color: colors.white,
  },
  buttonContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
    paddingBottom: 28,
    backgroundColor: colors.white,
  },
  saveButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
  },
});
