import React, { useState, useEffect, useCallback } from "react";
import { StyleSheet, View, ScrollView, TouchableOpacity } from "react-native";
import { colors } from "../../../Config/colors";
import { fonts } from "../../../Config/Fonts";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import Button from "../../../Components/Button";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../Config/Svg";
import TabSelector from "../../../Components/profile/ProfileTabSelector";
import SettingsItem from "../../../Components/profile/ProfileSettingsItem";
import AppHeader from "../../../Components/AppHeader";
import Input from "../../../Components/Input";
import DateInput from "../../../Components/DateInput";
import DropdownInput from "../../../Components/DropdownInput";
import { useToast } from "../../../Context/ToastContext";

import {
  GetUserProfile,
  UpdateUserDetails,
} from "../../../RequestHandler.tsx/User";
import Loader from "../../../Components/Loader";
import NetworkErrorView from "../../../Components/NetworkErrorView";
import {
  getErrorType,
  getErrorMessage,
} from "../../../utils/networkErrorHandler";
import { useFocusEffect } from "@react-navigation/native";
import ProfileMedicalScreen from "../../HealthCareApp/Profile/ProfileMedicalScreen";
import TestReportScreen from "../../HealthCareApp/Profile/TestReportScreen";
import VerifyQualificationsComponent from "./VerifyQualificationsComponent";
import IdentificationComponent from "./IdentificationComponent";


// Gender options for dropdown
const genderOptions = [
  { label: "Male", value: "male" },
  { label: "Female", value: "female" },
];

export default function PractProfileSettingsScreen({ navigation, route }) {
  // Check if we have an activeTab parameter from navigation
  const initialTab = route.params?.activeTab || "PERSONAL";
  const [activeTab, setActiveTab] = useState(initialTab);
  const { handleToast } = useToast();

  // State to track which field is being edited
  const [editingField, setEditingField] = useState(null);

  // Temporary values for editing
  const [tempValue, setTempValue] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");

  // Loading and error states
  const [loading, setLoading] = useState(true);
  const [networkError, setNetworkError] = useState<any>(null);
  const [showNetworkError, setShowNetworkError] = useState<boolean>(false);

  const [profileData, setProfileData] = useState({
    name: "",
    mobile: "",
    gender: "",
    dob: "",
    bloodGroup: "",
    maritalStatus: "",
    height: "",
    weight: "",
    emergencyContact: "",
  });

  // Function to handle network errors
  const handleNetworkError = (error: any) => {
    setNetworkError(error);
    setShowNetworkError(true);
  };

  // Function to retry after network error
  const handleRetry = () => {
    setShowNetworkError(false);
    setNetworkError(null);
    fetchUserProfile();
  };

  // Function to fetch user profile data
  const fetchUserProfile = async () => {
    try {
      setLoading(true);
      const response = await GetUserProfile();
      console.log("User profile data:", response);

      if (response) {
        // Format the data according to our UI structure
        setProfileData({
          name: `${response.firstname || ""} ${response.lastname || ""}`.trim(),
          mobile:
            response.phonenumber !== null
              ? response.phonenumber
              : response.email || "",
          gender: response.gender
            ? response.gender.charAt(0).toUpperCase() +
              response.gender.slice(1).toLowerCase()
            : "",
          dob: response.dob ? response.dob.split("T")[0] : "", // Format date to YYYY-MM-DD
          bloodGroup: response.medicalHistory?.bloodGroup || "",
          maritalStatus:
            response?.medicalHistory?.maritalStatus?.charAt(0)?.toUpperCase() +
              response?.medicalHistory?.maritalStatus
                ?.slice(1)
                ?.toLowerCase() || "",
          height: response.medicalHistory?.height
            ? `${response.medicalHistory.height}${
                response.medicalHistory.heightUnit || ""
              }`.trim()
            : "",
          weight: response.medicalHistory?.weight
            ? `${response.medicalHistory.weight}${
                response.medicalHistory.weightUnit || ""
              }`.trim()
            : "",
          emergencyContact: response.emergencyContact || "",
        });
      }
    } catch (error) {
      console.error("Error fetching user profile:", error);
      // Check if it's a network error
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        handleNetworkError(error);
      } else {
        handleToast("Failed to load profile data. Please try again.", "error");
      }
    } finally {
      setLoading(false);
    }
  };

  // Fetch user profile data on component mount
  useFocusEffect(
    useCallback(() => {
      fetchUserProfile();
    }, [])
  );

  const tabs = ["PERSONAL", "QUALIFICATIONS", "VERIFICATION"];

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  const handleUpdateField = (field: string, value: string) => {
    // If the field is gender, capitalize the first letter
    const formattedValue =
      field === "gender"
        ? value.charAt(0).toUpperCase() + value.slice(1).toLowerCase()
        : value;

    setProfileData({
      ...profileData,
      [field]: formattedValue,
    });
    // Show success toast
    handleToast(
      `${field.charAt(0).toUpperCase() + field.slice(1)} updated successfully`,
      "success"
    );
    // Reset editing state
    setEditingField(null);
  };
  // Start editing a field
  const startEditing = (field: string) => {
    if (field === "name") {
      // Split the name into first name and last name
      const nameParts = profileData.name.split(" ");
      setFirstName(nameParts[0] || "");
      setLastName(nameParts.slice(1).join(" ") || "");
    } else {
      setTempValue(profileData[field]);
    }
    setEditingField(field);
  };

  // Cancel editing
  const cancelEditing = () => {
    setEditingField(null);
  };

  // Handle field validation
  const validateField = (
    field: string,
    value: string
  ): { isValid: boolean; errorMessage: string | null } => {
    if (field === "name" || field === "firstName" || field === "lastName") {
      return {
        isValid: value.trim().length >= 2,
        errorMessage:
          value.trim().length >= 2
            ? null
            : `${
                field === "firstName"
                  ? "First"
                  : field === "lastName"
                  ? "Last"
                  : ""
              } Name must be at least 2 characters`,
      };
    } else if (field === "mobile") {
      // Check if the value is an email or a mobile number
      if (value.includes("@")) {
        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return {
          isValid: emailRegex.test(value),
          errorMessage: emailRegex.test(value)
            ? null
            : "Please enter a valid email address",
        };
      } else {
        // Mobile number validation
        return {
          isValid: /^[0-9+\s-]{10,15}$/.test(value),
          errorMessage: /^[0-9+\s-]{10,15}$/.test(value)
            ? null
            : "Please enter a valid mobile number",
        };
      }
    } else if (field === "dob") {
      return {
        isValid: /^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$/.test(value),
        errorMessage: /^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$/.test(
          value
        )
          ? null
          : "Date must be in YYYY-MM-DD format",
      };
    }
    return { isValid: true, errorMessage: null };
  };

  const navigateToFieldEditor = (field: string, title: string) => {
    // For basic profile fields, use inline editing
    if (["name", "gender", "dob"].includes(field)) {
      startEditing(field);
      return;
    }
    if (field === "mobile") {
      return;
    }
    // Different fields might need different editors
    if (field === "maritalStatus") {
      navigation.navigate("MaritalStatusScreen", {
        currentValue: profileData[field],
        onSave: (value: string) => handleUpdateField(field, value),
      });
    } else if (field === "bloodGroup") {
      navigation.navigate("BloodGroupScreen", {
        currentValue: profileData[field],
        onSave: (value: string) => handleUpdateField(field, value),
      });
    } else if (field === "height") {
      navigation.navigate("HeightScreen", {
        currentValue: profileData[field],
        onSave: (value: string) => handleUpdateField(field, value),
      });
    } else if (field === "weight") {
      navigation.navigate("WeightScreen", {
        currentValue: profileData[field],
        onSave: (value: string) => handleUpdateField(field, value),
      });
    } else {
      // Generic editor for other fields
      navigation.navigate("EditField", {
        field,
        title,
        currentValue: profileData[field],
        onSave: (value: string) => handleUpdateField(field, value),
      });
    }
  };

  const calculateProfileCompletion = () => {
    const totalFields = Object.keys(profileData).length;
    const completedFields = Object.values(profileData).filter(
      (value) => value && value.trim() !== ""
    ).length;

    const percentage = Math.round((completedFields / totalFields) * 100);
    return `${percentage}% profile completed`;
  };

  const handleUpdateChanges = async () => {
    try {
      setLoading(true);

      // Split the name into first name and last name
      const nameParts = profileData.name.split(" ");
      const firstName = nameParts[0] || "";
      const lastName = nameParts.slice(1).join(" ") || "";

      // Prepare the request body
      const requestBody: {
        firstname: string;
        lastname: string;
        dob: string;
        gender: string;
      } = {
        firstname: firstName,
        lastname: lastName,
        dob: profileData.dob,
        gender: profileData.gender.toLowerCase(), // Always send lowercase gender to API
      };
      console.log("Updating profile data:", requestBody);
      const response = await UpdateUserDetails(requestBody);
      console.log("Update response:", response);
      if (response) {
        handleToast("Profile updated successfully", "success");
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      // Check if it's a network error
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        handleNetworkError(error);
      } else {
        handleToast("Failed to update profile. Please try again.", "error");
      }
    } finally {
      setLoading(false);
    }
  };

  // If there's a network error, show the NetworkErrorView
  if (showNetworkError) {
    return (
      <NetworkErrorView
        errorType={getErrorType(networkError)}
        message={getErrorMessage(networkError)}
        onRetry={handleRetry}
      />
    );
  }

  return (
    <View style={styles.mainContainer}>
      <Div>
        {loading && <Loader />}
        {/* Header */}
        <View style={styles.header}>
          <AppHeader
            navigation={navigation}
            showBackButton={true}
            title={profileData.name || "Profile"}
            navStyle={{
              justifyContent: "flex-start",
              alignItems: "flex-start",
            }}
            subtitle={calculateProfileCompletion()}
            subtitleStyle={styles.completionText}
          />
        </View>

        {/* Tab Selector */}
        <TabSelector
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={handleTabChange}
        />
        <ScrollView
          style={styles.container}
          showsVerticalScrollIndicator={false}
        >
          {/* Personal Tab Content */}
          {activeTab === "PERSONAL" && (
            <View style={styles.tabContent}>
              {/* Name Field */}
              {editingField === "name" ? (
                <View style={styles.editingContainer}>
                  <Input
                    label="First Name"
                    placeholder="Enter your first name"
                    value={firstName}
                    onChangeText={setFirstName}
                    error={!validateField("firstName", firstName).isValid}
                    errorText={
                      validateField("firstName", firstName).errorMessage
                    }
                  />
                  <Input
                    contStyle={{ marginTop: 16 }}
                    label="Last Name"
                    placeholder="Enter your last name"
                    value={lastName}
                    onChangeText={setLastName}
                    error={!validateField("lastName", lastName).isValid}
                    errorText={validateField("lastName", lastName).errorMessage}
                  />
                  <View style={styles.editButtonsContainer}>
                    <TouchableOpacity
                      style={[styles.editButton, styles.cancelButton]}
                      onPress={cancelEditing}
                    >
                      <P style={styles.editButtonText}>Cancel</P>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[styles.editButton, styles.saveButton]}
                      onPress={() => {
                        const firstNameValidation = validateField(
                          "firstName",
                          firstName
                        );
                        const lastNameValidation = validateField(
                          "lastName",
                          lastName
                        );
                        if (
                          firstNameValidation.isValid &&
                          lastNameValidation.isValid
                        ) {
                          // Combine first name and last name
                          const fullName = `${firstName} ${lastName}`.trim();
                          handleUpdateField("name", fullName);
                        }
                      }}
                    >
                      <P style={styles.editButtonText}>Save</P>
                    </TouchableOpacity>
                  </View>
                </View>
              ) : (
                <SettingsItem
                  label="Name"
                  value={profileData.name}
                  onPress={() => navigateToFieldEditor("name", "Name")}
                />
              )}

              {/* Email/Mobile Number Field - Read Only */}
              <SettingsItem
                label={
                  profileData.mobile && profileData.mobile.includes("@")
                    ? "Email"
                    : "Mobile Number"
                }
                value={profileData.mobile}
                readOnly={true}
              />

              {/* Gender Field */}
              {editingField === "gender" ? (
                <View style={styles.editingContainer}>
                  <DropdownInput
                    label="Gender"
                    placeholder="Select gender"
                    value={tempValue}
                    options={genderOptions}
                    onSelect={(value) => setTempValue(value)}
                  />
                  <View style={styles.editButtonsContainer}>
                    <TouchableOpacity
                      style={[styles.editButton, styles.cancelButton]}
                      onPress={cancelEditing}
                    >
                      <P style={styles.editButtonText}>Cancel</P>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[styles.editButton, styles.saveButton]}
                      onPress={() => handleUpdateField("gender", tempValue)}
                    >
                      <P style={styles.editButtonText}>Save</P>
                    </TouchableOpacity>
                  </View>
                </View>
              ) : (
                <SettingsItem
                  label="Gender"
                  value={profileData.gender}
                  onPress={() => navigateToFieldEditor("gender", "Gender")}
                />
              )}

              {/* Date of Birth Field */}
              {editingField === "dob" ? (
                <View style={styles.editingContainer}>
                  <DateInput
                    label="Date of Birth"
                    placeholder="YYYY-MM-DD"
                    value={tempValue}
                    onSelect={(date) => setTempValue(date)}
                    error={!validateField("dob", tempValue).isValid}
                    errorText={validateField("dob", tempValue).errorMessage}
                  />
                  <View style={styles.editButtonsContainer}>
                    <TouchableOpacity
                      style={[styles.editButton, styles.cancelButton]}
                      onPress={cancelEditing}
                    >
                      <P style={styles.editButtonText}>Cancel</P>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[styles.editButton, styles.saveButton]}
                      onPress={() => {
                        const validation = validateField("dob", tempValue);
                        if (validation.isValid) {
                          handleUpdateField("dob", tempValue);
                        }
                      }}
                    >
                      <P style={styles.editButtonText}>Save</P>
                    </TouchableOpacity>
                  </View>
                </View>
              ) : (
                <SettingsItem
                  label="Date of Birth"
                  value={profileData.dob}
                  onPress={() => navigateToFieldEditor("dob", "Date of Birth")}
                />
              )}
              <SettingsItem
                label="Language"
                value="Manage Languages"
                placeholder="Add Languages..."
                onPress={() => navigation.navigate("LanguageDetailsScreenEdit")}
              />
            </View>
          )}

          {/* Qualifications Tab Content */}
          {activeTab === "QUALIFICATIONS" && (
            <View style={styles.tabContent}>
              <VerifyQualificationsComponent />
            </View>
          )}

          {/* Verification Tab Content */}
          {activeTab === "VERIFICATION" && (
            <View style={styles.tabContent}>
              <IdentificationComponent />
            </View>
          )}
        </ScrollView>

        {/* Update Changes Button */}
        {activeTab === "PERSONAL" && (
          <View style={styles.buttonContainer}>
            <Button
              btnText="Update Changes"
              onPress={handleUpdateChanges}
              style={styles.updateButton}
            />
          </View>
        )}
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "100%",
    paddingHorizontal: 16,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    paddingHorizontal: 20,
  },
  backButton: {
    padding: 8,
  },
  headerContent: {
    marginLeft: 8,
  },
  headerTitle: {
    fontSize: 16,
    fontFamily: fonts.dmSansBold,
    color: colors.primary || "#007AFF",
  },
  profileCompletion: {
    fontSize: 14,
    fontFamily: fonts.dmSansRegular,
    color: colors.b2Brown,
    marginTop: 4,
  },
  tabContent: {
    paddingBottom: 100, // Space for button
    width: "100%",
  },
  buttonContainer: {
    position: "absolute",
    bottom: 20,
    left: 0,
    right: 0,
    paddingHorizontal: 16,
  },
  updateButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
  },
  completionText: {
    color: "#D4AF37", // Gold color for completion percentage
    fontSize: 14,
    fontFamily: fonts.dmSansRegular,
    marginTop: 4,
  },
  // Inline editing styles
  editingContainer: {
    width: "100%",
    marginBottom: 16,
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  editButtonsContainer: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginTop: 16,
  },
  editButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 4,
    marginLeft: 8,
  },
  cancelButton: {
    backgroundColor: "#F2F2F2",
  },
  saveButton: {
    backgroundColor: colors.b2Brown,
  },
  editButtonText: {
    fontSize: 14,
    fontFamily: fonts.dmSansMedium,
    color: colors.black,
  },
});
