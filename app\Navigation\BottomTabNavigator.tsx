import React from "react";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { StyleSheet, Dimensions, Platform } from "react-native";
import { fonts } from "../Config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../Config/Svg";
// import SettingsScreen from "../screens/SettingsScreen";
import P from "../Components/P";
// import ReferralScreen from "../screens/ReferralScreen";
import HomeScreen from "../Screens/HealthCareApp/HomeScreen";
import BookingsScreen from "../Screens/HealthCareApp/Bookings/BookingsScreen";
import { colors } from "../Config/colors";
import ProfileScreen from "../Screens/HealthCareApp/Profile/ProfileScreen";
const { width, height } = Dimensions.get("window");
const isTablet = Dimensions.get("window").width >= 700;
const BottomTabNavigator = () => {
  const Tab = createBottomTabNavigator();
  return (
    <Tab.Navigator
      id={undefined}
      screenOptions={{
        // tabBarActiveTintColor: "#fff",
        tabBarHideOnKeyboard: true,
        // tabBarInactiveTintColor: "gray",
        tabBarStyle: [
          {
            position: "absolute",
            height: Platform.OS === "ios"? 90 :  72,
            paddingBottom: Platform.OS === "ios" ? 18 : 10,
            paddingTop: 10,
            backgroundColor: "#fff",
            justifyContent: "space-around",
            borderColor: "#fff",
            paddingLeft: (3 * width) / 100,
            paddingRight: (3 * width) / 100,
          },
          Platform.OS === "ios"
            ? {
                shadowColor: "#00000021",
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 0.5,
                shadowRadius: 4,
              }
            : { elevation: 20 },
        ],
        tabBarLabelStyle: {
          fontFamily: fonts.dmSansMedium,
          fontSize: 11,
          // marginTop: 4,
        },
      }}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen} // Ensure this matches the screen component name
        options={{
          headerShown: false,
          tabBarIcon: ({ focused }) =>
            focused ? (
              <SvgXml xml={svg.homeActive} />
            ) : (
              <SvgXml xml={svg.home} />
            ),
          tabBarLabel: ({ focused }) => (
            <P
              style={{
                color: focused ? colors.primary : colors.navGray,
                fontFamily: fonts.dmSansMedium,
                fontSize: 14,
                marginLeft: isTablet ? 15 : 0,
                textAlign: "center",
                marginTop: 2,
              }}
            >
              Home
            </P>
          ),
        }}
      />
      <Tab.Screen
        name="Bookings"
        component={BookingsScreen} // Ensure this matches the screen component name
        options={{
          headerShown: false,
          tabBarIcon: ({ focused }) =>
            focused ? (
              <SvgXml xml={svg.bookingsActive} />
            ) : (
              <SvgXml xml={svg.bookings} />
            ),
          tabBarLabel: ({ focused }) => (
            <P
              style={{
                color: focused ? colors.primary : colors.navGray,
                fontFamily: fonts.dmSansMedium,
                fontSize: 14,
                marginLeft: isTablet ? 15 : 0,
                textAlign: "center",
                marginTop: 2,
              }}
            >
              Bookings
            </P>
          ),
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen} // Ensure this matches the screen component name
        options={{
          headerShown: false,
          tabBarIcon: ({ focused }) =>
            focused ? (
              <SvgXml xml={svg.profileActive} />
            ) : (
              <SvgXml xml={svg.profile} />
            ),
          tabBarLabel: ({ focused }) => (
            <P
              style={{
                color: focused ? colors.primary : colors.navGray,
                fontFamily: fonts.dmSansMedium,
                fontSize: 14,
                marginLeft: isTablet ? 15 : 0,
                textAlign: "center",
                marginTop: 2,
              }}
            >
              Profile
            </P>
          ),
        }}
      />
    </Tab.Navigator>
  );
};

const styles = StyleSheet.create({});

export default BottomTabNavigator;
