import React, { useState, useEffect } from "react";
import {
  View,
  ScrollView,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  Text,
} from "react-native";
import AppHeader from "../../../Components/AppHeader";
import TabSelector from "../../../Components/profile/ProfileTabSelector";
import { tabs } from "./tabs";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import SelectInput from "../../../Components/SelectInput";
import Input from "../../../Components/Input";
import Button from "../../../Components/Button";
import DateInput from "../../../Components/DateInput";
import { colors } from "../../../Config/colors";
import { fonts } from "../../../Config/Fonts";
import { useToast } from "../../../Context/ToastContext";
import Loader from "../../../Components/Loader";
import NetworkErrorView from "../../../Components/NetworkErrorView";
import {
  getErrorType,
  getErrorMessage,
} from "../../../utils/networkErrorHandler";
import {
  GetDrugAllergies,
  GetChronicIllnesses,
  GetCognitiveConditions,
  GetPhysicalDisabilities,
  GetDrugReactions,
  GetGeneticConditions,
  GetVaccines,
  // GetMedications, // Commented out since using text input
  // GetHabits, // Commented out as requested
  GetDietRestrictions,
  // GetFoodPreferences, // Commented out as requested
} from "../../../RequestHandler.tsx/Auth";
import { UpdateMedicalHistory, GetMedicalHistory } from "../../../RequestHandler.tsx/User";
import { formatDate } from "../../../utils/DateUtils";

const { height } = Dimensions.get("window");
export default function ProfileMedicalScreen({ navigation }) {
  // State for each field (use arrays for multi-selects)
  // For display in UI, we use names
  const [drugAllergies, setDrugAllergies] = useState<string>(""); // Changed to simple text input
  const [reactionTypes, setReactionTypes] = useState([]);
  const [substances, setSubstances] = useState([]);
  const [physicalDisabilities, setPhysicalDisabilities] = useState([]);
  const [cognitiveConditions, setCognitiveConditions] = useState([]);
  const [chronicIllnesses, setChronicIllnesses] = useState([]);
  const [surgicalHistory, setSurgicalHistory] = useState([]);
  const [geneticConditions, setGeneticConditions] = useState([]);
  const [vaccinesReceived, setVaccinesReceived] = useState([]);
  const [lastBooster, setLastBooster] = useState("");
  const [medications, setMedications] = useState<string>(""); // Changed to text input
  const [therapy, setTherapy] = useState([]);
  // const [smokingAlcohol, setSmokingAlcohol] = useState([]); // Commented out as requested
  const [dietRestriction, setDietRestriction] = useState([]);
  // const [foodPreference, setFoodPreference] = useState([]); // Commented out as requested

  // For API submission, we use IDs (no longer needed for drug allergies)
  const [reactionTypeIds, setReactionTypeIds] = useState([]);
  const [physicalDisabilityIds, setPhysicalDisabilityIds] = useState([]);
  const [cognitiveConditionIds, setCognitiveConditionIds] = useState([]);
  const [chronicIllnessIds, setChronicIllnessIds] = useState([]);
  const [geneticConditionIds, setGeneticConditionIds] = useState([]);
  const [vaccineIds, setVaccineIds] = useState([]);
  // const [medicationIds, setMedicationIds] = useState([]); // Removed since using text input
  // const [habitIds, setHabitIds] = useState([]); // Commented out as requested
  const [dietRestrictionIds, setDietRestrictionIds] = useState([]);
  // const [foodPreferenceIds, setFoodPreferenceIds] = useState([]); // Commented out as requested

  // State for API options (drug allergen options removed)
  const [physicalDisabilityOptions, setPhysicalDisabilityOptions] = useState(
    []
  );
  const [cognitiveConditionOptions, setCognitiveConditionOptions] = useState(
    []
  );
  const [chronicIllnessOptions, setChronicIllnessOptions] = useState([]);
  const [reactionTypeOptions, setReactionTypeOptions] = useState([]);
  const [geneticConditionOptions, setGeneticConditionOptions] = useState([]);
  const [vaccineOptions, setVaccineOptions] = useState([]);
  // const [medicationOptions, setMedicationOptions] = useState([]); // Commented out since using text input
  // const [habitOptions, setHabitOptions] = useState([]); // Commented out as requested
  const [dietRestrictionOptions, setDietRestrictionOptions] = useState([]);
  // const [foodPreferenceOptions, setFoodPreferenceOptions] = useState([]); // Commented out as requested

  // Severity level options removed as per owner's request

  // Define therapy options using backend enum
  const therapyOptions = [
    { label: "Yes", value: "yes" },
    { label: "No", value: "no" }
  ];

  // Loading and error states (drug allergies loading removed)
  const [loadingPhysical, setLoadingPhysical] = useState(false);
  const [loadingCognitive, setLoadingCognitive] = useState(false);
  const [loadingIllnesses, setLoadingIllnesses] = useState(false);
  const [loadingReactions, setLoadingReactions] = useState(false);
  const [loadingGenetic, setLoadingGenetic] = useState(false);
  const [loadingVaccines, setLoadingVaccines] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingMedicalHistory, setLoadingMedicalHistory] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const [physicalError, setPhysicalError] = useState(""); // Drug allergy error removed
  const [cognitiveError, setCognitiveError] = useState("");
  const [illnessError, setIllnessError] = useState("");
  const [reactionError, setReactionError] = useState("");
  const [geneticError, setGeneticError] = useState("");
  const [vaccineError, setVaccineError] = useState("");

  // Network error handling
  const [networkError, setNetworkError] = useState<any>(null);
  const [showNetworkError, setShowNetworkError] = useState(false);
  const { handleToast } = useToast();

  // Function to handle network errors
  const handleNetworkError = (error: any) => {
    setNetworkError(error);
    setShowNetworkError(true);
  };

  // Function to retry all API calls
  const handleRetryAllApiCalls = () => {
    setShowNetworkError(false);
    setNetworkError(null);

    // Retry all API calls
    fetchAllData();
  };

  // Define fetch functions (drug allergies fetch removed since we use text input now)

  const fetchChronicIllnesses = async () => {
    try {
      setLoadingIllnesses(true);
      setIllnessError("");
      const response = await GetChronicIllnesses();
      if (response && Array.isArray(response)) {
        // Transform API response to options format
        const options = response.map((item) => ({
          label: item.name,
          value: item.name,
          id: item.id,
        }));
        setChronicIllnessOptions(options);
      } else {
        setIllnessError("Failed to load chronic illnesses");
      }
    } catch (error) {
      setIllnessError("Failed to load chronic illnesses");
      // Check if it's a network error
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        handleNetworkError(error);
      }
    } finally {
      setLoadingIllnesses(false);
    }
  };

  const fetchCognitiveConditions = async () => {
    try {
      setLoadingCognitive(true);
      setCognitiveError("");
      const response = await GetCognitiveConditions();
      if (response && Array.isArray(response)) {
        // Transform API response to options format
        const options = response.map((item) => ({
          label: item.name,
          value: item.name,
          id: item.id,
        }));

        setCognitiveConditionOptions(options);
      } else {
        setCognitiveError("Failed to load cognitive conditions");
      }
    } catch (error) {
      setCognitiveError("Failed to load cognitive conditions")
      // Check if it's a network error
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        handleNetworkError(error);
      }
    } finally {
      setLoadingCognitive(false);
    }
  };

  const fetchPhysicalDisabilities = async () => {
    try {
      setLoadingPhysical(true);
      setPhysicalError("");
      const response = await GetPhysicalDisabilities();

      if (response && Array.isArray(response)) {
        // Transform API response to options format
        const options = response.map((item) => ({
          label: item.name,
          value: item.name,
          id: item.id,
        }));
        setPhysicalDisabilityOptions(options);
      } else {
        setPhysicalError("Failed to load physical disabilities");
      }
    } catch (error) {
      console.log("Error fetching physical disabilities:", error);
      setPhysicalError("Failed to load physical disabilities");

      // Check if it's a network error
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        handleNetworkError(error);
      }
    } finally {
      setLoadingPhysical(false);
    }
  };

  // Medications fetch removed - now using text input

  // Fetch habits (smoking/alcohol) from API - commented out as requested
  /* const fetchHabits = async () => {
    try {
      setLoading(true);
      const response = await GetHabits();

      if (response && Array.isArray(response)) {
        // Transform API response to options format
        const options = response.map(item => ({
          label: item.name,
          value: item.name,
          id: item.id
        }));

        setHabitOptions(options);
      } else {
        console.log("Failed to load habits");
      }
    } catch (error) {
      console.log("Error fetching habits:", error);

      // Check if it's a network error
      if (getErrorType(error) === "network" || getErrorType(error) === "timeout") {
        handleNetworkError(error);
      }
    } finally {
      setLoading(false);
    }
  }; */

  // Fetch diet restrictions from API
  const fetchDietRestrictions = async () => {
    try {
      setLoading(true);
      const response = await GetDietRestrictions();

      if (response && Array.isArray(response)) {
        // Transform API response to options format
        const options = response.map(item => ({
          label: item.name,
          value: item.name,
          id: item.id
        }));

        setDietRestrictionOptions(options);
      } else {
        console.log("Failed to load diet restrictions");
      }
    } catch (error) {
      console.log("Error fetching diet restrictions:", error);

      // Check if it's a network error
      if (getErrorType(error) === "network" || getErrorType(error) === "timeout") {
        handleNetworkError(error);
      }
    } finally {
      setLoading(false);
    }
  };

  // Fetch food preferences from API - commented out as requested
  /* const fetchFoodPreferences = async () => {
    try {
      setLoading(true);
      const response = await GetFoodPreferences();

      if (response && Array.isArray(response)) {
        // Transform API response to options format
        const options = response.map(item => ({
          label: item.name,
          value: item.name,
          id: item.id
        }));

        setFoodPreferenceOptions(options);
      } else {
        console.log("Failed to load food preferences");
      }
    } catch (error) {
      console.log("Error fetching food preferences:", error);

      // Check if it's a network error
      if (getErrorType(error) === "network" || getErrorType(error) === "timeout") {
        handleNetworkError(error);
      }
    } finally {
      setLoading(false);
    }
  }; */

  // Function to fetch user's medical history
  const fetchUserMedicalHistory = async () => {
    try {
      setLoadingMedicalHistory(true);
      const response = await GetMedicalHistory();
      console.log(response);
      if (response && response.data && response.data.active) {
        console.log("Fetched medical history:", response);
        // Set drug allergies as simple text (no longer using multi-select or severity levels)
        if (response.data.drugAllergyAssociation && response.data.drugAllergyAssociation.length > 0) {
          // Convert array of drug allergies to comma-separated string
          const allergenNames = response.data.drugAllergyAssociation.map((item: any) => item.drugAllergy.name);
          setDrugAllergies(allergenNames.join(", "));
        }

        // Set drug reactions
        if (response.data.drugReactionAssociation && response.data.drugReactionAssociation.length > 0) {
          // For display in UI, we need the names
          const reactionNames = response.data.drugReactionAssociation.map(item => item.drugReaction.name);
          // For API submission, we need the IDs
          const reactionIds = response.data.drugReactionAssociation.map(item => item.drugReaction.id);

          setReactionTypes(reactionNames);
          setReactionTypeIds(reactionIds);
        }

        // Set physical disabilities
        if (response.data.disabilityAssociation && response.data.disabilityAssociation.length > 0) {
          // For display in UI, we need the names
          const disabilityNames = response.data.disabilityAssociation.map(item => item.physicalDisability.name);
          // For API submission, we need the IDs
          const disabilityIds = response.data.disabilityAssociation.map(item => item.physicalDisability.id);
          setPhysicalDisabilities(disabilityNames);
          setPhysicalDisabilityIds(disabilityIds);
        }

        // Set cognitive conditions
        if (response.data.cognitiveConditionAssociation && response.data.cognitiveConditionAssociation.length > 0) {
          // For display in UI, we need the names
          const conditionNames = response.data.cognitiveConditionAssociation.map(item => item.cognitiveCondition.name);
          // For API submission, we need the IDs
          const conditionIds = response.data.cognitiveConditionAssociation.map(item => item.cognitiveCondition.id);
          setCognitiveConditions(conditionNames);
          setCognitiveConditionIds(conditionIds);
        }

        // Set chronic illnesses
        if (response.data.illnessAssociation && response.data.illnessAssociation.length > 0) {
          // For display in UI, we need the names
          const illnessNames = response.data.illnessAssociation.map(item => item.illness.name);
          // For API submission, we need the IDs
          const illnessIds = response.data.illnessAssociation.map(item => item.illness.id);
          setChronicIllnesses(illnessNames);
          setChronicIllnessIds(illnessIds);
        }

        // Set surgical history (uses value, not ID)
        if (response.data.surgicalHistory) {
          setSurgicalHistory([response.data.surgicalHistory]);
        }

        // Set genetic conditions
        if (response.data.geneticConditionAssociation && response.data.geneticConditionAssociation.length > 0) {
          // For display in UI, we need the names
          const conditionNames = response.data.geneticConditionAssociation.map(item => item.geneticCondition.name);
          // For API submission, we need the IDs
          const conditionIds = response.data.geneticConditionAssociation.map(item => item.geneticCondition.id);
          setGeneticConditions(conditionNames);
          setGeneticConditionIds(conditionIds);
        }

        // Set vaccines
        if (response.data.vaccineAssociation && response.data.vaccineAssociation.length > 0) {
          // For display in UI, we need the names
          const vaccineNames = response.data.vaccineAssociation.map(item => item.vaccine.name);
          // For API submission, we need the IDs
          const vaccineIds = response.data.vaccineAssociation.map(item => item.vaccine.id);
          setVaccinesReceived(vaccineNames);
          setVaccineIds(vaccineIds);
        }

        // Set date of last booster
        if (response.data.dateOfLastBoaster) {
          console.log("Date of last booster from API:", response.data.dateOfLastBoaster);
          // Ensure the date is in YYYY-MM-DD format for the DateInput component
          try {
            // Parse the date and format it as YYYY-MM-DD
            const date = new Date(response.data.dateOfLastBoaster);
            if (!isNaN(date.getTime())) {
              const formattedDate = date.toISOString().split('T')[0]; // Get YYYY-MM-DD part
              console.log("Formatted date of last booster:", formattedDate);
              setLastBooster(formattedDate);
            } else {
              console.error("Invalid date format for dateOfLastBoaster:", response.data.dateOfLastBoaster);
              setLastBooster("");
            }
          } catch (error) {
            console.error("Error parsing date of last booster:", error);
            setLastBooster("");
          }
        }

        // Set medications as text
        if (response.data.medicationAssociation && response.data.medicationAssociation.length > 0) {
          // Convert array of medications to comma-separated string
          const medicationNames = response.data.medicationAssociation.map((item: any) => item.medication.name);
          setMedications(medicationNames.join(", "));
        }

        // Set recovery support (therapy) (uses value, not ID)
        if (response.data.recoverySupport) {
          setTherapy([response.data.recoverySupport]);
        }

        // Set habits (smoking/alcohol) - commented out as requested
        /* if (response.data.habitAssociation && response.data.habitAssociation.length > 0) {
          // For display in UI, we need the names
          const habitNames = response.data.habitAssociation.map(item => item.habit.name);
          // For API submission, we need the IDs
          const habitIds = response.data.habitAssociation.map(item => item.habit.id);
          setSmokingAlcohol(habitNames);
          setHabitIds(habitIds);
        } */

        // Set diet restrictions
        if (response.data.dietRestrictionAssociation && response.data.dietRestrictionAssociation.length > 0) {
          // For display in UI, we need the names
          const restrictionNames = response.data.dietRestrictionAssociation.map(item => item.dietRestriction.name);
          // For API submission, we need the IDs
          const restrictionIds = response.data.dietRestrictionAssociation.map(item => item.dietRestriction.id);
          setDietRestriction(restrictionNames);
          setDietRestrictionIds(restrictionIds);
        }

        // Set food preferences - commented out as requested
        /* if (response.data.foodPreferenceAssociation && response.data.foodPreferenceAssociation.length > 0) {
          // For display in UI, we need the names
          const preferenceNames = response.data.foodPreferenceAssociation.map(item => item.foodPreference.name);
          // For API submission, we need the IDs
          const preferenceIds = response.data.foodPreferenceAssociation.map(item => item.foodPreference.id);
          setFoodPreference(preferenceNames);
          setFoodPreferenceIds(preferenceIds);
        } */
      }
    } catch (error) {
      console.error("Error fetching medical history:", error);

      // Check if it's a network error
      if (getErrorType(error) === "network" || getErrorType(error) === "timeout") {
        handleNetworkError(error);
      }
    } finally {
      setLoadingMedicalHistory(false);
    }
  };

  // Function to fetch all data
  const fetchAllData = () => {
    // Call all the fetch functions directly (drug allergies removed)
    fetchChronicIllnesses();
    fetchCognitiveConditions();
    fetchPhysicalDisabilities();
    fetchDrugReactions();
    fetchGeneticConditions();
    fetchVaccines();
    // fetchMedications(); // Commented out since using text input
    // fetchHabits(); // Commented out as requested
    fetchDietRestrictions();
    // fetchFoodPreferences(); // Commented out as requested

    // Fetch user's medical history to set default values
    fetchUserMedicalHistory();
  };

  // Call fetchAllData on component mount
  useEffect(() => {
    fetchAllData();
  }, []);

  // Fetch drug reactions from API
  const fetchDrugReactions = async () => {
    try {
      setLoadingReactions(true);
      setReactionError("");
      const response = await GetDrugReactions();

      if (response && Array.isArray(response)) {
        // Transform API response to options format
        const options = response.map((item) => ({
          label: item.name,
          value: item.name,
          id: item.id,
        }));

        setReactionTypeOptions(options);
      } else {
        setReactionError("Failed to load drug reactions");
      }
    } catch (error) {
      console.log("Error fetching drug reactions:", error);
      setReactionError("Failed to load drug reactions");

      // Check if it's a network error
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        handleNetworkError(error);
      }
    } finally {
      setLoadingReactions(false);
    }
  };

  // Fetch genetic conditions from API
  const fetchGeneticConditions = async () => {
    try {
      setLoadingGenetic(true);
      setGeneticError("");
      const response = await GetGeneticConditions();

      if (response && Array.isArray(response)) {
        // Transform API response to options format
        const options = response.map((item) => ({
          label: item.name,
          value: item.name,
          id: item.id,
        }));

        setGeneticConditionOptions(options);
      } else {
        setGeneticError("Failed to load genetic conditions");
      }
    } catch (error) {
      console.log("Error fetching genetic conditions:", error);
      setGeneticError("Failed to load genetic conditions");

      // Check if it's a network error
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        handleNetworkError(error);
      }
    } finally {
      setLoadingGenetic(false);
    }
  };

  // Fetch vaccines from API
  const fetchVaccines = async () => {
    try {
      setLoadingVaccines(true);
      setVaccineError("");
      const response = await GetVaccines();

      if (response && Array.isArray(response)) {
        // Transform API response to options format
        const options = response.map((item) => ({
          label: item.name,
          value: item.name,
          id: item.id,
        }));

        setVaccineOptions(options);
      } else {
        setVaccineError("Failed to load vaccines");
      }
    } catch (error) {
      console.log("Error fetching vaccines:", error);
      setVaccineError("Failed to load vaccines");

      // Check if it's a network error
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        handleNetworkError(error);
      }
    } finally {
      setLoadingVaccines(false);
    }
  };

  // Drug allergies fetch removed - now using text input

  // Fetch chronic illnesses from API
  useEffect(() => {
    const fetchChronicIllnesses = async () => {
      try {
        setLoadingIllnesses(true);
        const response = await GetChronicIllnesses();

        if (response && Array.isArray(response)) {
          // Transform API response to options format
          const options = response.map((item) => ({
            label: item.name,
            value: item.name,
            id: item.id,
          }));

          setChronicIllnessOptions(options);
        } else {
          setIllnessError("Failed to load chronic illnesses");
        }
      } catch (error) {
        console.log("Error fetching chronic illnesses:", error);
        setIllnessError("Failed to load chronic illnesses");
      } finally {
        setLoadingIllnesses(false);
      }
    };

    fetchChronicIllnesses();
  }, []);

  // Fetch cognitive conditions from API
  useEffect(() => {
    const fetchCognitiveConditions = async () => {
      try {
        setLoadingCognitive(true);
        const response = await GetCognitiveConditions();

        if (response && Array.isArray(response)) {
          // Transform API response to options format
          const options = response.map((item) => ({
            label: item.name,
            value: item.name,
            id: item.id,
          }));

          setCognitiveConditionOptions(options);
        } else {
          setCognitiveError("Failed to load cognitive conditions");
        }
      } catch (error) {
        console.log("Error fetching cognitive conditions:", error);
        setCognitiveError("Failed to load cognitive conditions");
      } finally {
        setLoadingCognitive(false);
      }
    };

    fetchCognitiveConditions();
  }, []);

  // Fetch physical disabilities from API
  useEffect(() => {
    const fetchPhysicalDisabilities = async () => {
      try {
        setLoadingPhysical(true);
        const response = await GetPhysicalDisabilities();

        if (response && Array.isArray(response)) {
          // Transform API response to options format
          const options = response.map((item) => ({
            label: item.name,
            value: item.name,
            id: item.id,
          }));

          setPhysicalDisabilityOptions(options);
        } else {
          setPhysicalError("Failed to load physical disabilities");
        }
      } catch (error) {
        console.log("Error fetching physical disabilities:", error);
        setPhysicalError("Failed to load physical disabilities");
      } finally {
        setLoadingPhysical(false);
      }
    };

    fetchPhysicalDisabilities();
  }, []);

  // Handler functions for selections with IDs (drug allergies no longer need handlers)

  const handlePhysicalDisabilitySelect = (values: string[]) => {
    setPhysicalDisabilities(values);

    // Find and store the IDs of selected physical disabilities
    const selectedIds = values
      .map((value: string) => {
        const option = physicalDisabilityOptions.find(
          (opt) => opt.value === value
        );
        return option ? option.id : null;
      })
      .filter((id: any) => id !== null);

    setPhysicalDisabilityIds(selectedIds);
  };

  const handleCognitiveConditionSelect = (values: string[]) => {
    setCognitiveConditions(values);

    // Find and store the IDs of selected cognitive conditions
    const selectedIds = values
      .map((value: string) => {
        const option = cognitiveConditionOptions.find(
          (opt) => opt.value === value
        );
        return option ? option.id : null;
      })
      .filter((id: any) => id !== null);

    setCognitiveConditionIds(selectedIds);
  };

  const handleChronicIllnessSelect = (values: string[]) => {
    setChronicIllnesses(values);

    // Find and store the IDs of selected chronic illnesses
    const selectedIds = values
      .map((value: string) => {
        const option = chronicIllnessOptions.find((opt) => opt.value === value);
        return option ? option.id : null;
      })
      .filter((id: any) => id !== null);

    setChronicIllnessIds(selectedIds);
  };

  // Handler for reaction types selection
  const handleReactionTypeSelect = (values: string[]) => {
    setReactionTypes(values);

    // Find and store the IDs of selected reaction types
    const selectedIds = values
      .map((value: string) => {
        const option = reactionTypeOptions.find((opt) => opt.value === value);
        return option ? option.id : null;
      })
      .filter((id: any) => id !== null);

    // Store reaction type IDs for API
    setReactionTypeIds(selectedIds);
    console.log("Selected reaction type IDs:", selectedIds);
  };

  // Handler for genetic conditions selection
  const handleGeneticConditionSelect = (values: string[]) => {
    setGeneticConditions(values);

    // Find and store the IDs of selected genetic conditions
    const selectedIds = values
      .map((value: string) => {
        const option = geneticConditionOptions.find((opt) => opt.value === value);
        return option ? option.id : null;
      })
      .filter((id: any) => id !== null);

    // Store genetic condition IDs for API
    setGeneticConditionIds(selectedIds);
    console.log("Selected genetic condition IDs:", selectedIds);
  };

  // Handler for vaccines selection
  const handleVaccinesSelect = (values: string[]) => {
    setVaccinesReceived(values);

    // Find and store the IDs of selected vaccines
    const selectedIds = values
      .map((value: string) => {
        const option = vaccineOptions.find((opt) => opt.value === value);
        return option ? option.id : null;
      })
      .filter((id: any) => id !== null);

    // Store vaccine IDs for API
    setVaccineIds(selectedIds);
    console.log("Selected vaccine IDs:", selectedIds);
  };

  // Medications handler removed - now using text input

  // Handler for habits (smoking/alcohol) selection - commented out as requested
  /* const handleHabitsSelect = (values: string[]) => {
    setSmokingAlcohol(values);

    // Find and store the IDs of selected habits
    const selectedIds = values
      .map((value: string) => {
        const option = habitOptions.find((opt) => opt.value === value);
        return option ? option.id : null;
      })
      .filter((id: any) => id !== null);

    // Store habit IDs for API
    setHabitIds(selectedIds);
    console.log("Selected habit IDs:", selectedIds);
  }; */

  // Handler for diet restrictions selection
  const handleDietRestrictionSelect = (values: string[]) => {
    setDietRestriction(values);

    // Find and store the IDs of selected diet restrictions
    const selectedIds = values
      .map((value: string) => {
        const option = dietRestrictionOptions.find((opt) => opt.value === value);
        return option ? option.id : null;
      })
      .filter((id: any) => id !== null);

    // Store diet restriction IDs for API
    setDietRestrictionIds(selectedIds);
    console.log("Selected diet restriction IDs:", selectedIds);
  };

  // Handler for food preferences selection - commented out as requested
  /* const handleFoodPreferenceSelect = (values: string[]) => {
    setFoodPreference(values);

    // Find and store the IDs of selected food preferences
    const selectedIds = values
      .map((value: string) => {
        const option = foodPreferenceOptions.find((opt) => opt.value === value);
        return option ? option.id : null;
      })
      .filter((id: any) => id !== null);

    // Store food preference IDs for API
    setFoodPreferenceIds(selectedIds);
    console.log("Selected food preference IDs:", selectedIds);
  }; */

  const handleSave = async () => {
    try {
      setSubmitting(true);

      // Format the data according to the required API format
      const requestBody = {
        // Fields that use IDs (all except surgicalHistory, recoverySupport, drugAllergies)
        drugAllergies: drugAllergies, // Simple text field
        cognitiveConditions: cognitiveConditionIds,
        disabilities: physicalDisabilityIds,
        illnesses: chronicIllnessIds,
        drugReactions: reactionTypeIds,
        geneticConditions: geneticConditionIds,
        vaccines: vaccineIds,
        medications: medications, // Now using text field
        // habits: habitIds, // Commented out as requested
        dietRestrictions: dietRestrictionIds,
        // foodPreferences: foodPreferenceIds, // Commented out as requested
        // Fields that use values, not IDs
        surgicalHistory: surgicalHistory[0] || "none",
        dateOfLastBoaster: lastBooster || new Date().toISOString(),
        therapy: therapy[0] || "no",
      };
      console.log("Sending medical history part 1 to API:", requestBody);
      const res = await UpdateMedicalHistory(requestBody);
      console.log("Update response:", res);

      if (res.active) {
        handleToast("Medical history updated successfully", "success");
        navigation.goBack();
      }
    } catch (error) {
      console.error("Error updating medical history:", error);
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        handleNetworkError(error);
      } else {
        handleToast(
          "Failed to update medical history. Please try again.",
          "error"
        );
      }
    } finally {
      setSubmitting(false);
    }
  };

  // If there's a network error, show the NetworkErrorView
  if (showNetworkError) {
    return (
      <NetworkErrorView
        errorType={getErrorType(networkError)}
        message={getErrorMessage(networkError)}
        onRetry={handleRetryAllApiCalls}
      />
    );
  }

  // Show loading indicator while fetching medical history
  if (loadingMedicalHistory) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Loader />
      </View>
    );
  }

  return (
    <View>
      <View style={styles.contentContainer}>
        {/* @ts-ignore */}
        <H4 style={[styles.sectionTitle, { marginTop: 0 }]}>Allergies</H4>
        <Input
          label="Drug Allergies"
          placeholder="Enter drug allergies (e.g., Penicillin, Aspirin, etc.)"
          value={drugAllergies}
          onChangeText={setDrugAllergies}
          contStyle={styles.multilineInputContainer}
          inputStyle={styles.multilineInput}
          multiline={true}
          numberOfLines={4}
          textAlignVertical="top"
        />
        <View style={{ flexDirection: "row", gap: 10 }}>
          <View style={{ flex: 1 }}>
            {loadingReactions ? (
              <View style={styles.loadingContainerSmall}>
                <ActivityIndicator size="small" color={colors.primary} />
              </View>
            ) : reactionError ? (
              <View style={styles.errorContainerSmall}>
                <Text style={styles.errorTextSmall}>{reactionError}</Text>
              </View>
            ) : (
              <SelectInput
                label="Reaction Type"
                placeholder="Select"
                options={reactionTypeOptions}
                selectedValues={reactionTypes}
                onSelect={handleReactionTypeSelect}
                multiSelect={true}
                maxSelections={5}
                searchPlaceholder="Search for Reaction Types"
                contStyle={{ marginBottom: 16 }}
              />
            )}
          </View>
        </View>

        {/* Severity levels removed as per owner's request */}
        {/* Substances field - using custom options since there's no API endpoint */}
        {/* <SelectInput
          label="Substances"
          placeholder="Select"
          options={[
            { label: "Penicillin", value: "penicillin" },
            { label: "Aspirin", value: "aspirin" },
            { label: "Ibuprofen", value: "ibuprofen" },
            { label: "Sulfa drugs", value: "sulfa" },
            { label: "Latex", value: "latex" },
            { label: "Contrast dye", value: "contrast_dye" },
            { label: "Other", value: "other" }
          ]}
          selectedValues={substances}
          onSelect={setSubstances}
          multiSelect
        /> */}
        {/* Chronic Conditions Section */}
        <H4 style={styles.sectionTitle}>Chronic Conditions</H4>
        {loadingPhysical ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color={colors.primary} />
          </View>
        ) : physicalError ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{physicalError}</Text>
          </View>
        ) : (
          <SelectInput
            label="Physical Disabilities"
            placeholder="Select"
            options={physicalDisabilityOptions}
            selectedValues={physicalDisabilities}
            onSelect={handlePhysicalDisabilitySelect}
            multiSelect
            maxSelections={5}
            contStyle={{ marginBottom: 16 }}
          />
        )}
        <View style={{ flexDirection: "row", gap: 10 }}>
          <View style={{ flex: 1 }}>
            {loadingCognitive ? (
              <View style={styles.loadingContainerSmall}>
                <ActivityIndicator size="small" color={colors.primary} />
              </View>
            ) : cognitiveError ? (
              <View style={styles.errorContainerSmall}>
                <Text style={styles.errorTextSmall}>{cognitiveError}</Text>
              </View>
            ) : (
              <SelectInput
                label="Cognitive conditions"
                placeholder="Select"
                options={cognitiveConditionOptions}
                selectedValues={cognitiveConditions}
                onSelect={handleCognitiveConditionSelect}
                multiSelect
                maxSelections={3}
              />
            )}
          </View>
          <View style={{ flex: 1 }}>
            {loadingIllnesses ? (
              <View style={styles.loadingContainerSmall}>
                <ActivityIndicator size="small" color={colors.primary} />
              </View>
            ) : illnessError ? (
              <View style={styles.errorContainerSmall}>
                <Text style={styles.errorTextSmall}>{illnessError}</Text>
              </View>
            ) : (
              <SelectInput
                label="Chronic Illnesses"
                placeholder="Select"
                options={chronicIllnessOptions}
                selectedValues={chronicIllnesses}
                onSelect={handleChronicIllnessSelect}
                multiSelect
                maxSelections={5}
              />
            )}
          </View>
        </View>
        {/* Surgical History & Genetics */}
        <H4 style={styles.sectionTitle}>Surgical History</H4>
        {/* Surgical History field - using custom options since there's no API endpoint */}
        <SelectInput
          label="Surgical History"
          placeholder="Select"
          options={[
            { label: "None", value: "none" },
            { label: "Procedure", value: "procedure" },
            { label: "Year", value: "year" },
            { label: "Outcome", value: "outcome" },
          ]}
          selectedValues={surgicalHistory}
          onSelect={setSurgicalHistory}
          contStyle={{ marginBottom: 16 }}
        />
        {loadingGenetic ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color={colors.primary} />
          </View>
        ) : geneticError ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{geneticError}</Text>
          </View>
        ) : (
          <SelectInput
            label="Genetic Conditions (Optional)"
            placeholder="Select"
            options={geneticConditionOptions}
            selectedValues={geneticConditions}
            onSelect={handleGeneticConditionSelect}
            multiSelect
          />
        )}
        {/* Immunization Records */}
        <H4 style={styles.sectionTitle}>Immunization Records</H4>
        {loadingVaccines ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color={colors.primary} />
          </View>
        ) : vaccineError ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{vaccineError}</Text>
          </View>
        ) : (
          <SelectInput
            label="Vaccines Received"
            placeholder="Select Vaccines"
            options={vaccineOptions}
            selectedValues={vaccinesReceived}
            onSelect={handleVaccinesSelect}
            multiSelect
            contStyle={{ marginBottom: 16 }}
          />
        )}
        <DateInput
          label="Date of Last Booster"
          placeholder="DD/MM/YYYY"
          value={lastBooster}
          onSelect={setLastBooster}
        />
        {/* Current Status */}
        <H4 style={styles.sectionTitle}>Current Status</H4>
        {/* Medications field - now using text input */}
        <Input
          label="Medications Currently Taking"
          placeholder="Enter medications (e.g., Aspirin, Metformin, etc.)"
          value={medications}
          onChangeText={setMedications}
          contStyle={styles.multilineInputContainer}
          inputStyle={styles.multilineInput}
          multiline={true}
          numberOfLines={4}
          textAlignVertical="top"
        />
        {/* Therapy field - using predefined therapy options */}
        <SelectInput
          label="Therapy"
          placeholder="Select"
          options={therapyOptions}
          selectedValues={therapy}
          onSelect={setTherapy}
        />
        {/* Lifestyle */}
        <H4 style={styles.sectionTitle}>Lifestyle</H4>
        {/* Smoking/Alcohol field - commented out as requested */}
        {/* <SelectInput
          label="Smoking/Alcohol Use"
          placeholder="Select"
          options={habitOptions}
          selectedValues={smokingAlcohol}
          onSelect={handleHabitsSelect}
          multiSelect
          contStyle={{ marginBottom: 16 }}
        /> */}
        {/* Diet Restriction field - using API data */}
        <SelectInput
          label="Diet Restriction"
          placeholder="Select"
          options={dietRestrictionOptions}
          selectedValues={dietRestriction}
          onSelect={handleDietRestrictionSelect}
          multiSelect
          contStyle={{ marginBottom: 16 }}
        />
        {/* Food Preference field - commented out as requested */}
        {/* <SelectInput
          label="Food Preference"
          placeholder="Select"
          options={foodPreferenceOptions}
          selectedValues={foodPreference}
          onSelect={handleFoodPreferenceSelect}
          multiSelect
        /> */}
        <View style={{ height: 40 }} />
      </View>
      {/* Save Button */}
      <View style={styles.buttonContainer}>
        <Button
          btnText="Save changes"
          onPress={handleSave}
          style={styles.saveButton}
          loading={submitting}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
  },
  headerContainer: {
    width: "100%",
    paddingHorizontal: 20,
  },
  completionText: {
    color: "#D4AF37",
    fontSize: 12,
    fontFamily: fonts.dmSansRegular,
    marginTop: 4,
  },
  scrollView: {
    flex: 1,
    width: "100%",
  },
  contentContainer: {
    paddingBottom: 100,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: fonts.dmSansBold,
    color: colors.black,
    marginTop: 30,
    marginBottom: 10,
  },
  buttonContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
    paddingBottom: 28,
    backgroundColor: colors.white,
    width: "100%",
  },
  saveButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
  },
  severitySection: {
    marginTop: 16,
    padding: 16,
    backgroundColor: "#F8F9FA",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#E9ECEF",
  },
  severityTitle: {
    fontSize: 12,
    fontFamily: fonts.dmSansBold,
    color: colors.black,
    marginBottom: 12,
  },
  severityItem: {
    marginBottom: 8,
  },
  // Loading and error styles
  loadingContainer: {
    padding: 16,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#F5F5F5", // Light gray background
    borderRadius: 8,
    marginBottom: 16,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 12,
    fontFamily: fonts.dmSansRegular,
    color: "#757575", // Dark gray text
  },
  errorContainer: {
    padding: 16,
    backgroundColor: "#FFEBEE", // Light red background
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 12,
    fontFamily: fonts.dmSansRegular,
    color: "#D32F2F", // Red text
  },
  // Small loading and error styles for half columns
  loadingContainerSmall: {
    padding: 8,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#F5F5F5", // Light gray background
    borderRadius: 8,
    marginBottom: 16,
    height: 80, // Fixed height to match the SelectInput
  },
  loadingTextSmall: {
    marginTop: 4,
    fontSize: 12,
    fontFamily: fonts.dmSansRegular,
    color: "#757575", // Dark gray text
  },
  errorContainerSmall: {
    padding: 8,
    backgroundColor: "#FFEBEE", // Light red background
    borderRadius: 8,
    marginBottom: 16,
    height: 80, // Fixed height to match the SelectInput
  },
  errorTextSmall: {
    fontSize: 12,
    fontFamily: fonts.dmSansRegular,
    color: "#D32F2F", // Red text
  },
  // Multiline input styles
  multilineInputContainer: {
    minHeight: 120, // Increased height
    maxHeight: 200, // Maximum height to prevent excessive growth
    marginBottom: 16, // Bottom margin
  },
  multilineInput: {
    minHeight: 100, // Increased input height
    maxHeight: 180, // Maximum input height
    paddingTop: 12, // Top padding
    paddingBottom: 12, // Bottom padding
    paddingHorizontal: 16, // Horizontal padding
    textAlignVertical: "top", // Align text to top
  },
});
