import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  FlatList,
  Keyboard,
  TouchableWithoutFeedback,
  ActivityIndicator,
  Alert,
} from "react-native";
import { colors } from "../../../Config/colors";
import { fonts } from "../../../Config/Fonts";
import Div from "../../../Components/Div";
import P from "../../../Components/P";
import Button from "../../../Components/Button";
import AppHeader from "../../../Components/AppHeader";
import Input from "../../../Components/Input";
import SelectInput from "../../../Components/SelectInput";
import { useToast } from "../../../Context/ToastContext";
import DaySelector from "../../../Components/DaySelector";
import { AddReminder } from "../../../RequestHandler.tsx/User";
import NetworkErrorView from "../../../Components/NetworkErrorView";
import { getErrorMessage, getErrorType } from "../../../utils/networkErrorHandler";


const { width, height } = Dimensions.get("window");

// Form options
const formOptions = [
  { label: "Tablet", value: "Tablet" },
  { label: "Capsule", value: "Capsule" },
  { label: "Syrup", value: "Syrup" },
  { label: "Injection", value: "Injection" },
  { label: "Drops", value: "Drops" },
  { label: "Inhaler", value: "Inhaler" },
  { label: "Patch", value: "Patch" },
  { label: "Cream", value: "Cream" },
  { label: "Ointment", value: "Ointment" },
];

const repeatOptions = [
  { label: "Once", value: "once" },
  { label: "Monday-Sunday", value: "mon-sun" },
  { label: "Daily", value: "daily" },
  { label: "Custom", value: "custom" },
];

export default function CreateReminderScreen({ navigation, route }) {
  const { handleToast } = useToast();
  const [loading, setLoading] = useState(false);
  const [networkError, setNetworkError] = useState<any>(null);
  const [showNetworkError, setShowNetworkError] = useState(false);

  // Form state
  const [medicineName, setMedicineName] = useState("");
  const [dosage, setDosage] = useState("");
  const [form, setForm] = useState<string[]>([]);
  const [repeat, setRepeat] = useState<string[]>(["once"]);
  const [selectedDays, setSelectedDays] = useState<string[]>([]);
  const [guidance, setGuidance] = useState("");

  // Time state
  const [selectedTime, setSelectedTime] = useState("08:00AM");

  // Check if custom repeat is selected
  const isCustomRepeatSelected = repeat.includes("custom");

  // References for FlatLists
  const hourListRef = React.useRef(null);
  const minuteListRef = React.useRef(null);
  const ampmListRef = React.useRef(null);

  // Track if user is currently scrolling to prevent auto-scroll conflicts
  const isUserScrolling = React.useRef(false);

  // Keyboard visibility state
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);

  // Form validation
  const [errors, setErrors] = useState({
    medicineName: "",
    form: "",
    customDays: "",
  });

  // Function to handle network errors
  const handleNetworkError = (error: any) => {
    setNetworkError(error);
    setShowNetworkError(true);
  };

  // Function to retry after network error
  const handleRetry = () => {
    setShowNetworkError(false);
    setNetworkError(null);
  };

  // Add keyboard listeners
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        setKeyboardVisible(true);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        setKeyboardVisible(false);
      }
    );

    // Clean up listeners
    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  // Scroll to selected time when it changes (but not when user is manually scrolling)
  useEffect(() => {
    // Don't auto-scroll if user is currently scrolling
    if (isUserScrolling.current) {
      return;
    }

    const scrollToCenter = () => {
      try {
        // Extract hour, minute, and period from selectedTime
        const [timePart, period] = selectedTime.split(/(?=AM|PM)/);
        const [hour, minute] = timePart.split(":");
        const hourIndex = parseInt(hour) - 1;
        const minuteIndex = parseInt(minute);
        const ampmIndex = period === "AM" ? 0 : 1;

        // Add delay to ensure ScrollViews are rendered and layout is calculated
        setTimeout(() => {
          // Scroll to selected hour (center it)
          if (hourListRef.current && hourIndex >= 0 && hourIndex < 12) {
            hourListRef.current.scrollTo({
              y: hourIndex * 50 + 3, // Adjust for centering
              animated: false, // Use false for initial positioning
            });
          }

          // Scroll to selected minute (center it)
          if (minuteListRef.current && minuteIndex >= 0 && minuteIndex < 60) {
            minuteListRef.current.scrollTo({
              y: minuteIndex * 50 + 3, // Adjust for centering
              animated: false, // Use false for initial positioning
            });
          }

          // Scroll to selected AM/PM (center it)
          if (ampmListRef.current && ampmIndex >= 0 && ampmIndex < 2) {
            ampmListRef.current.scrollTo({
              y: ampmIndex * 50 + 3, // Adjust for centering
              animated: false, // Use false for initial positioning
            });
          }
        }, 200);
      } catch (error) {
        console.log("Error scrolling to selected time:", error);
      }
    };

    scrollToCenter();
  }, [selectedTime]);

  // Handle scroll begin - mark that user is scrolling
  const handleScrollBegin = () => {
    isUserScrolling.current = true;
  };

  // Handle scroll end - allow auto-scroll again after a delay
  const handleScrollEnd = () => {
    setTimeout(() => {
      isUserScrolling.current = false;
    }, 100);
  };

  // Handle scroll events to update selected time
  const handleHourScroll = (event: any) => {
    const offsetY = event.nativeEvent.contentOffset.y;
    const itemHeight = 50; // height (44) + marginBottom (6)

    // Calculate the index based on the scroll offset
    // We want the item whose center is closest to the center of the view (100px within the 200px height)
    // The items start after 78px padding.
    // So, the effective scroll position relative to the start of the items is offsetY.
    // The center of the view relative to the start of the items is 100 - 78 = 22.
    // We want the item index 'i' where i * itemHeight + itemHeight / 2 is closest to offsetY + 22.
    // (offsetY + 22 - itemHeight / 2) / itemHeight = index
    const index = Math.round((offsetY + 22 - itemHeight / 2) / itemHeight);

    const hour = String(Math.max(1, Math.min(12, index + 1))).padStart(2, "0");

    const [_, minute] = selectedTime.split(":");
    const period = selectedTime.includes("AM") ? "AM" : "PM";
    const newMinute = minute ? minute.replace(/AM|PM/, "") : "00";

    // Temporarily disable auto-scroll while updating
    isUserScrolling.current = true;
    setSelectedTime(`${hour}:${newMinute}${period}`);
    handleScrollEnd();
     // Manually snap to the calculated index
     hourListRef.current?.scrollTo({
      y: index * itemHeight, // Scroll to the top of the selected item
      animated: true,
    });
  };

  const handleMinuteScroll = (event: any) => {
    const offsetY = event.nativeEvent.contentOffset.y;
    const itemHeight = 50; // height (44) + marginBottom (6)

    // Calculate the index based on the scroll offset
    const index = Math.round((offsetY + 22 - itemHeight / 2) / itemHeight);

    const minute = String(Math.max(0, Math.min(59, index))).padStart(2, "0");

    const [hour] = selectedTime.split(":");
    const period = selectedTime.includes("AM") ? "AM" : "PM";

    // Temporarily disable auto-scroll while updating
    isUserScrolling.current = true;
    setSelectedTime(`${hour}:${minute}${period}`);
    handleScrollEnd();
     // Manually snap to the calculated index
     minuteListRef.current?.scrollTo({
      y: index * itemHeight,
      animated: true,
    });
  };

  const handleAmPmScroll = (event: any) => {
    const offsetY = event.nativeEvent.contentOffset.y;
    const itemHeight = 50; // height (44) + marginBottom (6)

    // Calculate the index based on the scroll offset
    const index = Math.round((offsetY + 22 - itemHeight / 2) / itemHeight);

    const period = index === 0 ? "AM" : "PM";

    const [time] = selectedTime.split(/AM|PM/);

    // Temporarily disable auto-scroll while updating
    isUserScrolling.current = true;
    setSelectedTime(time + period);
    handleScrollEnd();
     // Manually snap to the calculated index
     ampmListRef.current?.scrollTo({
      y: index * 50 + 3, // Adjust for centering
      animated: true,
    });
  };

  // Dismiss keyboard when tapping outside input fields
  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  // Validate form
  const validateForm = () => {
    let isValid = true;
    const newErrors = { ...errors };

    // Validate medicine name
    if (!medicineName.trim()) {
      newErrors.medicineName = "Medicine name is required";
      isValid = false;
    } else {
      newErrors.medicineName = "";
    }

    // Validate form selection
    if (form.length === 0) {
      newErrors.form = "Medicine form is required";
      isValid = false;
    } else {
      newErrors.form = "";
    }

    // Validate custom days selection if custom repeat is selected
    if (repeat.includes("custom") && selectedDays.length === 0) {
      newErrors.customDays = "Please select at least one day";
      isValid = false;
    } else {
      newErrors.customDays = "";
    }

    setErrors(newErrors);
    return isValid;
  };

  // Handle save reminder
  const handleSaveReminder = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Format time for API (convert from "08:00AM" to "08:00")
      const timeValue = selectedTime.replace(/([0-9]{2}:[0-9]{2})(AM|PM)/, (_, time, period) => {
        const [hours, minutes] = time.split(':').map(Number);
        let hour = hours;

        // Convert to 24-hour format
        if (period === 'PM' && hours < 12) {
          hour += 12;
        } else if (period === 'AM' && hours === 12) {
          hour = 0;
        }

        return `${hour.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
      });

      // Format custom repeat days if custom is selected
      let customRepeatValue = '';
      if (repeat.includes('custom') && selectedDays.length > 0) {
        customRepeatValue = selectedDays.join(', ');
      }

      // Create reminder object for API
      const reminderData = {
        medicineName: medicineName,
        dosage: dosage || '',
        form: form.length > 0 ? form[0] : '',
        repeat: repeat.length > 0 ? repeat[0] : 'once',
        customRepeat: repeat.includes('custom') ? customRepeatValue : undefined,
        guidiance: guidance?.trim() || '',
        time: timeValue
      };

      console.log('Sending reminder data:', reminderData);

      // Call the API to add the reminder
      const response = await AddReminder(reminderData);
      console.log('API response:', response);

      // Show success toast
      handleToast("Reminder created successfully", "success");

      // Navigate back
      navigation.goBack();
    } catch (error) {
      console.error("Failed to save reminder:", error);

      // Check if it's a network error
      if (getErrorType(error) === 'network' || getErrorType(error) === 'timeout') {
        handleNetworkError(error);
      } else {
        handleToast("Failed to create reminder", "error");
      }
    } finally {
      setLoading(false);
    }
  };

  // If there's a network error, show the NetworkErrorView
  if (showNetworkError) {
    return (
      <NetworkErrorView
        errorType={getErrorType(networkError)}
        message={getErrorMessage(networkError)}
        onRetry={handleRetry}
      />
    );
  }

  return (
    <TouchableWithoutFeedback onPress={dismissKeyboard}>
      <View style={styles.mainContainer}>
        <Div>
          <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : "height"}
            style={{ flex: 1 }}
          >
            {/* Header */}
            <AppHeader
              navigation={navigation}
              showBackButton={true}
              title="Medicine Reminder"
              navStyle={{ justifyContent: "flex-start", paddingHorizontal: 16 }}
              titleStyle={{ color: colors.black }}
            />

            <View style={styles.container}>
              {/* Time Selection */}
              <P style={styles.timeLabel}>
                Medicine to be taken by {selectedTime}
              </P>
              <View style={styles.timePickerContainer}>
                {/* Center Selection Indicator */}
                <View style={styles.centerSelectionIndicator} />
                <View style={styles.timePickerRow}>
                  {/* AM/PM Column */}
                  <View style={styles.ampmColumn}>
                    <ScrollView
                      ref={ampmListRef}
                      showsVerticalScrollIndicator={false}
                      contentContainerStyle={styles.ampmScrollContent}
                      onMomentumScrollEnd={handleAmPmScroll}
                      scrollEventThrottle={16}
                    >
                      {["AM", "PM"].map((period, index) => {
                        const isSelected = selectedTime.includes(period);
                        return (
                          <TouchableOpacity
                            key={`period-${period}`}
                            style={[styles.ampmButton]}
                            onPress={() => {
                              const [time] = selectedTime.split(/AM|PM/);
                              setSelectedTime(time + period);
                              // Scroll to center when selected manually
                              ampmListRef.current?.scrollTo({
                                y: index * 50 + 3, // Adjust for centering
                                animated: true,
                              });
                            }}
                          >
                            <P
                              style={[
                                styles.ampmButtonText,
                                isSelected && styles.selectedAmPmButtonText,
                              ]}
                            >
                              {period}
                            </P>
                          </TouchableOpacity>
                        );
                      })}
                    </ScrollView>
                  </View>

                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      width: "40%",
                    }}
                  >
                    {/* Hours Column */}
                    <View style={styles.hoursColumn}>
                      <ScrollView
                        ref={hourListRef}
                        showsVerticalScrollIndicator={false}
                        contentContainerStyle={styles.hoursScrollContent}
                        onMomentumScrollEnd={handleHourScroll}
                        scrollEventThrottle={16}
                      >
                        {Array.from({ length: 12 }, (_, i) =>
                          String(i + 1).padStart(2, "0")
                        ).map((hour, index) => {
                          const isSelected = selectedTime.startsWith(hour);
                          return (
                            <TouchableOpacity
                              key={`hour-${hour}`}
                              style={[styles.hourButton]}
                              onPress={() => {
                                const period = selectedTime.includes("AM")
                                  ? "AM"
                                  : "PM";
                                const [_, minutes] = selectedTime.split(":");
                                const newMinutes = minutes
                                  ? minutes.replace(/AM|PM/, "")
                                  : "00";
                                setSelectedTime(
                                  `${hour}:${newMinutes}${period}`
                                );
                                // Scroll to center when selected manually
                                hourListRef.current?.scrollTo({
                                  y: index * 50 + 3, // Adjust for centering
                                  animated: true,
                                });
                              }}
                            >
                              <P
                                style={[
                                  styles.hourButtonText,
                                  isSelected && styles.selectedHourButtonText,
                                ]}
                              >
                                {hour}
                              </P>
                            </TouchableOpacity>
                          );
                        })}
                      </ScrollView>
                    </View>

                    {/* Minutes Column */}
                    <View style={styles.minutesColumn}>
                      <ScrollView
                        ref={minuteListRef}
                        showsVerticalScrollIndicator={false}
                        contentContainerStyle={styles.minutesScrollContent}
                        onMomentumScrollEnd={handleMinuteScroll}
                        scrollEventThrottle={16}
                      >
                        {Array.from({ length: 60 }, (_, i) =>
                          String(i).padStart(2, "0")
                        ).map((minute, index) => {
                          const minutePattern = new RegExp(`:${minute}[AP]M`);
                          const isSelected = minutePattern.test(selectedTime);
                          return (
                            <TouchableOpacity
                              key={`minute-${minute}`}
                              style={[styles.minuteButton]}
                              onPress={() => {
                                const period = selectedTime.includes("AM")
                                  ? "AM"
                                  : "PM";
                                const [hours] = selectedTime.split(":");
                                setSelectedTime(`${hours}:${minute}${period}`);
                                // Scroll to center when selected manually
                                minuteListRef.current?.scrollTo({
                                  y: index * 50 + 3, // Adjust for centering
                                  animated: true,
                                });
                              }}
                            >
                              <P
                                style={[
                                  styles.minuteButtonText,
                                  isSelected && styles.selectedMinuteButtonText,
                                ]}
                              >
                                {minute}
                              </P>
                            </TouchableOpacity>
                          );
                        })}
                      </ScrollView>
                    </View>
                  </View>
                </View>
              </View>
              {/* Medicine Details - Scrollable Form Section */}
              <ScrollView
                style={styles.formScrollContainer}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.formScrollContent}
              >
                <View style={styles.formContainer}>
                  <Input
                    label="Medicine Name"
                    value={medicineName}
                    onChangeText={setMedicineName}
                    error={!!errors.medicineName}
                    errorText={errors.medicineName}
                    contStyle={{ marginBottom: 16 }}
                  />
                  <Input
                    label="Dosage"
                    value={dosage}
                    onChangeText={setDosage}
                    contStyle={{ marginBottom: 16 }}
                  />
                  <SelectInput
                    label="Form"
                    placeholder="Tablet, Capsule, Syrup, Injection, etc."
                    options={formOptions}
                    selectedValues={form}
                    onSelect={setForm}
                    multiSelect={false}
                    contStyle={{ marginBottom: 16 }}
                  />

                  <SelectInput
                    label="Repeat"
                    placeholder="Select repeat option"
                    options={repeatOptions}
                    selectedValues={repeat}
                    onSelect={setRepeat}
                    multiSelect={false}
                    contStyle={{ marginBottom: 16 }}
                    error={!!errors.form}
                    errorText={errors.form}
                  />

                  {/* Custom Day Selector - Only show when custom repeat is selected */}
                  {repeat.includes("custom") && (
                    <DaySelector
                      label="Select Days"
                      selectedDays={selectedDays}
                      onDaySelect={setSelectedDays}
                      contStyle={{ marginBottom: 16 }}
                    />
                  )}

                  {/* Show error message for custom days if needed */}
                  {repeat.includes("custom") && errors.customDays && (
                    <P style={styles.errorText}>{errors.customDays}</P>
                  )}

                  <Input
                    label="Guidelines for Taking Medicine"
                    value={guidance}
                    onChangeText={setGuidance}
                    multiline={true}
                    numberOfLines={3}
                    textAlignVertical="top"
                    customInputStyle={{ height: 100, paddingTop: 12 }}
                    contStyle={{ marginBottom: isKeyboardVisible ? 20 : 80 }} // Adjust padding based on keyboard
                  />
                </View>
              </ScrollView>
            </View>

            {/* Update Button - Hidden when keyboard is visible */}
            {!isKeyboardVisible && (
              <View style={styles.buttonContainer}>
                <Button
                  btnText="Create Reminder"
                  onPress={handleSaveReminder}
                  style={styles.updateButton}
                  loading={loading}
                />
              </View>
            )}
          </KeyboardAvoidingView>
        </Div>
      </View>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "100%",
    alignSelf: "center",
    paddingHorizontal: 16,
  },
  contentContainer: {
    flexGrow: 1,
    paddingHorizontal: 16,
    paddingBottom: 100,
  },
  timeLabel: {
    fontSize: 12,
    fontFamily: fonts.dmSansMedium,
    color: colors.b2Brown,
    marginBottom: 16,
    paddingHorizontal: 5,
  },
  timePickerContainer: {
    marginBottom: 16,
    position: "relative",
  },
  centerSelectionIndicator: {
    position: "absolute",
    top: 78, // Matches paddingTop - this is where the first item starts
    left: 0,
    right: 0,
    height: 44, // Same as button height
    backgroundColor: "rgba(0, 122, 255, 0.15)",
    borderTopWidth: 2,
    borderBottomWidth: 2,
    borderColor: "rgba(0, 122, 255, 0.4)",
    borderRadius: 8,
    zIndex: 1,
    pointerEvents: "none", // Allow touches to pass through
  },
  timePickerRow: {
    flexDirection: "row",
    justifyContent: "space-around",
    position: "relative",
    zIndex: 2,
  },
  // AM/PM Column
  ampmColumn: {
    width: "25%",
    height: 200,
    justifyContent: "center",
  },
  ampmScrollContent: {
    paddingTop: 78, // Align with center selection indicator
    paddingBottom: 78,
  },
  ampmButton: {
    height: 44,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 6,
    borderRadius: 8,
  },
  ampmButtonText: {
    fontSize: 20,
    fontFamily: fonts.dmSansRegular,
    color: colors.gray1,
    lineHeight: 30,
  },
  selectedAmPmButtonText: {
    color: colors.primary,
    fontFamily: fonts.dmSansBold,
  },
  // Hours Column
  hoursColumn: {
    width: "37.5%",
    height: 200,
  },
  hoursScrollContent: {
    paddingTop: 78, // Align with center selection indicator
    paddingBottom: 78,
  },
  hourButton: {
    height: 44,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 8,
    marginBottom: 6,
  },
  selectedHourButton: {
    backgroundColor: colors.primary,
  },
  hourButtonText: {
    fontSize: 20,
    fontFamily: fonts.dmSansRegular,
    color: colors.gray1,
    lineHeight: 24,
  },
  selectedHourButtonText: {
    color: colors.primary,
    fontFamily: fonts.dmSansBold,
  },
  // Minutes Column
  minutesColumn: {
    width: "37.5%",
    height: 200,
  },
  minutesScrollContent: {
    paddingTop: 78, // Align with center selection indicator
    paddingBottom: 78,
  },
  minuteButton: {
    minHeight: 44,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 8,
    marginBottom: 6,
  },
  selectedMinuteButton: {
    backgroundColor: colors.primary,
  },
  minuteButtonText: {
    fontSize: 20,
    fontFamily: fonts.dmSansRegular,
    color: colors.gray1,
    lineHeight: 24,
  },
  selectedMinuteButtonText: {
    color: colors.primary,
    fontFamily: fonts.dmSansBold,
  },
  // Legacy styles (keeping for compatibility)
  timeColumn: {
    width: "30%",
  },
  timeHeader: {
    fontSize: 18,
    fontFamily: fonts.dmSansMedium,
    color: colors.gray1,
    marginBottom: 8,
    textAlign: "center",
  },
  timeButton: {
    height: 48,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 8,
    backgroundColor: "#F5F5F5",
    marginBottom: 8,
  },
  selectedTimeButton: {
    backgroundColor: colors.primary,
  },
  timeButtonText: {
    fontSize: 12,
    fontFamily: fonts.dmSansRegular,
    color: colors.gray1,
  },
  selectedTimeButtonText: {
    color: colors.white,
    fontFamily: fonts.dmSansBold,
  },
  formScrollContainer: {
    flex: 1,
    marginTop: 10,
  },
  formScrollContent: {
    paddingBottom: 100,
  },
  formContainer: {
    marginTop: 16,
  },
  buttonContainer: {
    position: "absolute",
    bottom: 10,
    left: 0,
    right: 0,
    paddingHorizontal: 16,
    backgroundColor: colors.white,
    paddingVertical: 8,
  },
  updateButton: {
    width: "100%",
    height: 50,
    borderRadius: 25,
  },
  errorText: {
    color: colors.error || 'red',
    fontSize: 12,
    fontFamily: fonts.dmSansRegular,
    marginTop: -12,
    marginBottom: 16,
    paddingHorizontal: 4,
  },
});
