import {
  Dimensions,
  StyleSheet,
  View,
  TouchableOpacity,
  StyleProp,
  TextStyle,
  ViewStyle,
} from "react-native";
import React from "react";
import P from "./P";
import { fonts } from "../Config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../Config/Svg";
import { colors } from "../Config/colors";

const { width } = Dimensions.get("window");

interface PProps {
  navigation?: any;
  text?: string;
  title?: string;
  subtitle?: string;
  subtitleStyle?: StyleProp<TextStyle>;
  iconComp?: any;
  contStyle?: StyleProp<ViewStyle>;
  navStyle?: StyleProp<ViewStyle>;
  showBorder?: boolean;
  showBackButton?: boolean;
  showBackArrow?: boolean;
  goHome?: boolean;
  cancel?: boolean;
  modalClose?: () => void; // Updated type for modalClose
  disabled?: boolean;
  goToScreen?: string;
  titleStyle?: StyleProp<TextStyle>
}

export default function AppHeader({
  navigation,
  text,
  title,
  subtitle,
  subtitleStyle,
  iconComp,
  contStyle,
  navStyle,
  showBorder,
  showBackButton = true,
  showBackArrow = true,
  goHome = false,
  cancel,
  disabled,
  modalClose,
  goToScreen,
  titleStyle
}: PProps) {
  const handlePress = () => {
    if (cancel && modalClose) {
      modalClose(); // Call the modalClose function
    } else if (goHome) {
      navigation.reset({
        index: 0,
        routes: [{ name: "BottomTabNavigator" }],
      });
    } else if (goToScreen) {
      navigation.navigate(goToScreen);
    } else {
      navigation.pop();
    }
  };

  return (
    <View style={[styles.navCont, contStyle, showBorder && styles.navBorder]}>
      <View style={[styles.nav, navStyle]}>
        {showBackButton && (
          <TouchableOpacity
            onPress={handlePress}
            style={{ flexDirection: "row", alignItems: "center" }}
            disabled={disabled}
          >
            {showBackArrow && (
              <SvgXml xml={svg.goBack} style={{ marginRight: 12 }} />
            )}
            {text && <P style={styles.navText}>{text}</P>}
          </TouchableOpacity>
        )}

        {title && (
          <View style={styles.titleContainer}>
            <P style={[styles.titleText, titleStyle]}>{title}</P>
            {subtitle && (
              <P style={[styles.subtitleText, subtitleStyle]}>{subtitle}</P>
            )}
          </View>
        )}
        <View style={styles.rightContainer}>{iconComp}</View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  navCont: {
    width,
    alignItems: "center",
    marginTop: 24,
    marginBottom: 16,
  },
  nav: {
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingTop: 2,
  },
  navText: {
    color: "#000",
    fontFamily: fonts.dmSansMedium,
  },
  navBorder: {
    borderBottomWidth: 1,
    borderColor: "#313030",
  },
  titleContainer: {
    // alignItems: "center",
    // justifyContent: "center",
  },
  titleText: {
    fontSize: 16,
    fontFamily: fonts.dmSansBold,
    color: colors.primary,
  },
  subtitleText: {
    fontSize: 14,
    fontFamily: fonts.dmSansRegular,
    color: "#666",
    textAlign: "center",
    marginTop: 4,
  },
  rightContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
  },
});
