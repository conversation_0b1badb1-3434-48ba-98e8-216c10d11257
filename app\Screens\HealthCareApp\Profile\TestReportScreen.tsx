import React, { useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  Image,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Alert,
  ActivityIndicator,
  Share,
} from "react-native";
import AppHeader from "../../../Components/AppHeader";
import TabSelector from "../../../Components/profile/ProfileTabSelector";
import { tabs } from "./tabs";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import Button from "../../../Components/Button";
import { colors } from "../../../Config/colors";
import { fonts } from "../../../Config/Fonts";
import {
  GetTestReport,
  DeleteTestReport,
} from "../../../RequestHandler.tsx/User";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../Config/Svg";
import { formatDateTime } from "../../../utils/DateUtils";
import { useToast } from "../../../Context/ToastContext";

const { height, width } = Dimensions.get("window");

// Define the TestReport interface
interface TestReport {
  id: string;
  name: string;
  doctorName: string;
  reportDate: string;
  reportFile: string;
  active: boolean;
  created_at: string;
  updated_at: string;
}

export default function TestReportScreen({ navigation }) {
  const [test, setTest] = useState<TestReport[]>([]);
  const [loading, setLoading] = useState(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const { handleToast } = useToast();

  const getTestReports = async () => {
    try {
      setLoading(true);
      const res = await GetTestReport();
      console.log(res);
      if (res.items) {
        setTest(res.items);
      }
    } catch (error) {
      console.log(error);
      handleToast("Failed to load test reports", "error");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteReport = (id: string) => {
    Alert.alert(
      "Delete Test Report",
      "Are you sure you want to delete this test report? This action cannot be undone.",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => deleteTestReport(id),
        },
      ]
    );
  };

  const deleteTestReport = async (id: string) => {
    try {
      setDeletingId(id);
      const response = await DeleteTestReport(id);
      console.log("Delete response:", response);
      // Remove the deleted report from the state
      setTest((prevReports) =>
        prevReports.filter((report) => report.id !== id)
      );
      handleToast("Test report deleted successfully", "success");
    } catch (error) {
      console.error("Error deleting test report:", error);
      handleToast("Failed to delete test report", "error");
    } finally {
      setDeletingId(null);
    }
  };

  // Function to share test report details
  const shareTestReport = async (report: TestReport) => {
    try {
      // Format the report details as text
      const reportDate = formatDateTime(report.reportDate);
      const shareMessage = `
Test Report: ${report.name}
Doctor: ${report.doctorName}
Date: ${reportDate}
View Report: ${report.reportFile}
      `.trim();

      // Use the Share API to share the text
      const result = await Share.share({
        message: shareMessage,
        title: `Test Report: ${report.name}`,
      });

      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // Shared with activity type of result.activityType
          handleToast(`Shared via ${result.activityType}`, "success");
        } else {
          // Shared
          handleToast("Report shared successfully", "success");
        }
      } else if (result.action === Share.dismissedAction) {
        // Dismissed
        console.log("Share dismissed");
      }
    } catch (error) {
      console.error("Error sharing report:", error);
      handleToast("Failed to share report", "error");
    }
  };

  useEffect(() => {
    getTestReports();
    // Refresh the list when navigating back to this screen
    const unsubscribe = navigation.addListener("focus", () => {
      getTestReports();
    });
    return unsubscribe;
  }, [navigation]);
  return (
    <View style={{ flex: 1 }}>
      {/* Show loading indicator when fetching reports */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : test.length === 0 ? (
        <View style={styles.contentContainer}>
          <View style={styles.emptyContainer}>
            <Image
              source={require("../../../assets/no-report.png")}
              style={styles.illustration}
              resizeMode="contain"
            />
            <P style={styles.emptyText}>No Test Report available</P>
          </View>
          <View style={{ width: "90%", position: "absolute", bottom: 0 }}>
            <Button
              btnText="Add Report"
              onPress={() => {
                navigation.navigate("AddTestReportScreen");
              }}
              style={styles.addButton}
            />
          </View>
        </View>
      ) : (
        <>
          <ScrollView
            style={{
              width: "100%",
              minHeight: (80 * height) / 100,
              padding: 16,
            }}
          >
            {test.map((item) => (
              <View key={item.id} style={styles.reportCard}>
                <View style={styles.reportHeader}>
                  <P style={styles.reportName}>{item?.name}</P>
                  {/* Delete button with loading indicator */}
                  <TouchableOpacity
                    onPress={() => handleDeleteReport(item.id)}
                    disabled={deletingId === item.id}
                    style={styles.deleteButton}
                  >
                    {deletingId === item.id ? (
                      <ActivityIndicator size="small" color={colors.error} />
                    ) : (
                      <SvgXml xml={svg.trash} width={20} height={20} />
                    )}
                  </TouchableOpacity>
                </View>
                <View style={styles.reportDateContainer}>
                  <P style={styles.reportDateLabel}>
                    Report date:{" "}
                    <P style={styles.reportDateValue}>
                      {formatDateTime(item?.reportDate)}
                    </P>
                  </P>
                </View>
                <View style={styles.reportActions}>
                  <TouchableOpacity
                    style={styles.viewButton}
                    onPress={() => {
                      // Navigate to ViewTestReportScreen
                      navigation.navigate("ViewTestReportScreen", {
                        report: item
                      });
                    }}
                  >
                    <P style={styles.viewButtonText}>View report</P>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.shareButton}
                    onPress={() => shareTestReport(item)}
                  >
                    <P style={styles.shareButtonText}>Share</P>
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </ScrollView>
          <View style={styles.addButtonContainer}>
            <Button
              btnText="Add Report"
              onPress={() => {
                navigation.navigate("AddTestReportScreen");
              }}
              style={styles.addButton}
            />
          </View>
        </>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  headerContainer: {
    width: "100%",
    paddingHorizontal: 20,
  },
  completionText: {
    color: "#D4AF37",
    fontSize: 14,
    fontFamily: fonts.dmSansRegular,
    marginTop: 4,
  },
  contentContainer: {
    width: "100%",
    height: (75 * height) / 100,
    alignItems: "center",
    justifyContent: "center",
    paddingBottom: 100,
    marginTop: (2 * height) / 100,
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
  },
  illustration: {
    width: (70 * width) / 100,
    height: (70 * width) / 100,
    marginBottom: 24,
  },
  emptyText: {
    fontSize: 14,
    color: colors.black,
    fontFamily: fonts.dmSansMedium,
    marginBottom: 24,
  },
  addButton: {
    // width: "100%",
    // height: 56,
    // borderRadius: 28,
  },
  // Loading styles
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
    height: (60 * height) / 100,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
    color: colors.primary,
    fontFamily: fonts.dmSansMedium,
  },
  // Report card styles
  reportCard: {
    width: "100%",
    backgroundColor: colors.white,
    borderRadius: 12,
    marginBottom: 16,
  },
  reportHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  reportName: {
    fontSize: 14,
    fontFamily: fonts.dmSansBold,
    color: colors.black,
    flex: 1,
  },
  deleteButton: {
    padding: 8,
    borderRadius: 20,
  },
  reportDateContainer: {
    marginBottom: 16,
  },
  reportDateLabel: {
    fontSize: 14,
    color: colors.gray1,
    fontFamily: fonts.dmSansRegular,
  },
  reportDateValue: {
    fontSize: 14,
    color: colors.black,
    fontFamily: fonts.dmSansMedium,
  },
  reportActions: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  viewButton: {
    width: "48%",
    height: 40,
    borderWidth: 1,
    borderColor: colors.primary,
    borderRadius: 100,
    alignItems: "center",
    justifyContent: "center",
  },
  viewButtonText: {
    color: colors.primary,
    fontFamily: fonts.dmSansMedium,
    fontSize: 14,
  },
  shareButton: {
    width: "48%",
    height: 40,
    borderRadius: 100,
    backgroundColor: colors.primary,
    alignItems: "center",
    justifyContent: "center",
  },
  shareButtonText: {
    color: colors.white,
    fontFamily: fonts.dmSansMedium,
    fontSize: 14,
  },
  addButtonContainer: {
    width: "100%",
    position: "absolute",
    alignSelf: "center",
    bottom: 0,
    paddingHorizontal: 16,
    backgroundColor: colors.white,
    paddingVertical: 16,
  },
  formContainer: {
    width: "100%",
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 24,
    marginTop: 24,
    marginBottom: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  formTitle: {
    fontSize: 20,
    fontFamily: fonts.dmSansBold,
    color: colors.black,
    marginBottom: 20,
    textAlign: "center",
  },
  label: {
    fontSize: 14,
    fontFamily: fonts.dmSansMedium,
    color: colors.black,
    marginBottom: 6,
    marginTop: 12,
  },
  input: {
    width: "100%",
    height: 48,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.grayLx || "#E5E5E5",
    backgroundColor: colors.grayLx || "#F7F7F7",
    paddingHorizontal: 16,
    fontSize: 14,
    fontFamily: fonts.dmSansRegular,
    marginBottom: 8,
  },
  uploadBox: {
    width: "100%",
    height: 100,
    borderRadius: 12,
    borderWidth: 1.5,
    borderColor: colors.b2Brown,
    backgroundColor: colors.grayLx || "#F7F7F7",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 8,
    marginBottom: 24,
    borderStyle: "dashed",
  },
  uploadText: {
    color: colors.b2Brown,
    fontSize: 14,
    textAlign: "center",
    fontFamily: fonts.dmSansMedium,
  },
  uploadSubText: {
    color: colors.gray1,
    fontSize: 14,
    textAlign: "center",
    fontFamily: fonts.dmSansRegular,
  },
  updateButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
    marginTop: 8,
  },
});
