import React, { act, useState } from "react";
import {
  Dimensions,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { colors } from "../Config/colors";
import Div from "../Components/Div";
import H4 from "../Components/H4";
import P from "../Components/P";
import { fonts } from "../Config/Fonts";
import Button from "../Components/Button";

const { width, height } = Dimensions.get("window");

export default function IntroScreen({ navigation, route }) {
  const option = [
    { text: "Patient", img: require("../assets/patience.png") },
    { text: "Practitioner", img: require("../assets/doctors.png") },
  ];

  const { path } = route?.params || "";

  const [activeOption, setActiveOption] = useState("");
  return (
    <View style={{ flex: 1, backgroundColor: colors.white }}>
      <Div>
        <View style={styles.container}>
          <ScrollView
            style={{ width: "100%" }}
            contentContainerStyle={{ minHeight: "100%" }}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
          >
            <H4 style={{ marginTop: (15 * width) / 100 }}>
              Choose Your Role to Get Started
            </H4>
            <View style={{ width: "100%", marginTop: (8 * height) / 100 }}>
              {option.map((item, index) => (
                <TouchableOpacity
                  onPress={() => {
                    setActiveOption(item.text);
                  }}
                  key={index}
                  style={{
                    width: "100%",
                    minHeight: (25 * height) / 100,
                    backgroundColor:
                      activeOption === item.text
                        ? colors.primary
                        : colors.white,
                    padding: 16,
                    borderRadius: 16,
                    alignItems: "center",
                    marginTop: index === 1 ? 8 : 0,
                  }}
                >
                  <P
                    style={{
                      fontFamily: fonts.dmSansSemibold,
                      color:
                        activeOption === item.text
                          ? colors.white
                          : colors.black,
                    }}
                  >
                    {item.text}
                  </P>
                  <View
                    style={{
                      width: "100%",
                      marginTop: 14,
                      alignItems: "center",
                      justifyContent: "center",
                      backgroundColor: colors.white,
                      borderRadius: 16,
                    }}
                  >
                    <Image
                      source={item.img}
                      style={{ width: 149, height: 149 }}
                    />
                  </View>
                </TouchableOpacity>
              ))}
            </View>
            {activeOption !== "" && (
              <View
                style={{
                  width: "100%",
                  position: "absolute",
                  bottom: (5 * height) / 100,
                }}
              >
                <Button
                  btnText="Continue"
                  onPress={() => {
                    if (activeOption?.toLowerCase() === "patient") {
                      navigation.navigate(path, {
                        isPractional: false,
                      });
                    } else {
                      navigation.navigate(path, {
                        isPractional: true,
                      });
                    }
                  }}
                />
              </View>
            )}
          </ScrollView>
        </View>
      </Div>
    </View>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: "90%",
    height: "100%",
    backgroundColor: colors.white,
  },
});
