import React, { useState, useEffect } from "react";
import { StyleSheet, View, ScrollView } from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import AppHeader from "../../../Components/AppHeader";
import SelectInput from "../../../Components/SelectInput";
import Input from "../../../Components/Input";
import Loader from "../../../Components/Loader";
import {
  // GetMedications, // Commented out since using text input
  // GetHabits, // Commented out as requested
  GetDietRestrictions,
  // GetFoodPreferences, // Commented out as requested
} from "../../../RequestHandler.tsx/Auth";
import { UpdateMedicalHistory } from "../../../RequestHandler.tsx/User";
import NetworkErrorView from "../../../Components/NetworkErrorView";
import {
  getErrorType,
  getErrorMessage,
} from "../../../utils/networkErrorHandler";
import { useToast } from "../../../Context/ToastContext";

// Therapy options using backend enum
const therapyOptions = [
  { label: "Yes", value: "yes" },
  { label: "No", value: "no" },
];

export default function MedicalHistoryScreen3({ navigation, route }) {
  // Get medical history data from previous screens
  const { medicalHistory } = route.params || { medicalHistory: {} };
  // Toast context for showing notifications
  const { handleToast } = useToast();
  // State for all selections
  const [medications, setMedications] = useState<string>(""); // Changed to text input
  const [therapy, setTherapy] = useState<string[]>([]);
  // const [smokingAlcohol, setSmokingAlcohol] = useState<string[]>([]); // Commented out as requested
  // const [smokingAlcoholIds, setSmokingAlcoholIds] = useState<string[]>([]); // Commented out as requested
  const [dietRestriction, setDietRestriction] = useState<string[]>([]);
  const [dietRestrictionIds, setDietRestrictionIds] = useState<string[]>([]);
  // const [foodPreference, setFoodPreference] = useState<string[]>([]); // Commented out as requested
  // const [foodPreferenceIds, setFoodPreferenceIds] = useState<string[]>([]); // Commented out as requested
  // State for API options (medications options removed since using text input)
  // const [smokingAlcoholOptions, setSmokingAlcoholOptions] = useState<any[]>([]); // Commented out as requested
  const [dietRestrictionOptions, setDietRestrictionOptions] = useState<any[]>(
    []
  );
  // const [foodPreferenceOptions, setFoodPreferenceOptions] = useState<any[]>([]); // Commented out as requested
  // Loading and error states
  const [loading, setLoading] = useState<boolean>(true);
  const [submitting, setSubmitting] = useState<boolean>(false);
  // Network error states
  const [networkError, setNetworkError] = useState<any>(null);
  const [showNetworkError, setShowNetworkError] = useState<boolean>(false);
  // Function to handle network errors
  const handleNetworkError = (error: any) => {
    setNetworkError(error);
    setShowNetworkError(true);
  };

  // Fetch all data from APIs
  const fetchAllData = async () => {
    try {
      setLoading(true);

      // Medications fetch removed - now using text input

      // Fetch habits (smoking/alcohol) - commented out as requested
      /* const habitsResponse = await GetHabits();
      if (habitsResponse && Array.isArray(habitsResponse)) {
        // Transform API response to options format
        const options = habitsResponse.map((item) => ({
          label: item.name,
          value: item.name,
          id: item.id,
        }));
        setSmokingAlcoholOptions(options);
      } else {
        handleToast("Failed to load habits", "error");
      } */

      // Fetch diet restrictions
      const dietResponse = await GetDietRestrictions();
      if (dietResponse && Array.isArray(dietResponse)) {
        // Transform API response to options format
        const options = dietResponse.map((item) => ({
          label: item.name,
          value: item.name,
          id: item.id,
        }));
        setDietRestrictionOptions(options);
      } else {
        handleToast("Failed to load diet restrictions", "error");
      }

      // Fetch food preferences - commented out as requested
      /* const foodResponse = await GetFoodPreferences();
      if (foodResponse && Array.isArray(foodResponse)) {
        // Transform API response to options format
        const options = foodResponse.map((item) => ({
          label: item.name,
          value: item.name,
          id: item.id,
        }));
        setFoodPreferenceOptions(options);
      } else {
        handleToast("Failed to load food preferences", "error");
      } */
    } catch (error) {
      console.log("Error fetching data:", error);

      // Check if it's a network error
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        handleNetworkError(error);
      } else {
        handleToast("Failed to load data", "error");
      }
    } finally {
      setLoading(false);
    }
  };

  // Function to retry all API calls
  const handleRetryAllApiCalls = () => {
    setShowNetworkError(false);
    setNetworkError(null);

    // Retry all API calls
    fetchAllData();
  };

  // Call API function on component mount
  useEffect(() => {
    fetchAllData();
  }, []);

  // Medications handler removed - now using text input

  // Handle selection of smoking/alcohol - commented out as requested
  /* const handleSmokingAlcoholSelect = (selected: string[]) => {
    setSmokingAlcohol(selected);

    // Get the IDs of the selected smoking/alcohol
    const ids = selected
      .map((value) => {
        const option = smokingAlcoholOptions.find((opt) => opt.value === value);
        return option ? option.id : null;
      })
      .filter((id) => id !== null);

    setSmokingAlcoholIds(ids);
  }; */

  // Handle selection of diet restriction
  const handleDietRestrictionSelect = (selected: string[]) => {
    setDietRestriction(selected);

    // Get the IDs of the selected diet restriction
    const ids = selected
      .map((value) => {
        const option = dietRestrictionOptions.find(
          (opt) => opt.value === value
        );
        return option ? option.id : null;
      })
      .filter((id) => id !== null);

    setDietRestrictionIds(ids);
  };

  // Handle selection of food preference - commented out as requested
  /* const handleFoodPreferenceSelect = (selected: string[]) => {
    setFoodPreference(selected);

    // Get the IDs of the selected food preference
    const ids = selected
      .map((value) => {
        const option = foodPreferenceOptions.find((opt) => opt.value === value);
        return option ? option.id : null;
      })
      .filter((id) => id !== null);

    setFoodPreferenceIds(ids);
  }; */

  const handleSaveDetails = async () => {
    try {
      setSubmitting(true);

      // Format the data according to the required API format
      const medicalHistoryData = {
        medications: medications, // Now using text field
        dietRestrictions: dietRestrictionIds,
        // habits: smokingAlcoholIds, // Commented out as requested
        // foodPreferences: foodPreferenceIds, // Commented out as requested
        recoverySupport: therapy[0] || "no", // Default to "no" as per enum
      };
      console.log("Submitting Final Medical History:", medicalHistoryData);
      const response = await UpdateMedicalHistory(medicalHistoryData);
      if (response.active) {
        handleToast("Medical history updated successfully", "success");
        navigation.navigate("BottomTabNavigator");
      }
    } catch (error) {
      console.log("Error updating medical history:", error);
      try {
        // Safely check error type
        const errorType = getErrorType(error);
        if (errorType === "network" || errorType === "timeout") {
          handleNetworkError(error);
        } else {
          // Safely get error message
          let errorMessage = "Unknown error";
          try {
            if (error && typeof error === 'object') {
              if (error.message && typeof error.message === 'string') {
                errorMessage = error.message;
              } else if (error.message) {
                errorMessage = String(error.message);
              } else if (error.toString && typeof error.toString === 'function') {
                errorMessage = error.toString();
              }
            }
          } catch (e) {
            console.log("Error extracting error message:", e);
          }

          handleToast("Failed to update medical history: " + errorMessage, "error");
        }
      } catch (handlingError) {
        console.log("Error in error handling:", handlingError);
        handleToast("Failed to update medical history", "error");
      }
    } finally {
      setSubmitting(false);
    }
  };

  // If there's a network error, show the NetworkErrorView
  if (showNetworkError) {
    return (
      <NetworkErrorView
        errorType={getErrorType(networkError)}
        message={getErrorMessage(networkError)}
        onRetry={handleRetryAllApiCalls}
      />
    );
  }

  return (
    <View style={styles.mainContainer}>
      <Div>
        <View style={styles.container}>
          <ScrollView
            style={{ width: "100%" }}
            contentContainerStyle={{ paddingBottom: 100 }}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
          >
            {/* Header */}
            <View style={{ width: "100%" }}>
              <AppHeader navigation={navigation} />
            </View>

            {/* Title */}
            <H4 style={styles.title}>Medical History</H4>
            {/* Current Status Section */}
            <View style={styles.section}>
              <H4 style={styles.sectionTitle}>Current Status</H4>
              {/* Medications */}
              <Input
                label="Medications Currently Taking"
                placeholder="Enter medications (e.g., Aspirin, Metformin, etc.)"
                value={medications}
                onChangeText={setMedications}
                contStyle={styles.selectInput}
                multiline={true}
                numberOfLines={3}
                textAlignVertical="top"
              />
              {/* Therapy */}
              <SelectInput
                label="Therapy"
                placeholder="Select"
                options={therapyOptions}
                selectedValues={therapy}
                onSelect={setTherapy}
                multiSelect={false}
                contStyle={styles.selectInput}
              />
            </View>

            {/* Lifestyle Section */}
            <View style={styles.section}>
              <H4 style={styles.sectionTitle}>Lifestyle</H4>

              {/* Smoking/Alcohol - commented out as requested */}
              {/* <SelectInput
                label="Smoking/Alcohol Use"
                placeholder="Select"
                options={smokingAlcoholOptions}
                selectedValues={smokingAlcohol}
                onSelect={handleSmokingAlcoholSelect}
                multiSelect={false}
                contStyle={styles.selectInput}
              /> */}

              {/* Diet Restriction */}
              <SelectInput
                label="Diet Restriction"
                placeholder="Select"
                options={dietRestrictionOptions}
                selectedValues={dietRestriction}
                onSelect={handleDietRestrictionSelect}
                multiSelect={false}
                contStyle={styles.selectInput}
              />

              {/* Food Preference - commented out as requested */}
              {/* <SelectInput
                label="Food Preference"
                placeholder="Select"
                options={foodPreferenceOptions}
                selectedValues={foodPreference}
                onSelect={handleFoodPreferenceSelect}
                multiSelect={false}
                contStyle={styles.selectInput}
              /> */}
            </View>

            {/* Save Button */}
            <View style={styles.buttonContainer}>
              <Button
                btnText={submitting ? "Saving..." : "Save Details"}
                onPress={handleSaveDetails}
                disabled={submitting || loading}
              />
            </View>
          </ScrollView>
        </View>
      </Div>
      {(loading || submitting) && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    padding: 16,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 16,
  },
  loadingText: {
    marginTop: 8,
    color: colors.gray,
    fontFamily: fonts.dmSansRegular,
  },
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "90%",
    height: "100%",
    backgroundColor: colors.white,
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontFamily: fonts.dmSansBold,
    marginTop: 16,
    marginBottom: 24,
    alignSelf: "flex-start",
  },
  section: {
    width: "100%",
    marginTop: 8,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: fonts.dmSansBold,
    marginBottom: 16,
  },
  selectInput: {
    marginBottom: 16,
  },
  buttonContainer: {
    width: "100%",
    marginTop: 16,
  },
});
