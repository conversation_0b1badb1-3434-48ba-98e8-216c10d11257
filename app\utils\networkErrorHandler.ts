/**
 * Utility functions for handling network errors and timeouts
 */

/**
 * Determines if an error is a network error
 * @param error The error to check
 * @returns True if the error is a network error
 */
export const isNetworkError = (error: any): boolean => {
  if (!error) return false;

  // Check for common network error messages
  let errorMessage = '';
  try {
    errorMessage = error && error.message && typeof error.message === 'string' ? error.message.toLowerCase() : '';
  } catch (e) {
    console.log('Error getting error message:', e);
  }

  return (
    errorMessage.includes('network') ||
    errorMessage.includes('connection') ||
    errorMessage.includes('offline') ||
    errorMessage.includes('internet') ||
    (error && error.name === 'NetworkError') ||
    (error && error.code === 'NETWORK_ERROR') ||
    (error && error.code === 'ENOTFOUND') ||
    (error && error.code === 'ECONNREFUSED') ||
    (error && error.code === 'ECONNRESET') ||
    (error && error.code === 'ECONNABORTED') ||
    (error && error.code === 'ETIMEDOUT') ||
    (error && error.code === 'ERR_NETWORK')
  );
};

/**
 * Determines if an error is a timeout error
 * @param error The error to check
 * @returns True if the error is a timeout error
 */
export const isTimeoutError = (error: any): boolean => {
  if (!error) return false;

  // Check for common timeout error messages
  let errorMessage = '';
  try {
    errorMessage = error && error.message && typeof error.message === 'string' ? error.message.toLowerCase() : '';
  } catch (e) {
    console.log('Error getting error message:', e);
  }

  return (
    errorMessage.includes('timeout') ||
    errorMessage.includes('timed out') ||
    (error && error.code === 'TIMEOUT') ||
    (error && error.code === 'ETIMEDOUT') ||
    (error && error.code === 'ESOCKETTIMEDOUT') ||
    (error && error.code === 'ECONNABORTED')
  );
};

/**
 * Determines if an error is a server error
 * @param error The error to check
 * @returns True if the error is a server error
 */
export const isServerError = (error: any): boolean => {
  if (!error) return false;

  // Check for server errors (5xx status codes)
  return (
    (error && error.status >= 500) ||
    (error && error.response && error.response.status >= 500) ||
    (error && error.code === 'SERVER_ERROR')
  );
};

/**
 * Determines the type of error
 * @param error The error to check
 * @returns The error type ('network', 'timeout', 'server', or 'generic')
 */
export const getErrorType = (error: any): 'network' | 'timeout' | 'server' | 'generic' => {
  if (isNetworkError(error)) return 'network';
  if (isTimeoutError(error)) return 'timeout';
  if (isServerError(error)) return 'server';
  return 'generic';
};

/**
 * Gets a user-friendly error message based on the error
 * @param error The error to get a message for
 * @returns A user-friendly error message
 */
export const getErrorMessage = (error: any): string => {
  const errorType = getErrorType(error);

  switch (errorType) {
    case 'network':
      return 'Please check your internet connection and try again.';
    case 'timeout':
      return 'The server is taking too long to respond. Please try again later.';
    case 'server':
      return 'We\'re experiencing issues with our server. Please try again later.';
    case 'generic':
    default:
      // Try to get a meaningful message from the error
      try {
        if (error && error.message && typeof error.message === 'string') {
          return error.message;
        }
        // If error.message exists but is not a string, try to convert it
        if (error && error.message) {
          return String(error.message);
        }
      } catch (e) {
        console.log('Error getting error message:', e);
      }
      return 'An unexpected error occurred. Please try again.';
  }
};
