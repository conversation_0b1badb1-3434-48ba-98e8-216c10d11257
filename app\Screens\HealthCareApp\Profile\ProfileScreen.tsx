import React, { useContext, useEffect, useState } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  Image,
  ScrollView,
  StatusBar,
  Alert,
  ActivityIndicator,
  Platform,
} from "react-native";
import { colors } from "../../../Config/colors";
import { fonts } from "../../../Config/Fonts";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../Config/Svg";
import { CredentailsContext } from "../../../Context/CredentailsContext";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import Div from "../../../Components/Div";
import { GetUserProfile, UpdateUserDetails } from "../../../RequestHandler.tsx/User";
import * as ImagePicker from "expo-image-picker";
import { uploadImageToCloudinary } from "../../../Services/CloudinaryService";
import { useToast } from "../../../Context/ToastContext";

const { height } = Dimensions.get("window");

// Profile menu items
const menuItems = [
  {
    id: 1,
    title: "My medical Records",
    icon: <SvgXml xml={svg.sheet} />,
    route: "ProfileSettingsScreen",
    bgColor: "#C5D8EF",
  },
  {
    id: 2,
    title: "Appointment Codes",
    icon: <SvgXml xml={svg.qr_code} />,
    // route: "AppointmentCodesScreen",
    bgColor: "#C5D8EF",
  },
  {
    id: 3,
    title: "Manage Address",
    icon: <SvgXml xml={svg.mapAlt} />,
    // route: "ManageAddressScreen",
    bgColor: "#C5D8EF",
  },
  {
    id: 4,
    title: "Medicine Reminder",
    icon: <SvgXml xml={svg.b_bell} />,
    route: "MedicineReminderScreen",
    bgColor: "#C5D8EF",
  },
  {
    id: 5,
    title: "Settings",
    icon: <SvgXml xml={svg.settings} />,
    // route: "SettingsScreen",
    bgColor: "#C5D8EF",
  },
  {
    id: 6,
    title: "Help and Support",
    icon: <SvgXml xml={svg.headset} />,
    // route: "HelpSupportScreen",
    bgColor: "#C5D8EF",
  },
  {
    id: 7,
    title: "Logout",
    icon: <SvgXml xml={svg.logout} />,
    route: "Logout",
    bgColor: "#E3A7A1",
  },
];

export default function ProfileScreen({ navigation }) {
  const { storedCredentails, setStoredCredentails } =
    useContext(CredentailsContext);
  const { handleToast } = useToast();
  const [details, setDetails] = useState<any>({});
  const [email, setEmail] = useState("...");
  const [avatar, setAvatar] = useState(null);
  const [name, setName] = useState("...");
  const [uploadingImage, setUploadingImage] = useState(false);
  // Handle logout
  const handleLogout = async () => {
    try {
      await GoogleSignin.signOut();
      await AsyncStorage.removeItem("cookies##$$");
      // @ts-ignore
      setStoredCredentails(null);
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  // Handle menu item press
  const handleMenuItemPress = (route: string) => {
    if (route === "Logout") {
      handleLogout();
    } else {
      navigation.navigate(route);
    }
  };

  const getUserDetails = async () => {
    try {
      const res = await GetUserProfile();
      console.log(res);
      if (res.email) {
        setDetails(res);
        setEmail(res.email);
        setName(`${res.firstname} ${res.lastname}`);
        setAvatar({ uri: res.avatar });
      }
    } catch (error) {
      console.log(error);
      handleToast("Failed to load profile details", "error");
    }
  };

  // Handle profile image update
  const handleProfileImageUpdate = async () => {
    try {
      // Request permission if needed (for Android)
      if (Platform.OS !== 'web') {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission Required', 'Sorry, we need camera roll permissions to make this work!');
          return;
        }
      }

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: "Images" as any, // Using 'as any' to avoid deprecation warning
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.5,
        base64: true,
      });
      if (!result.canceled && result.assets?.length > 0) {
        // Set local image preview
        const imageUri = result.assets[0].uri;
        setAvatar({ uri: imageUri });
        // Start upload
        setUploadingImage(true);
        handleToast("Uploading profile picture...", "success");

        try {
          // Upload to Cloudinary
          const cloudinaryUrl = await uploadImageToCloudinary(result.assets[0].base64);

          // Update user profile with new avatar URL
          const updateResponse = await UpdateUserDetails({
            avatar: cloudinaryUrl,
          });
          console.log("Profile update response:", updateResponse);
          // Update local state with the new avatar URL
          setAvatar({ uri: cloudinaryUrl });
          handleToast("Profile picture updated successfully", "success");
        } catch (error) {
          console.error("Error updating profile picture:", error);
          handleToast("Failed to update profile picture", "error");

          // Revert to previous avatar if update fails
          getUserDetails();
        } finally {
          setUploadingImage(false);
        }
      }
    } catch (error) {
      console.error("Error picking image:", error);
      handleToast("Failed to pick image", "error");
      setUploadingImage(false);
    }
  };

  useEffect(() => {
    getUserDetails();
  }, []);

  // Get user data from stored credentials
  return (
    <View style={styles.container}>
      <Div>
        {/* Header */}
        <View style={styles.header}>
          <H4 style={styles.headerTitle}>My Profile</H4>

          {/* Header Icons */}
          <View style={styles.headerIcons}>
            <TouchableOpacity style={styles.iconButton}>
              <SvgXml xml={svg.mail} />
            </TouchableOpacity>

            <TouchableOpacity style={styles.iconButton}>
              <SvgXml xml={svg.bell} />
            </TouchableOpacity>
          </View>
        </View>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={{paddingBottom: 100}}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
        >
          {/* Profile Card */}
          <View style={styles.profileCard}>
            {/* Profile Image and Edit Button */}
            <View style={styles.profileImageContainer}>
              <Image
                source={avatar ? avatar : require("../../../assets/user.png")}
                style={styles.profileImage}
              />
              <TouchableOpacity
                style={styles.editProfileButton}
                onPress={handleProfileImageUpdate}
                disabled={uploadingImage}
              >
                {uploadingImage ? (
                  <ActivityIndicator size="small" color={colors.primary} />
                ) : (
                  <SvgXml xml={svg.pencil} />
                )}
              </TouchableOpacity>
            </View>

            {/* User Info */}
            <View style={styles.userInfo}>
              <H4 style={styles.userName}>{name}</H4>
              <P style={styles.userEmail}>{email}</P>
            </View>

            {/* Menu Items */}
            <View style={styles.menuContainer}>
              {menuItems.map((item) => (
                <TouchableOpacity
                  key={item.id}
                  style={[styles.menuItem]}
                  onPress={() => {
                    if (item.route) {
                      handleMenuItemPress(item.route);
                    }
                  }}
                >
                  <View style={styles.menuItemLeft}>
                    <View
                      style={[
                        styles.menuItemIcon,
                        {
                          backgroundColor: item.bgColor,
                          borderWidth: item.title === "Logout" ? 0 : 1,
                        },
                      ]}
                    >
                      {item.icon}
                    </View>
                    <P
                      style={[
                        styles.menuItemTitle,
                        item.id === 7 && styles.logoutText,
                      ]}
                    >
                      {item.title}
                    </P>
                  </View>
                  <SvgXml
                    xml={
                      item.title === "Logout"
                        ? svg.red_chev_right
                        : svg.chev_right
                    }
                  />
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </ScrollView>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.primary,
  },
  header: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingTop: 21,
    // paddingBottom: 16,
  },
  headerTitle: {
    color: colors.white,
    fontSize: 24,
    fontFamily: fonts.dmSansBold,
  },
  headerIcons: {
    flexDirection: "row",
    alignItems: "center",
  },
  iconButton: {
    width: 44,
    height: 44,
    borderRadius: 12,
    backgroundColor: colors.white,
    justifyContent: "center",
    alignItems: "center",
    marginLeft: 16,
  },
  scrollView: {
    flex: 1,
    backgroundColor: colors.primary,
    width: "100%",
    paddingHorizontal: 16,
  },
  profileCard: {
    width: "100%",
    backgroundColor: colors.white,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    paddingHorizontal: 20,
    borderRadius: 16,
    minHeight: (50 * height) / 100,
    paddingBottom: 22,
    marginTop: (5 * height) / 100,
  },
  profileImageContainer: {
    width: "100%",
    position: "absolute",
    alignSelf: "flex-start",
    marginBottom: 10,
    top: -35,
    left: 23,
    justifyContent: "center",
  },
  profileImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "#E0E0E0",
  },
  editProfileButton: {
    position: "absolute",
    right: 0,
    backgroundColor: colors.white,
    width: 44,
    height: 44,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  userInfo: {
    marginBottom: 20,
    marginTop: 50,
  },
  userName: {
    fontSize: 16,
    fontFamily: fonts.dmSansBold,
    color: colors.black,
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    fontFamily: fonts.dmSansRegular,
    color: colors.gray1,
  },
  menuContainer: {
    marginTop: 10,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 15,
    paddingHorizontal: 15,
    borderRadius: 12,
    marginBottom: 8,
  },
  menuItemLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  menuItemIcon: {
    marginRight: 7,
    width: 44,
    height: 44,
    backgroundColor: "#C5D8EF",
    borderWidth: 1,
    borderColor: "#D8DADC",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 12,
  },
  menuItemTitle: {
    fontSize: 14,
    fontFamily: fonts.dmSansMedium,
    color: colors.black,
  },
  logoutText: {
    color: colors.error || "#F44336",
  },
});
