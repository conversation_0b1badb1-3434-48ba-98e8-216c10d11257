import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import NetworkErrorView from '../Components/NetworkErrorView';
import { getErrorType } from '../utils/networkErrorHandler';

/**
 * Higher-Order Component that adds network error handling to any component
 * @param WrappedComponent The component to wrap
 * @returns A new component with network error handling
 */
const withNetworkErrorHandling = (WrappedComponent: React.ComponentType<any>) => {
  return (props: any) => {
    const [error, setError] = useState<any>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);

    // Function to handle API calls with error handling
    const handleApiCall = async (apiCall: () => Promise<any>, onSuccess?: (data: any) => void) => {
      try {
        setIsLoading(true);
        setError(null);
        const result = await apiCall();
        if (onSuccess) {
          onSuccess(result);
        }
        return result;
      } catch (err) {
        console.error('API call failed:', err);
        setError(err);
        return null;
      } finally {
        setIsLoading(false);
      }
    };

    // Function to retry the last failed API call
    const handleRetry = () => {
      setError(null);
    };

    // If there's an error, show the NetworkErrorView
    if (error) {
      return (
        <NetworkErrorView
          errorType={getErrorType(error)}
          message={error.message}
          onRetry={handleRetry}
        />
      );
    }

    // Otherwise, render the wrapped component with the additional props
    return (
      <WrappedComponent
        {...props}
        handleApiCall={handleApiCall}
        isLoading={isLoading}
        error={error}
      />
    );
  };
};

export default withNetworkErrorHandling;
