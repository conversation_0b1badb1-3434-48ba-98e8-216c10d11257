import AsyncStorage from "@react-native-async-storage/async-storage";
import { getErrorType } from "../utils/networkErrorHandler";
import { handleAuthError } from "../utils/authErrorHandler";

export class RequestHandler {
  private BASE_URL = "https://onlymed-backend-api.onrender.com/";
  private TIMEOUT_MS = 60000; // 20 seconds timeout

  private async getUniqueID(): Promise<string | null> {
    return await AsyncStorage.getItem("uniqueID");
  }

  // Get device timezone
  private getDeviceTimezone(): string {
    try {
      // Use the JavaScript Intl API as a reliable way to get the timezone
      return Intl.DateTimeFormat().resolvedOptions().timeZone || "UTC";
    } catch (error) {
      console.error("Error getting device timezone:", error);
      // Return UTC as a fallback
      return "UTC";
    }
  }

  // Helper method to handle fetch with timeout
  private async fetchWithTimeout(
    url: string,
    options: RequestInit
  ): Promise<Response> {
    const controller = new AbortController();
    const { signal } = controller;

    // Create a timeout promise
    const timeout = new Promise<Response>((_, reject) => {
      setTimeout(() => {
        controller.abort();
        reject(new Error("Request timeout"));
      }, this.TIMEOUT_MS);
    });

    // Create the fetch promise with the abort signal
    const fetchPromise = fetch(url, {
      ...options,
      signal,
    });

    // Race between fetch and timeout
    try {
      return (await Promise.race([fetchPromise, timeout])) as Response;
    } catch (error) {
      if (error.name === "AbortError") {
        throw new Error("Request timeout");
      }
      throw error;
    }
  }

  public async get(
    path: string,
    token?: string,
    functions?: Array<(data: any) => void>
  ): Promise<any> {
    const UNIQUE_ID = await this.getUniqueID();
    try {
      const response = await this.fetchWithTimeout(`${this.BASE_URL}${path}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          "x-device-id": UNIQUE_ID || "",
          "x-timezone": this.getDeviceTimezone(),
          Authorization: token ? `Bearer ${token}` : "",
        },
      });

      const data = await response.json();

      // Call the callback functions with the data
      if (functions && functions.length > 0) {
        functions.forEach((func) => func(data));
      }

      // Check if the response was successful (status code 200-299)
      if (response.ok) {
        return data;
      } else {
        // For non-2xx responses, check for 401 status
        if (response.status === 401) {
          // Handle 401 Unauthorized error
          await handleAuthError({ status: 401, message: data.message || "Unauthorized" });
        }
        // Throw the error data
        throw data;
      }
    } catch (error) {
      // Determine the type of error
      const errorType = getErrorType(error);

      // Create a standardized error object
      const enhancedError = {
        type: errorType,
        message: error.message || "An error occurred",
        status: error.status || (errorType === 'network' ? "network_error" :
                               errorType === 'timeout' ? "timeout_error" :
                               errorType === 'server' ? "server_error" : "unknown_error"),
        originalError: error,
      };

      // Check for 401 status in the error
      if (error.status === 401 || (error.originalError && error.originalError.status === 401)) {
        await handleAuthError(enhancedError);
      }

      // Call the callback functions with the enhanced error
      if (functions && functions.length > 0) {
        functions.forEach((func) => func(enhancedError));
      }

      throw enhancedError;
    }
  }

  public async post(
    path: string,
    body: object,
    token?: string,
    functions?: Array<(data: any) => void>
  ): Promise<any> {
    const UNIQUE_ID = await this.getUniqueID();
    try {
      const response = await this.fetchWithTimeout(`${this.BASE_URL}${path}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          "x-device-id": UNIQUE_ID || "",
          "x-timezone": this.getDeviceTimezone(),
          Authorization: token ? `Bearer ${token}` : "",
        },
        body: JSON.stringify(body),
      });

      const data = await response.json();

      // Call the callback functions with the data
      if (functions && functions.length > 0) {
        functions.forEach((func) => func(data));
      }

      // Check if the response was successful (status code 200-299)
      if (response.ok) {
        return data;
      } else {
        // For non-2xx responses, check for 401 status
        if (response.status === 401) {
          // Handle 401 Unauthorized error
          await handleAuthError({ status: 401, message: data.message || "Unauthorized" });
        }
        // Throw the error data
        throw data;
      }
    } catch (error) {
      // Determine the type of error
      const errorType = getErrorType(error);

      // Create a standardized error object
      const enhancedError = {
        type: errorType,
        message: error.message || "An error occurred",
        status:
          error.status ||
          (errorType === "network"
            ? "network_error"
            : errorType === "timeout"
            ? "timeout_error"
            : errorType === "server"
            ? "server_error"
            : "unknown_error"),
        originalError: error,
      };

      // Check for 401 status in the error
      if (error.status === 401 || (error.originalError && error.originalError.status === 401)) {
        await handleAuthError(enhancedError);
      }

      // Call the callback functions with the enhanced error
      if (functions && functions.length > 0) {
        functions.forEach((func) => func(enhancedError));
      }

      throw enhancedError;
    }
  }

  public async put(
    path: string,
    body: object,
    token?: string,
    functions?: Array<(data: any) => void>
  ): Promise<any> {
    const UNIQUE_ID = await this.getUniqueID();
    try {
      const response = await this.fetchWithTimeout(`${this.BASE_URL}${path}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          "x-device-id": UNIQUE_ID || "",
          "x-timezone": this.getDeviceTimezone(),
          Authorization: token ? `Bearer ${token}` : "",
        },
        body: JSON.stringify(body),
      });

      const data = await response.json();

      // Call the callback functions with the data
      if (functions && functions.length > 0) {
        functions.forEach((func) => func(data));
      }

      // Check if the response was successful (status code 200-299)
      if (response.ok) {
        return data;
      } else {
        // For non-2xx responses, check for 401 status
        if (response.status === 401) {
          // Handle 401 Unauthorized error
          await handleAuthError({ status: 401, message: data.message || "Unauthorized" });
        }
        // Throw the error data
        throw data;
      }
    } catch (error) {
      // Determine the type of error
      const errorType = getErrorType(error);

      // Create a standardized error object
      const enhancedError = {
        type: errorType,
        message: error.message || "An error occurred",
        status: error.status || (errorType === 'network' ? "network_error" :
                               errorType === 'timeout' ? "timeout_error" :
                               errorType === 'server' ? "server_error" : "unknown_error"),
        originalError: error,
      };

      // Check for 401 status in the error
      if (error.status === 401 || (error.originalError && error.originalError.status === 401)) {
        await handleAuthError(enhancedError);
      }

      // Call the callback functions with the enhanced error
      if (functions && functions.length > 0) {
        functions.forEach((func) => func(enhancedError));
      }

      throw enhancedError;
    }
  }

  public async patch(
    path: string,
    body: object,
    token?: string,
    functions?: Array<(data: any) => void>
  ): Promise<any> {
    const UNIQUE_ID = await this.getUniqueID();
    try {
      const response = await this.fetchWithTimeout(`${this.BASE_URL}${path}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          "x-device-id": UNIQUE_ID || "",
          "x-timezone": this.getDeviceTimezone(),
          Authorization: token ? `Bearer ${token}` : "",
        },
        body: JSON.stringify(body),
      });

      const data = await response.json();

      // Call the callback functions with the data
      if (functions && functions.length > 0) {
        functions.forEach((func) => func(data));
      }

      // Check if the response was successful (status code 200-299)
      if (response.ok) {
        return data;
      } else {
        // For non-2xx responses, check for 401 status
        if (response.status === 401) {
          // Handle 401 Unauthorized error
          await handleAuthError({ status: 401, message: data.message || "Unauthorized" });
        }
        // Throw the error data
        throw data;
      }
    } catch (error) {
      // Determine the type of error
      const errorType = getErrorType(error);

      // Create a standardized error object
      const enhancedError = {
        type: errorType,
        message: error.message || "An error occurred",
        status: error.status || (errorType === 'network' ? "network_error" :
                               errorType === 'timeout' ? "timeout_error" :
                               errorType === 'server' ? "server_error" : "unknown_error"),
        originalError: error,
      };

      // Check for 401 status in the error
      if (error.status === 401 || (error.originalError && error.originalError.status === 401)) {
        await handleAuthError(enhancedError);
      }

      // Call the callback functions with the enhanced error
      if (functions && functions.length > 0) {
        functions.forEach((func) => func(enhancedError));
      }

      throw enhancedError;
    }
  }

  public async delete(
    path: string,
    body: object,
    token?: string,
    functions?: Array<(data: any) => void>
  ): Promise<any> {
    const UNIQUE_ID = await this.getUniqueID();
    try {
      const response = await this.fetchWithTimeout(`${this.BASE_URL}${path}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          "x-device-id": UNIQUE_ID || "",
          "x-timezone": this.getDeviceTimezone(),
          Authorization: token ? `Bearer ${token}` : "",
        },
        body: JSON.stringify(body),
      });

      const data = await response.json();

      // Call the callback functions with the data
      if (functions && functions.length > 0) {
        functions.forEach((func) => func(data));
      }

      // Check if the response was successful (status code 200-299)
      if (response.ok) {
        return data;
      } else {
        // For non-2xx responses, check for 401 status
        if (response.status === 401) {
          // Handle 401 Unauthorized error
          await handleAuthError({ status: 401, message: data.message || "Unauthorized" });
        }
        // Throw the error data
        throw data;
      }
    } catch (error) {
      // Determine the type of error
      const errorType = getErrorType(error);

      // Create a standardized error object
      const enhancedError = {
        type: errorType,
        message: error.message || "An error occurred",
        status: error.status || (errorType === 'network' ? "network_error" :
                               errorType === 'timeout' ? "timeout_error" :
                               errorType === 'server' ? "server_error" : "unknown_error"),
        originalError: error,
      };

      // Check for 401 status in the error
      if (error.status === 401 || (error.originalError && error.originalError.status === 401)) {
        await handleAuthError(enhancedError);
      }

      // Call the callback functions with the enhanced error
      if (functions && functions.length > 0) {
        functions.forEach((func) => func(enhancedError));
      }

      throw enhancedError;
    }
  }
}
