import React, { useState } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  ScrollView,
  StatusBar,
} from "react-native";
import { colors } from "../../../Config/colors";
import { fonts } from "../../../Config/Fonts";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import Button from "../../../Components/Button";
import AppHeader from "../../../Components/AppHeader";
import { useToast } from "../../../Context/ToastContext";
import TabSelector from "../../../Components/profile/ProfileTabSelector";
import { tabs } from "./tabs";
import Div from "../../../Components/Div";
import SliderVisualization from "../../../Components/SliderVisualization";
import { UpdateMedicalHistory } from "../../../RequestHandler.tsx/User";
import Loader from "../../../Components/Loader";
import NetworkErrorView from "../../../Components/NetworkErrorView";
import { getErrorType, getErrorMessage } from "../../../utils/networkErrorHandler";

const { height } = Dimensions.get("window");

// Weight unit options
const weightUnitOptions = [
  { id: 1, label: "Kg", value: "kg" },
  { id: 2, label: "lb", value: "lb" },
  { id: 3, label: "g", value: "g" },
];

export default function WeightScreen({ navigation, route }) {
  // Get current weight from route params if available
  const { currentValue, onSave } = route.params || {
    currentValue: "",
    onSave: null,
  };

  // Define min and max values for each unit
  const minMaxValues = {
    kg: { min: 30, max: 200 },
    lb: { min: 66, max: 440 }, // 30*2.20462, 200*2.20462
    g: { min: 30000, max: 200000 }, // 30*1000, 200*1000
  };

  const [selectedUnit, setSelectedUnit] = useState("kg");
  const [weightValue, setWeightValue] = useState(
    currentValue ? parseFloat(currentValue) : 70
  ); // Default weight in kg
  const [loading, setLoading] = useState(false);
  const [networkError, setNetworkError] = useState<any>(null);
  const [showNetworkError, setShowNetworkError] = useState(false);
  const { handleToast } = useToast();

  // Function to handle network errors
  const handleNetworkError = (error: any) => {
    setNetworkError(error);
    setShowNetworkError(true);
  };

  // Function to retry after network error
  const handleRetry = () => {
    setShowNetworkError(false);
    setNetworkError(null);
    handleSaveChanges();
  };

  // Handle unit selection
  const handleUnitSelect = (value: string) => {
    // Store current weight value before conversion
    const currentWeight = weightValue;

    // Convert weight value based on selected unit
    let newWeight: number;
    if (value === "kg" && selectedUnit === "lb") {
      // Convert from lb to kg
      newWeight = currentWeight * 0.453592;
    } else if (value === "lb" && selectedUnit === "kg") {
      // Convert from kg to lb
      newWeight = currentWeight * 2.20462;
    } else if (value === "g" && selectedUnit === "kg") {
      // Convert from kg to g
      newWeight = currentWeight * 1000;
    } else if (value === "kg" && selectedUnit === "g") {
      // Convert from g to kg
      newWeight = currentWeight / 1000;
    } else if (value === "lb" && selectedUnit === "g") {
      // Convert from g to lb
      newWeight = currentWeight * 0.00220462;
    } else if (value === "g" && selectedUnit === "lb") {
      // Convert from lb to g
      newWeight = currentWeight * 453.592;
    } else {
      newWeight = currentWeight;
    }

    // Update selected unit
    setSelectedUnit(value);

    // Update weight value
    setWeightValue(newWeight);
  };

  // Format weight value for display
  const getFormattedWeight = () => {
    if (selectedUnit === "g" && weightValue >= 1000) {
      // Format as kg if g is too large
      return `${(weightValue / 1000).toFixed(1)} kg`;
    } else {
      // Format as regular number
      return `${
        selectedUnit === "g" ? Math.round(weightValue) : weightValue.toFixed(1)
      } ${selectedUnit}`;
    }
  };

  // Handle save changes
  const handleSaveChanges = async () => {
    if (!weightValue) {
      handleToast("Please select a weight", "error");
      return;
    }

    setLoading(true);

    try {
      // Format the weight value with the unit for display
      const formattedWeight = getFormattedWeight();
      // Prepare the request body with the numeric weight value and unit
      const requestBody = {
        weight: Math.round(weightValue),
        weightUnit: selectedUnit
      };
      console.log("Updating weight:", requestBody);
      // Call the API to update medical history
      const response = await UpdateMedicalHistory(requestBody);
      console.log("Update response:", response);
      // Call the onSave callback if provided
      if (onSave) {
        onSave(formattedWeight);
      }
      handleToast("Weight updated successfully", "success");
      // Navigate back to profile screen
      navigation.pop();
    } catch (error) {
      console.error("Error updating weight:", error);
      // Check if it's a network error
      if (getErrorType(error) === 'network' || getErrorType(error) === 'timeout') {
        handleNetworkError(error);
      } else {
        handleToast("Failed to update weight. Please try again.", "error");
      }
    } finally {
      setLoading(false);
    }
  };

  // If there's a network error, show the NetworkErrorView
  if (showNetworkError) {
    return (
      <NetworkErrorView
        errorType={getErrorType(networkError)}
        message={getErrorMessage(networkError)}
        onRetry={handleRetry}
      />
    );
  }

  return (
    <View style={styles.container}>
      <Div>
        {/* Header */}
        <View style={styles.headerContainer}>
          <AppHeader
            navigation={navigation}
            showBackButton={true}
            title={"Henry Osuji"}
            navStyle={{
              justifyContent: "flex-start",
              alignItems: "flex-start",
            }}
            subtitle={`20% profile completed`}
            subtitleStyle={styles.completionText}
          />
        </View>

        {/* Tabs */}
        {/* <TabSelector
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={handleTabChange}
        /> */}
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
        >
          <View style={styles.contentContainer}>
            {/* Title */}
            <H4 style={styles.title}>Add Weight</H4>

            {/* Unit Options */}
            <View style={styles.unitOptionsContainer}>
              {weightUnitOptions.map((option) => (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.unitButton,
                    selectedUnit === option.value && styles.selectedUnit,
                  ]}
                  onPress={() => handleUnitSelect(option.value)}
                >
                  <P
                    style={[
                      styles.unitText,
                      selectedUnit === option.value && styles.selectedUnitText,
                    ]}
                  >
                    {option.label}
                  </P>
                </TouchableOpacity>
              ))}
            </View>

            {/* Weight Slider Visualization */}
            <View
              style={{
                width: "100%",
                alignItems: "center",
                marginTop: 10,
                marginBottom: 30,
              }}
            >
              <SliderVisualization
                value={weightValue}
                minValue={minMaxValues[selectedUnit].min}
                maxValue={minMaxValues[selectedUnit].max}
                unit={selectedUnit}
                onValueChange={(newValue) => {
                  setWeightValue(newValue);
                }}
              />
            </View>
          </View>
        </ScrollView>

        {/* Save Button */}
        <View style={styles.buttonContainer}>
          <Button
            btnText="Save changes"
            onPress={handleSaveChanges}
            loading={loading}
            style={styles.saveButton}
          />
        </View>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  headerContainer: {
    width: "100%",
    paddingHorizontal: 20,
  },
  completionText: {
    color: "#D4AF37", // Gold color for completion percentage
    fontSize: 12,
    fontFamily: fonts.dmSansRegular,
    marginTop: 4,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingTop: (10 * height) / 100,
    paddingBottom: 100,
    alignItems: "center",
  },
  title: {
    fontSize: 18,
    fontFamily: fonts.dmSansBold,
    color: colors.black,
    marginBottom: (2.8 * height) / 100,
    textAlign: "center",
  },
  unitOptionsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    marginBottom: 40,
  },
  unitButton: {
    width: "30%",
    height: 56,
    borderRadius: 28,
    borderWidth: 1,
    borderColor: colors.b2Brown,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.white,
  },
  selectedUnit: {
    backgroundColor: colors.b2Brown,
  },
  unitText: {
    fontSize: 12,
    fontFamily: fonts.dmSansMedium,
    color: colors.b2Brown,
  },
  selectedUnitText: {
    color: colors.white,
  },
  valueDisplayContainer: {
    alignItems: "center",
    marginBottom: 30,
  },
  valueText: {
    fontSize: 18,
    fontFamily: fonts.dmSansBold,
    color: colors.black,
  },
  sliderContainer: {
    width: "100%",
    marginTop: 20,
  },
  buttonContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
    paddingBottom: 28,
    backgroundColor: colors.white,
  },
  saveButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
  },
});
