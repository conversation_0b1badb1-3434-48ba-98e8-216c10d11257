import React, { useEffect, useState } from "react";
import {
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
  ScrollView,
  TextInput,
  ImageBackground,
  RefreshControl,
} from "react-native";
import { colors } from "../../Config/colors";
import Div from "../../Components/Div";
import H4 from "../../Components/H4";
import P from "../../Components/P";
import { fonts } from "../../Config/Fonts";
// Button is used in the NetworkErrorView component
import Icon from "react-native-vector-icons/Ionicons";
import { SvgXml } from "react-native-svg";
import { svg } from "../../Config/Svg";
import { GetUserProfile } from "../../RequestHandler.tsx/User";
import Loader from "../../Components/Loader";
import NetworkErrorView from "../../Components/NetworkErrorView";
import { getErrorType, getErrorMessage } from "../../utils/networkErrorHandler";
import { useToast } from "../../Context/ToastContext";

export default function HomeScreen({ navigation }) {
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [networkError, setNetworkError] = useState<any>(null);
  const [showNetworkError, setShowNetworkError] = useState(false);
  const [userProfile, setUserProfile] = useState<any>(null);
  const { handleToast } = useToast();

  const getUserProfile = async () => {
    setLoading(true);
    setNetworkError(null);
    setShowNetworkError(false);
    try {
      const res = await GetUserProfile();
      setUserProfile(res);
      if (res.accountSetup === false) {
        navigation.navigate("UploadPatienceDetailsScreen1", {
          avatar: res.avatar,
        });
      }
      console.log(res);
    } catch (error) {
      console.log("Error fetching user profile:", error);
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        setNetworkError(error);
        setShowNetworkError(true);
      } else {
        handleToast("Failed to load profile. Please try again.", "error");
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Handle pull-to-refresh
  const onRefresh = () => {
    setRefreshing(true);
    getUserProfile();
  };

  // Handle retry when network error occurs
  const handleRetry = () => {
    setShowNetworkError(false);
    getUserProfile();
  };

  useEffect(() => {
    setLoading(true);
    getUserProfile();
  }, []);

  // If there's a network error, show the NetworkErrorView
  if (showNetworkError) {
    return (
      <NetworkErrorView
        errorType={getErrorType(networkError)}
        message={getErrorMessage(networkError)}
        onRetry={handleRetry}
      />
    );
  }

  return (
    <View style={styles.mainContainer}>
      <Div>
        <ScrollView
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.scrollContainer}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
        >
          {/* Header Section */}
          <View style={styles.headerContainer}>
            <View style={styles.headerContent}>
              <View>
                <View style={styles.locationRow}>
                  <Icon
                    name="location-outline"
                    size={18}
                    color={colors.primary}
                  />
                  <P style={styles.locationText}>Turn on Location</P>
                </View>
                <H4 style={styles.userName}>
                  {userProfile?.firstName || "Welcome"}{" "}
                  {userProfile?.lastName || ""}
                </H4>
                <View style={styles.taglineRow}>
                  <P style={styles.taglineText}>An apple a day... </P>
                  <Text style={styles.appleEmoji}>🍎</Text>
                </View>
              </View>
              <View style={styles.headerIcons}>
                <TouchableOpacity style={styles.iconButton}>
                  <SvgXml xml={svg.mail} />
                </TouchableOpacity>
                <TouchableOpacity style={styles.iconButton}>
                  <SvgXml xml={svg.bell} />
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* Search Section */}
          <View style={styles.searchContainer}>
            <ImageBackground
              style={{ position: "absolute", padding: 16, width: "100%" }}
              source={require("../../assets/arrBg.png")}
              resizeMode="cover"
            >
              <P style={styles.searchText}>
                Looking for a Doctor or other professional health providers
              </P>
              <View style={styles.searchInputContainer}>
                <SvgXml xml={svg.filter} />
                <TextInput
                  style={styles.searchInput}
                  placeholder="Filter"
                  placeholderTextColor="#333"
                />
              </View>
            </ImageBackground>
          </View>

          {/* Medical Checks Section, i will come back to this section*/}
          <ImageBackground
            source={require("../../assets/lg.png")}
            resizeMode="cover"
            style={styles.medicalCheckContainer}
          >
            <View style={styles.medicalCheckContent}>
              <View style={styles.medicalCheckTextContainer}>
                <H4 style={styles.medicalCheckTitle}>
                  Periodic Medical Checks
                </H4>
                <P style={styles.medicalCheckDescription}>
                  Our medical check-up section connects you to trusted clinics
                  and practitioners for routine exams and preventive care.
                </P>
                <TouchableOpacity style={styles.appointmentButton}>
                  <Text style={styles.appointmentButtonText}>
                    Schedule an appointment
                  </Text>
                </TouchableOpacity>
              </View>
              <View style={styles.medicalCheckImageContainer}>
                {/* <View style={styles.imagePlaceholder} /> */}
                <Image
                  source={require("../../assets/doc.png")}
                  style={{
                    width: 132,
                    height: 132,
                    // position: "absolute",
                    // bottom: 0,
                  }}
                />
              </View>
            </View>
          </ImageBackground>

          {/* Services Section */}
          <View style={styles.servicesContainer}>
            <TouchableOpacity style={styles.serviceItem}>
              <View style={styles.serviceIconContainer}>
                <SvgXml xml={svg.scope} />
                {/* <Icon name="medical-outline" size={28} color={colors.primary} /> */}
              </View>
              <P style={styles.serviceText}>Consult doctor</P>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.serviceItem}
              onPress={() => navigation.navigate("MedicineReminderScreen")}
            >
              <View style={styles.serviceIconContainer}>
                <SvgXml xml={svg.b_bell} />
              </View>
              <P style={styles.serviceText}>Medicine Reminder</P>
            </TouchableOpacity>

            <TouchableOpacity style={styles.serviceItem}>
              <View style={styles.serviceIconContainer}>
                <SvgXml xml={svg.lab} />
              </View>
              <P style={styles.serviceText}>Lab Test</P>
            </TouchableOpacity>

            <TouchableOpacity style={styles.serviceItem}>
              <View style={styles.serviceIconContainer}>
                <SvgXml xml={svg.menu} />
              </View>
              <P style={styles.serviceText}>More</P>
            </TouchableOpacity>
          </View>

          {/* Bookings Section */}
          <View style={styles.bookingsContainer}>
            <H4 style={styles.bookingsTitle}>Bookings</H4>
            <View style={styles.bookingsContent}>
              <View style={styles.calendarImageContainer}>
                <Image
                  source={require("../../assets/calender.png")}
                  style={{ width: 250, height: 250 }}
                />
              </View>
              <P style={styles.noAppointmentsText}>No Active appointments</P>
            </View>
          </View>
        </ScrollView>
      </Div>
      {loading && <Loader />}
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: 100,
  },
  headerContainer: {
    width: "100%",
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  headerContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    width: "100%",
  },
  locationRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 5,
  },
  locationText: {
    color: colors.primary,
    marginLeft: 5,
    fontSize: 14,
    fontFamily: fonts.dmSansSemibold,
  },
  userName: {
    fontSize: 18,
    fontFamily: fonts.dmSansBold,
    marginBottom: 4,
  },
  taglineRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  taglineText: {
    fontSize: 14,
    color: colors.black,
    fontFamily: fonts.dmSansRegular,
  },
  appleEmoji: {
    fontSize: 14,
  },
  headerIcons: {
    flexDirection: "row",
    alignItems: "center",
  },
  iconButton: {
    marginLeft: 15,
    width: 40,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
  },
  searchContainer: {
    backgroundColor: colors.primary,
    borderRadius: 15,
    width: "90%",
    alignSelf: "center",
    minHeight: 119,
    marginTop: 24,
    position: "relative",
    overflow: "hidden",
  },
  searchText: {
    color: colors.white,
    marginBottom: 15,
    fontSize: 14,
    fontFamily: fonts.dmSansMedium,
  },
  searchInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingHorizontal: 10,
    paddingVertical: 10,
  },
  searchInput: {
    flex: 1,
    marginLeft: 10,
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
  },
  medicalCheckContainer: {
    backgroundColor: "#CCEEE4",
    borderRadius: 15,
    marginTop: 20,
    marginHorizontal: 20,
    overflow: "hidden",
  },
  medicalCheckContent: {
    flexDirection: "row",
    padding: 15,
    position: "relative",
  },
  medicalCheckTextContainer: {
    flex: 2,
    paddingRight: 10,
  },
  medicalCheckTitle: {
    fontSize: 18,
    fontFamily: fonts.dmSansBold,
    color: "#0E6655",
    marginBottom: 8,
  },
  medicalCheckDescription: {
    fontSize: 14,
    color: colors.deepGreen,
    marginBottom: 15,
    width: "70%",
  },
  appointmentButton: {
    backgroundColor: colors.lightGreen1,
    borderRadius: 20,
    paddingVertical: 10,
    paddingHorizontal: 15,
    alignSelf: "flex-start",
  },
  appointmentButtonText: {
    color: colors.white,
    fontFamily: fonts.dmSansBold,
    fontSize: 14,
  },
  medicalCheckImageContainer: {
    flex: 1,
    alignItems: "flex-end",
    justifyContent: "center",
    position: "absolute",
    bottom: 0,
    right: 0,
  },
  imagePlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "#0E6655",
  },
  servicesContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 25,
    marginHorizontal: 20,
  },
  serviceItem: {
    alignItems: "center",
    width: "22%",
  },
  serviceIconContainer: {
    // width: 50,
    // height: 50,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  serviceText: {
    fontSize: 14,
    textAlign: "center",
    fontFamily: fonts.dmSansSemibold,
  },
  bookingsContainer: {
    marginTop: 25,
    marginHorizontal: 20,
  },
  bookingsTitle: {
    fontSize: 20,
    fontFamily: fonts.dmSansBold,
    marginBottom: 15,
  },
  bookingsContent: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 20,
  },
  calendarImageContainer: {
    marginBottom: 15,
  },
  calendarImagePlaceholder: {
    width: 200,
    height: 150,
    backgroundColor: "#E6F0FD",
    borderRadius: 10,
  },
  noAppointmentsText: {
    fontSize: 14,
    color: colors.black,
    marginTop: 10,
  },
});
