export const maskEmail = (email) => {
  const [name, domain] = email?.split("@");
  const maskedName = name?.slice(0, 4) + "*".repeat(name?.length - 4);
  return maskedName + "@" + domain;
};
export function maskPhoneNumber(phoneNumber: string): string {
  const countryCode = phoneNumber.substring(0, 4);
  const phoneDigits = phoneNumber.substring(4);
  const firstPart = phoneDigits.substring(0, 2);
  const lastPart = phoneDigits.substring(phoneDigits.length - 3);
  const maskedPart = "*".repeat(phoneDigits.length - 5);
  return `${countryCode} ${firstPart}${maskedPart}${lastPart}`;
}
