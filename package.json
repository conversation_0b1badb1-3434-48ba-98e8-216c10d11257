{"name": "onlymed", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-google-signin/google-signin": "^13.2.0", "@react-navigation/bottom-tabs": "^7.2.1", "@react-navigation/native": "^7.0.15", "@react-navigation/stack": "^7.1.2", "expo": "~52.0.38", "expo-apple-authentication": "~7.1.3", "expo-dev-client": "~5.0.14", "expo-font": "~13.0.4", "expo-router": "^4.0.19", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.1", "expo-updates": "~0.27.4", "firebase": "^10.13.2", "formik": "^2.4.6", "react": "18.3.1", "react-native": "0.76.7", "react-native-country-codes-picker": "^2.3.5", "react-native-device-info": "^14.0.4", "react-native-gesture-handler": "~2.20.2", "react-native-otp-entry": "^1.8.3", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-vector-icons": "^10.2.0", "yup": "^1.6.1", "expo-document-picker": "~13.0.3", "expo-image-picker": "~16.0.6"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.3.12", "typescript": "^5.3.3"}, "private": true}