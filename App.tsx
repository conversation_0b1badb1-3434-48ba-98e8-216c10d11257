import { StatusBar } from "expo-status-bar";
import { Dimensions, StyleSheet, View } from "react-native";
import { useFonts } from "expo-font";
import { useEffect, useState } from "react";
import MainStack from "./app/Navigation/MainStack";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { CredentailsContext } from "./app/Context/CredentailsContext";
import CustomSplashScreen from "./app/Screens/CustomSplashScreen";
import AsyncStorage from "@react-native-async-storage/async-storage";
import DeviceInfo from "react-native-device-info";
import { ToastProvider } from "./app/Context/ToastContext";
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import { setAuthHandler } from "./app/utils/authErrorHandler";

const { width, height } = Dimensions.get("window");

export default function App() {
  const [appReady, setAppReady] = useState(false);
  const [storedCredentails, setStoredCredentails] = useState("");

  const [fontsLoaded] = useFonts({
    "dmSans-bold": require("./app/assets/fonts/DMSans-Bold.ttf"),
    "dmSans-semibold": require("./app/assets/fonts/DMSans-SemiBold.ttf"),
    "dmSans-medium": require("./app/assets/fonts/DMSans-Medium.ttf"),
    "dmSans-regular": require("./app/assets/fonts/DMSans-Regular.ttf"),
  });
  GoogleSignin.configure({
    webClientId:
      "211734170318-0odqjbt2s3qec0rhj8l04q1msmq7fj82.apps.googleusercontent.com",
    iosClientId:
      "211734170318-fj0eagc400tgcjcq3ucilvt9l02d5156.apps.googleusercontent.com",
    profileImageSize: 120,
  });

  const fetchUniqueId = async () => {
    const id = await DeviceInfo.getUniqueId();
    AsyncStorage.setItem("uniqueID", id);
  };
  const checkLoginCredentails = async () => {
    try {
      const res = await AsyncStorage.getItem("cookies##$$");
      if (res !== null && res !== undefined && typeof res === "string") {
        const data = JSON.parse(res);
        setStoredCredentails(data);
      } else {
        setStoredCredentails(null);
      }
      setAppReady(true);
    } catch (err) {
      console.log("Error fetching credentials:", err);
    }
  };
  const clearLogin = async () => {
    await GoogleSignin.signOut();
    AsyncStorage.removeItem("cookies##$$")
      .then(() => {
        // @ts-ignore
        setStoredCredentails(null);
      })
      .catch((error) => {});
  };

  useEffect(() => {
    // clearLogin();
    fetchUniqueId();

    // Set the global auth handler
    setAuthHandler(setStoredCredentails);
  }, []);

  useEffect(() => {
    if (fontsLoaded) {
      setTimeout(checkLoginCredentails, 4000);
    }
  }, [fontsLoaded]);

  if (!appReady) {
    return <CustomSplashScreen />;
  }

  return (
    <>
      <StatusBar style="dark" />
      <CredentailsContext.Provider
        // @ts-ignore
        value={{ storedCredentails, setStoredCredentails }}
      >
        <ToastProvider>
          <GestureHandlerRootView style={{ flex: 1 }}>
            <MainStack />
          </GestureHandlerRootView>
        </ToastProvider>
      </CredentailsContext.Provider>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width,
    height,
    backgroundColor: "#fff",
    alignItems: "center",
    justifyContent: "center",
  },
});
