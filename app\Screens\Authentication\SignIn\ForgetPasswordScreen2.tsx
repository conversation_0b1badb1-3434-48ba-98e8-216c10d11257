import React, { useState } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  Keyboard,
  ScrollView,
  Platform,
} from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../Config/Svg";
import AppHeader from "../../../Components/AppHeader";
import Input from "../../../Components/Input";
import * as yup from "yup";
import { Formik } from "formik";
import { CountryPicker } from "react-native-country-codes-picker";

const { width, height } = Dimensions.get("window");

export default function ForgottenPasswordScreen2({ navigation, route }) {
  const [loading, setLoading] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState("email");
  const [defaultCountryCode, setDefaultCountryCode] = useState("+234");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [show, setShow] = useState(false);
  const { method } = route?.params || "";
  const InputScheme = (activeTab: string) => {
    //@ts-ignore
    return yup.object().shape({
      email:
        activeTab === "email"
          ? yup
              .string()
              .email("Invalid email address")
              .required("Email is required")
          : yup.string().notRequired(),
      phonenumber:
        activeTab === "phone"
          ? yup
              .string()
              .matches(/^[0-9]+$/, "Phone number should not include letters")
              .min(11, "Invalid mobile number")
              .required("Phone number is required")
          : yup.string().notRequired(),
    });
  };

  return (
    <View style={styles.mainContainer}>
      <Div>
        <Formik
          initialValues={
            method === "email" ? { email: "" } : { phonenumber: "" }
          }
          enableReinitialize={true}
          validationSchema={InputScheme(method)}
          onSubmit={async (values, actions) => {
            Keyboard.dismiss();
            setLoading(true);
            setTimeout(() => {
              let formattedNum = "";
              if (values.phonenumber) {
                let formattedNumber = values.phonenumber.replace(/\D/g, ""); // Remove non-numeric characters
                if (formattedNumber.startsWith("0")) {
                  formattedNumber = formattedNumber.substring(1); // Remove leading zero
                }
                formattedNumber = `${defaultCountryCode}${formattedNumber}`; // Append country code
                formattedNum = formattedNumber;
              }
              navigation.navigate("ForgottenPassWordVerify", {
                value: values.email ? values.email : formattedNum,
              });
              setLoading(false);
            }, 2000);
            // setLoading(true);
          }}
        >
          {(formikProps) => (
            <View style={styles.container}>
              <ScrollView
                style={{ width: "100%" }}
                automaticallyAdjustContentInsets={true}
                contentContainerStyle={{
                  alignItems: "center",
                  minHeight: "100%",
                }}
                showsVerticalScrollIndicator={false}
              >
                {/* Back Button */}
                <View style={{ width: "100%" }}>
                  <AppHeader navigation={navigation} />
                </View>

                {/* Title Section */}
                <View style={styles.titleContainer}>
                  <H4 style={styles.title}>Forgotten Password</H4>
                  <P style={styles.subtitle}>
                    {method === "email"
                      ? "Verify with your email address"
                      : "Verify with your phone number"}
                  </P>
                </View>

                <View style={styles.formContainer}>
                  {method === "phone" ? (
                    <Input
                      defaultCountryCode={defaultCountryCode}
                      label={"Phone"}
                      value={formikProps.values.phonenumber}
                      onChangeText={formikProps.handleChange("phonenumber")}
                      onBlur={formikProps.handleBlur("phonenumber")}
                      type="phone"
                      onDefualtCodePress={() => {
                        setShow(true);
                      }}
                      placeholder="0 00 00 00 00"
                      autoCapitalize="none"
                      error={
                        formikProps.errors.phonenumber &&
                        formikProps.touched.phonenumber
                      }
                      errorText={formikProps.errors.phonenumber}
                    />
                  ) : (
                    <Input
                      label={"Email"}
                      placeholder="<EMAIL>"
                      value={formikProps.values.email}
                      onChangeText={formikProps.handleChange("email")}
                      onBlur={formikProps.handleBlur("email")}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      error={
                        formikProps.errors.email && formikProps.touched.email
                      }
                      errorText={formikProps.errors.email}
                    />
                  )}
                </View>

                {/* Continue Button */}
                <View style={styles.buttonContainer}>
                  <Button
                    btnText="Continue"
                    onPress={() => {
                      formikProps.handleSubmit();
                    }}
                    loading={loading}
                    style={styles.continueButton}
                  />
                </View>
              </ScrollView>
            </View>
          )}
        </Formik>
        <CountryPicker
          show={show}
          lang="en"
          // when picker button press you will get the country object with dial code
          onBackdropPress={() => {
            setShow(false);
          }}
          pickerButtonOnPress={(item) => {
            // setCountryCode(item.dial_code);
            setDefaultCountryCode(item.dial_code);
            setShow(false);
          }}
          style={{
            textInput: {
              fontFamily: fonts.dmSansRegular,
              paddingLeft: 16,
            },
            modal: {
              height: Platform.OS === "ios"? 80 * height/100: (50 * height) / 100,
            },
            dialCode: {
              fontFamily: fonts.dmSansRegular,
            },
            countryName: {
              fontFamily: fonts.dmSansRegular,
            },
          }}
        />
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "90%",
    height: "100%",
    backgroundColor: colors.white,
    alignItems: "center",
  },
  backButton: {
    alignSelf: "flex-start",
    marginTop: (3 * height) / 100,
    padding: 5,
  },
  titleContainer: {
    width: "100%",
    alignItems: "flex-start",
    marginTop: (2 * height) / 100,
    marginBottom: (5 * height) / 100,
  },
  title: {
    fontFamily: fonts.dmSansBold,
    fontSize: 16,
    marginBottom: 8,
  },
  subtitle: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    color: colors.gray1,
  },
  optionsContainer: {
    width: "100%",
    marginBottom: (4 * height) / 100,
  },
  optionCard: {
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    padding: 16,
    backgroundColor: colors.white,
    borderRadius: 16,
    marginBottom: 16,
    // borderWidth: 1,
    // borderColor: colors.stroke,
  },
  selectedCard: {
    borderColor: colors.primary || "#1A73E8",
    borderWidth: 1,
    backgroundColor: colors.primaryLight,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: "center",
    borderWidth: 1,
    borderColor: colors.stroke,
    alignItems: "center",
    marginRight: 12,
  },
  selectedIconContainer: {
    backgroundColor: colors.primaryLight_100 || "#D5E5FF",
    borderWidth: 0,
    borderColor: "transparent",
  },
  optionTextContainer: {
    flex: 1,
  },
  optionTitle: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    color: colors.gray1 || "#757575",
    marginBottom: 4,
  },
  optionValue: {
    fontFamily: fonts.dmSansMedium,
    fontSize: 14,
    color: colors.black,
  },
  buttonContainer: {
    width: "100%",
    position: "absolute",
    bottom: (5 * height) / 100,
  },
  continueButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
  },
  formContainer: {
    width: "100%",
  },
  inputLabel: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    marginBottom: 8,
  },
});
