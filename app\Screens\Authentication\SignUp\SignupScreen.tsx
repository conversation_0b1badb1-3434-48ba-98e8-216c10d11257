import React, { useEffect, useState } from "react";
import {
  Dimensions,
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
  TextInput,
  ScrollView,
  Keyboard,
  Platform,
} from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../Config/Svg";
import Input from "../../../Components/Input";
import * as yup from "yup";
import { Formik } from "formik";
import GoogleButton from "../../../Components/GoogleButton";
import { useToast } from "../../../Context/ToastContext";
import { CountryPicker } from "react-native-country-codes-picker";
import AppleButton from "../../../Components/AppleBtn";
const { width, height } = Dimensions.get("window");

export default function SignUpScreen({ navigation, route }) {
  const [activeTab, setActiveTab] = useState("Email");
  const [show, setShow] = useState(false);
  const [countryCode, setCountryCode] = useState("+234");
  const [defaultCountryCode, setDefaultCountryCode] = useState("+234");
  const [loading, setLoading] = useState(false);
  const { isPractional } = route?.params || false;
  const { handleToast } = useToast();
  const getRegisterSchema = (activeTab: string) => {
    //@ts-ignore
    return yup.object().shape({
      email:
        activeTab === "Email"
          ? yup
              .string()
              .email("Invalid email address")
              .required("Email is required")
          : yup.string().notRequired(),

      phonenumber:
        activeTab === "Phone"
          ? yup
              .string()
              .matches(/^[0-9]+$/, "Phone number should not include letters")
              .min(10, "Invalid mobile number")
              .required("Phone number is required")
          : yup.string().notRequired(),

      password: yup
        .string()
        .required("Password is required")
        .matches(
          /^(?=.*[0-9])(?=.*[*?!#$%&@^()\-_=+\\|[\]{};:/?.>])(?=.*[a-z])(?=.*[A-Z])[a-zA-Z0-9*?!#$%&@^()\-_=+\\|[\]{};:/?.>]{8,}$/,
          "Password must contain at least 8 characters, including letters, numbers, and special characters, with at least one uppercase letter"
        ),
    });
  };

  return (
    <View style={styles.mainContainer}>
      <Div>
        <Formik
          initialValues={
            activeTab === "Email"
              ? { email: "", password: "", isPractitional: isPractional }
              : { phonenumber: "", password: "", isPractitional: isPractional }
          }
          enableReinitialize={true} // Ensures values reset when activeTab changes
          validationSchema={getRegisterSchema(activeTab)}
          onSubmit={async (values, actions) => {
            Keyboard.dismiss();
            setLoading(true);
            setTimeout(() => {
              let formattedNum = "";
              // If the user is registering with a phone number, format it before submission
              if (values.phonenumber) {
                let formattedNumber = values.phonenumber.replace(/\D/g, ""); // Remove non-numeric characters
                if (formattedNumber.startsWith("0")) {
                  formattedNumber = formattedNumber.substring(1); // Remove leading zero
                }
                formattedNumber = `${defaultCountryCode}${formattedNumber}`; // Append country code
                formattedNum = formattedNumber;
              }
              const newValue = { ...values, phonenumber: formattedNum };
              navigation.navigate("VerifyEmailScreen", {
                data: activeTab === "Email" ? values : newValue,
              });
              setLoading(false);
            }, 2000);

            // setLoading(true);
          }}
        >
          {(formikProps) => (
            <View style={styles.container}>
              <ScrollView
                style={{ width: "100%" }}
                automaticallyAdjustKeyboardInsets={true}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{
                  alignItems: "center",
                  minHeight: "100%",
                }}
              >
                {/* Logo */}
                <View style={styles.logoContainer}>
                  {/* <Text style={styles.logoText}>LOGO</Text> */}
                  <SvgXml xml={svg.logo} />
                </View>
                {/* Create Account Title */}
                <H4 style={styles.title}>Create account</H4>
                {/* Email/Phone Tabs */}
                <View style={styles.tabContainer}>
                  <TouchableOpacity
                    style={[
                      styles.tab,
                      activeTab === "Email" && styles.activeTab,
                    ]}
                    onPress={() => setActiveTab("Email")}
                  >
                    <P
                      // @ts-ignore
                      style={[
                        styles.tabText,
                        activeTab === "Email" && styles.activeTabText,
                      ]}
                    >
                      Email
                    </P>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.tab,
                      activeTab === "Phone" && styles.activeTab,
                    ]}
                    onPress={() => setActiveTab("Phone")}
                  >
                    <P
                      // @ts-ignore
                      style={[
                        styles.tabText,
                        activeTab === "Phone" && styles.activeTabText,
                      ]}
                    >
                      Phone
                    </P>
                  </TouchableOpacity>
                </View>

                {/* Form Fields */}
                <View style={styles.formContainer}>
                  {activeTab === "Phone" ? (
                    <Input
                      defaultCountryCode={defaultCountryCode}
                      onDefualtCodePress={() => {
                        setShow(true);
                      }}
                      label={"Phone"}
                      value={formikProps.values.phonenumber}
                      onChangeText={formikProps.handleChange("phonenumber")}
                      onBlur={formikProps.handleBlur("phonenumber")}
                      type="phone"
                      placeholder="0 00 00 00 00"
                      autoCapitalize="none"
                      error={
                        formikProps.errors.phonenumber &&
                        formikProps.touched.phonenumber
                      }
                      errorText={formikProps.errors.phonenumber}
                    />
                  ) : (
                    <Input
                      label={"Email"}
                      placeholder="<EMAIL>"
                      value={formikProps.values.email}
                      onChangeText={formikProps.handleChange("email")}
                      onBlur={formikProps.handleBlur("email")}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      error={
                        formikProps.errors.email && formikProps.touched.email
                      }
                      errorText={formikProps.errors.email}
                    />
                  )}
                  <Input
                    contStyle={{ marginTop: 16 }}
                    label={"Password"}
                    value={formikProps.values.password}
                    onChangeText={formikProps.handleChange("password")}
                    onBlur={formikProps.handleBlur("password")}
                    type="password"
                    autoCapitalize="none"
                    error={
                      formikProps.errors.password &&
                      formikProps.touched.password
                    }
                    errorText={formikProps.errors.password}
                  />
                </View>

                {/* Sign Up Button */}
                <View style={{ width: "100%" }}>
                  <Button
                    btnText="Sign up"
                    onPress={() => {
                      formikProps.handleSubmit();
                    }}
                    loading={loading}
                    style={styles.signUpButton}
                  />
                </View>

                {/* Already have account */}
                <View style={styles.loginContainer}>
                  <Text style={styles.loginText}>
                    Already have an account?{" "}
                  </Text>
                  <TouchableOpacity
                    onPress={() =>
                      navigation.navigate("LoginScreen", {
                        isPractional: isPractional,
                      })
                    }
                  >
                    <Text style={styles.loginLink}>Login</Text>
                  </TouchableOpacity>
                </View>
                {/* Divider */}
                <View style={styles.dividerContainer}>
                  <View style={styles.divider} />
                  <P style={styles.dividerText}>Or Register with</P>
                  <View style={styles.divider} />
                </View>
                {/* Social Auth Buttons */}
                <View style={styles.socialButtonsContainer}>
                  <GoogleButton isPractitinal={isPractional} navigation={navigation} />
                  <AppleButton isPractitinal={isPractional} navigation={navigation}/>
                </View>

                {/* Terms and Privacy */}
                <View style={styles.termsContainer}>
                  <Text style={styles.termsText}>
                    By signing up, I accept to the{" "}
                    <Text style={styles.termsLink}>Terms</Text> and{" "}
                    <Text style={styles.termsLink}>privacy policies</Text>
                  </Text>
                </View>
              </ScrollView>
            </View>
          )}
        </Formik>
        <CountryPicker
          show={show}
          lang="en"
          // when picker button press you will get the country object with dial code
          onBackdropPress={() => {
            setShow(false);
          }}
          pickerButtonOnPress={(item) => {
            // setCountryCode(item.dial_code);
            setDefaultCountryCode(item.dial_code);
            setShow(false);
          }}
          style={{
            textInput: {
              fontFamily: fonts.dmSansRegular,
              paddingLeft: 16,
            },
            modal: {
              height: Platform.OS === "ios"? 80 * height/100: (50 * height) / 100,

            },
            dialCode: {
              fontFamily: fonts.dmSansRegular,
            },
            countryName: {
              fontFamily: fonts.dmSansRegular,
            },
          }}
        />
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "90%",
    height: "100%",
    backgroundColor: colors.white,
    alignItems: "center",
  },
  statusBar: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 10,
    marginBottom: 20,
  },
  timeText: {
    fontFamily: fonts.dmSansBold,
    fontSize: 14,
  },
  statusIcons: {
    flexDirection: "row",
  },
  statusIcon: {
    marginLeft: 5,
    fontSize: 14,
  },
  logoContainer: {
    width: "100%",
    alignItems: "center",
    marginTop: (3.5 * height) / 100,
    marginBottom: (1 * height) / 100,
  },
  logoText: {
    fontFamily: fonts.dmSansBold,
    fontSize: 16,
  },
  title: {
    fontFamily: fonts.dmSansBold,
    fontSize: 16,
    marginBottom: (5 * height) / 100,
  },
  socialButtonsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    marginBottom: (3 * height) / 100,
  },
  socialButton: {
    width: "48%",
    height: 56,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: colors.stroke,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.white,
  },
  socialIcon: {
    width: 24,
    height: 24,
  },
  dividerContainer: {
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    marginBottom: (3 * height) / 100,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: "#E0E0E0",
  },
  dividerText: {
    marginHorizontal: 10,
    color: colors.gray1,
    fontSize: 14,
    fontFamily: fonts.dmSansRegular,
  },
  tabContainer: {
    flexDirection: "row",
    width: "100%",
    borderRadius: 10,
    backgroundColor: colors.grayLx,
    marginBottom: (3 * height) / 100,
    overflow: "hidden",
    padding: 3,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: "center",
    justifyContent: "center",
  },
  activeTab: {
    backgroundColor: colors.white,
    borderRadius: 10,
  },
  tabText: {
    color: colors.black,
    fontFamily: fonts.dmSansRegular,
  },
  activeTabText: {
    color: colors.black,
  },
  formContainer: {
    width: "100%",
    marginBottom: (3 * height) / 100,
  },
  inputLabel: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    marginBottom: 8,
  },
  textInput: {
    width: "100%",
    height: 50,
    backgroundColor: "#F5F5F5",
    borderRadius: 10,
    paddingHorizontal: 15,
    marginBottom: 15,
    fontFamily: fonts.dmSansRegular,
  },
  passwordContainer: {
    flexDirection: "row",
    width: "100%",
    height: 59,
    backgroundColor: "#F5F5F5",
    borderRadius: 10,
    alignItems: "center",
  },
  passwordInput: {
    flex: 1,
    height: "100%",
    paddingHorizontal: 15,
    fontFamily: fonts.dmSansRegular,
  },
  eyeIcon: {
    paddingHorizontal: 15,
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  eyeIconText: {
    fontSize: 20,
  },
  signUpButton: {
    width: "100%",
    marginBottom: (2 * height) / 100,
  },
  loginContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: (2 * height) / 100,
  },
  loginText: {
    fontFamily: fonts.dmSansRegular,
    color: colors.black,
    fontSize: 14,
  },
  loginLink: {
    fontFamily: fonts.dmSansBold,
    color: colors.primary,
  },
  termsContainer: {
    width: "90%",
    position: "absolute",
    bottom: (1.5 * height) / 100,
  },
  termsText: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    textAlign: "center",
    color: colors.black,
  },
  termsLink: {
    fontFamily: fonts.dmSansBold,
    color: colors.black,
  },
  bottomIndicator: {
    width: 50,
    height: 5,
    backgroundColor: "#000",
    borderRadius: 2.5,
    position: "absolute",
    bottom: 10,
  },
});
