import React, { useState, useEffect } from "react";
import {
  Dimensions,
  StyleSheet,
  View,
  ScrollView,
  Platform,
  ActivityIndicator,
  Alert,
} from "react-native";
import { colors } from "../../../Config/colors";
import Div from "../../../Components/Div";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import AppHeader from "../../../Components/AppHeader";
import Input from "../../../Components/Input";
import SelectInput from "../../../Components/SelectInput";
import * as yup from "yup";
import { Formik } from "formik";
import { useToast } from "../../../Context/ToastContext";
import { GetHealthcareEducation } from "../../../RequestHandler.tsx/Auth";
import { UpdateProssionalDetails } from "../../../RequestHandler.tsx/User";
import NetworkErrorView from "../../../Components/NetworkErrorView";
import { getErrorType, getErrorMessage } from "../../../utils/networkErrorHandler";

const { height } = Dimensions.get("window");

// Default education options (will be replaced with API data)
const defaultEducationOptions = [
  { label: "Loading education options...", value: "" },
];

export default function ProfileDetailsScreen({ navigation, route }) {
  const [loading, setLoading] = useState(false);
  const [fetchingEducation, setFetchingEducation] = useState(true);
  const { handleToast } = useToast();
  const [educationOptions, setEducationOptions] = useState<Array<{label: string, value: string}>>(
    defaultEducationOptions
  );
  const [networkError, setNetworkError] = useState<any>(null);
  const [showNetworkError, setShowNetworkError] = useState(false);

  // Get doctor profile data from previous screens
  const { doctorProfile } = route.params || {};

  // Fetch healthcare education options from API
  useEffect(() => {
    fetchEducationOptions();
  }, []);

  // Function to fetch healthcare education options
  const fetchEducationOptions = async () => {
    try {
      setFetchingEducation(true);
      const response = await GetHealthcareEducation();

      if (response && Array.isArray(response)) {
        // Transform API response to the format needed for SelectInput
        const options = response.map(item => ({
          label: item.name,
          value: item.id
        }));

        setEducationOptions(options);
      } else {
        handleToast("Failed to load education options", "error");
      }
    } catch (error) {
      console.error("Error fetching education options:", error);

      // Check if it's a network error
      if (getErrorType(error) === 'network' || getErrorType(error) === 'timeout') {
        setNetworkError(error);
        setShowNetworkError(true);
      } else {
        handleToast("Failed to load education options", "error");
      }
    } finally {
      setFetchingEducation(false);
    }
  };

  // Function to retry after network error
  const handleRetry = () => {
    setShowNetworkError(false);
    setNetworkError(null);
    fetchEducationOptions();
  };

  const profileDetailsSchema = yup.object().shape({
    education: yup
      .string()
      .required("Education is required"),
    affiliations: yup
      .string()
      .required("Professional affiliations are required"),
    interests: yup
      .string()
      .required("Area of interest is required"),
    about: yup
      .string()
      .required("About section is required")
      .min(10, "Please provide at least 10 characters"),
  });

  const handleContinue = async (values: {
    education: string;
    affiliations: string;
    interests: string;
    about: string;
  }) => {
    // Formik validation will handle the required fields

    try {
      setLoading(true);

      // Prepare the request body for the API according to the required format
      const professionalDetails = {
        professionalAffliliations: values.affiliations,
        about: values.about,
        areaOfInterest: values.interests,
        education: [values.education], // API expects an array
      };
      console.log("Sending professional details:", professionalDetails);

      // Call the API to update professional details
      const response = await UpdateProssionalDetails(professionalDetails);
      console.log("Professional details update response:", response);
      // Show success message
      handleToast("Profile details saved successfully", "success");

      // Navigate to language details screen
      navigation.navigate("LanguageDetailsScreen");
    } catch (error) {
      console.error("Error updating professional details:", error);

      // Check if it's a network error
      if (getErrorType(error) === "network" || getErrorType(error) === "timeout") {
        setNetworkError(error);
        setShowNetworkError(true);
      } else {
        // Show error message
        handleToast(
          getErrorMessage(error) || "Failed to update professional details",
          "error"
        );
      }
    } finally {
      setLoading(false);
    }
  };

  // If there's a network error, show the NetworkErrorView
  if (showNetworkError) {
    return (
      <NetworkErrorView
        errorType={getErrorType(networkError) as "network" | "timeout" | "server" | "generic"}
        message={getErrorMessage(networkError)}
        onRetry={handleRetry}
      />
    );
  }

  return (
    <View style={styles.mainContainer}>
      <Div>
        <Formik
          initialValues={{
            education: "",
            affiliations: "",
            interests: "",
            about: "",
          }}
          validationSchema={profileDetailsSchema}
          onSubmit={(values) => {
            handleContinue(values);
          }}
        >
          {(formikProps) => (
            <View style={styles.container}>
              <ScrollView
                style={{ width: "100%" }}
                automaticallyAdjustKeyboardInsets={true}
                contentContainerStyle={{
                  alignItems: "center",
                  paddingBottom: 100,
                }}
                showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}
              >
                {/* Header */}
                <View style={{ width: "100%" }}>
                  <AppHeader navigation={navigation} />
                </View>

                {/* Title */}
                <H4 style={styles.title}>Enter Profile details</H4>

                {/* Form Fields */}
                <View style={styles.formContainer}>
                  {/* Education and Trainings */}
                  <View style={{ position: 'relative' }}>
                    <SelectInput
                      label="Education and Trainings"
                      placeholder={fetchingEducation ? "Loading education options..." : "Select"}
                      options={educationOptions}
                      selectedValues={formikProps.values.education ? [formikProps.values.education] : []}
                      onSelect={(values) => {
                        if (values.length > 0) {
                          formikProps.setFieldValue('education', values[0]);
                        }
                      }}
                      multiSelect={false}
                      error={formikProps.errors.education && formikProps.touched.education}
                      errorText={formikProps.errors.education}
                    />
                    {fetchingEducation && (
                      <ActivityIndicator
                        size="small"
                        color={colors.primary}
                        style={styles.loadingIndicator}
                      />
                    )}
                  </View>

                  {/* Professional Affiliations */}
                  <Input
                    contStyle={{ marginTop: 16 }}
                    label="Professional Affiliations"
                     placeholder="--------"
                    value={formikProps.values.affiliations}
                    onChangeText={formikProps.handleChange("affiliations")}
                    onBlur={formikProps.handleBlur("affiliations")}
                    error={
                      formikProps.errors.affiliations &&
                      formikProps.touched.affiliations
                    }
                    errorText={formikProps.errors.affiliations}
                  />

                  {/* Area of Interest */}
                  <Input
                    contStyle={{ marginTop: 16 }}
                    label="Area of Interest"
                      placeholder="--------"
                    value={formikProps.values.interests}
                    onChangeText={formikProps.handleChange("interests")}
                    onBlur={formikProps.handleBlur("interests")}
                    error={
                      formikProps.errors.interests &&
                      formikProps.touched.interests
                    }
                    errorText={formikProps.errors.interests}
                  />

                  {/* About */}
                  <Input
                    contStyle={{ marginTop: 16 }}
                    label="About"
                    placeholder="Tell us about yourself"
                    value={formikProps.values.about}
                    onChangeText={formikProps.handleChange("about")}
                    onBlur={formikProps.handleBlur("about")}
                    error={
                      formikProps.errors.about &&
                      formikProps.touched.about
                    }
                    errorText={formikProps.errors.about}
                    multiline={true}
                    numberOfLines={4}
                    textAlignVertical="top"
                    inputStyle={styles.multilineInput}
                  />
                </View>

                {/* Continue Button */}
                <View style={styles.buttonContainer}>
                  <Button
                    btnText="Continue"
                    onPress={formikProps.handleSubmit}
                    loading={loading}
                    style={styles.continueButton}
                  />
                </View>
              </ScrollView>
            </View>
          )}
        </Formik>
      </Div>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  container: {
    flex: 1,
    width: "90%",
    height: "100%",
    backgroundColor: colors.white,
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontFamily: fonts.dmSansBold,
    marginTop: 16,
    marginBottom: 24,
    alignSelf: "flex-start",
  },
  formContainer: {
    width: "100%",
    marginTop: (2 * height) / 100,
  },
  multilineInput: {
    height: 150,
    paddingTop: 12,
    textAlignVertical: "top",
  },
  buttonContainer: {
    width: "100%",
    marginTop: (5 * height) / 100,
  },
  continueButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
  },
  loadingIndicator: {
    position: 'absolute',
    right: 16,
    top: 40,
  },
});
