import React, { useEffect, useState, useRef } from "react";
import { StyleSheet } from "react-native";
import { View, Animated } from "react-native";
import { colors } from "../Config/colors";

interface PProps {
  loading?: boolean;
}

function DotLoader({ loading }: PProps) {
  const [activeDot, setActiveDot] = useState(0);
  const dotsArray = [1, 2, 3, 4,];

  // Create animated values for each dot
  const animations = useRef(dotsArray.map(() => new Animated.Value(1))).current;
  useEffect(() => {
    if (loading) {
      const interval = setInterval(() => {
        setActiveDot((prevDot) => (prevDot + 1) % dotsArray.length);

        // Animate the active dot
        Animated.sequence([
          Animated.timing(animations[activeDot], {
            toValue: 1.5, // Increase size by 1.5x
            duration: 200,
            useNativeDriver: false,
          }),
          Animated.timing(animations[activeDot], {
            toValue: 1, // Return to original size
            duration: 200,
            useNativeDriver: false,
          }),
        ]).start();
      }, 300);

      return () => clearInterval(interval);
    }
  }, [loading, activeDot]);

  return (
    <View style={styles.loaderContainer}>
      {dotsArray.map((dot, index) => (
        <Animated.View
          key={dot}
          style={[
            styles.dot,
            {
              transform: [{ scale: animations[index] }],
              backgroundColor: "#699AD5",
            },
          ]}
        />
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  loaderContainer: {
    flexDirection: "row",
    justifyContent: "center",
    width: "100%",
    alignItems: "center",
    backgroundColor: colors.white,
    height: 50,
  },
  dot: {
    width: 16,
    height: 16,
    borderRadius: 50,
    marginHorizontal: 4,
  },
  retryContainer: {
    alignItems: "center",
  },
});

export default DotLoader;
