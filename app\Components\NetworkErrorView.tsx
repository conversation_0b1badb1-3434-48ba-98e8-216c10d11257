import React from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
} from "react-native";
import { colors } from "../Config/colors";
import { fonts } from "../Config/Fonts";
import Button from "./Button";
import AntDesign from "react-native-vector-icons/AntDesign";
import MaterialIcons from "react-native-vector-icons/MaterialIcons";
import Feather from "react-native-vector-icons/Feather";

interface NetworkErrorViewProps {
  onRetry?: () => void;
  message?: string;
  title?: string;
  containerStyle?: ViewStyle;
  titleStyle?: TextStyle;
  messageStyle?: TextStyle;
  showRetryButton?: boolean;
  retryButtonText?: string;
  errorType?: "network" | "timeout" | "server" | "generic";
}

/**
 * A component to display when there's a network error or request timeout
 */
const NetworkErrorView: React.FC<NetworkErrorViewProps> = ({
  onRetry,
  message,
  title,
  containerStyle,
  titleStyle,
  messageStyle,
  showRetryButton = true,
  retryButtonText = "Retry",
  errorType = "network",
}) => {
  // Safely determine error type
  let safeErrorType = errorType;
  try {
    // If errorType is not provided or invalid, default to 'generic'
    if (!["network", "timeout", "server", "generic"].includes(errorType)) {
      safeErrorType = "generic";
    }
  } catch (e) {
    console.log("Error in NetworkErrorView:", e);
    safeErrorType = "generic";
  }

  // Default messages based on error type
  const getDefaultTitle = () => {
    try {
      switch (safeErrorType) {
        case "network":
          return "No Internet Connection";
        case "timeout":
          return "Request Timed Out";
        case "server":
          return "Server Error";
        case "generic":
        default:
          return "Something Went Wrong";
      }
    } catch (e) {
      console.log("Error getting default title:", e);
      return "Something Went Wrong";
    }
  };

  const getDefaultMessage = () => {
    try {
      switch (safeErrorType) {
        case "network":
          return "Please check your internet connection and try again.";
        case "timeout":
          return "The server is taking too long to respond. Please try again later.";
        case "server":
          return "We're experiencing issues with our server. Please try again later.";
        case "generic":
        default:
          return "An unexpected error occurred. Please try again.";
      }
    } catch (e) {
      console.log("Error getting default message:", e);
      return "An unexpected error occurred. Please try again.";
    }
  };

  // Use provided title/message or default based on error type
  let displayTitle = "";
  let displayMessage = "";

  try {
    displayTitle = title || getDefaultTitle();
    displayMessage = message || getDefaultMessage();

    // Ensure title and message are strings
    if (typeof displayTitle !== "string") {
      displayTitle = String(displayTitle);
    }
    if (typeof displayMessage !== "string") {
      displayMessage = String(displayMessage);
    }
  } catch (e) {
    console.log("Error setting display text:", e);
    displayTitle = "Something Went Wrong";
    displayMessage = "An unexpected error occurred. Please try again.";
  }

  // Get appropriate icon based on error type
  const renderErrorIcon = () => {
    try {
      switch (safeErrorType) {
        case "network":
          return (
            <AntDesign name="disconnect" size={80} color={colors.primary} />
          );
        case "timeout":
          return (
            <MaterialIcons name="timer" size={80} color={colors.primary} />
          );
        case "server":
          return (
            <MaterialIcons name="error" size={80} color={colors.primary} />
          );
        case "generic":
        default:
          return (
            <Feather name="alert-circle" size={80} color={colors.primary} />
          );
      }
    } catch (e) {
      console.log("Error rendering icon:", e);
      return <Feather name="alert-circle" size={80} color={colors.primary} />;
    }
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <View style={styles.iconContainer}>{renderErrorIcon()}</View>
      <Text style={[styles.title, titleStyle]}>{displayTitle}</Text>
      <Text style={[styles.message, messageStyle]}>{displayMessage}</Text>

      {showRetryButton && onRetry && (
        <View>
          <Button btnText={retryButtonText} onPress={onRetry} />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
    backgroundColor: colors.white,
  },
  iconContainer: {
    width: 150,
    height: 150,
    marginBottom: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  title: {
    fontSize: 20,
    fontFamily: fonts.dmSansBold,
    color: colors.black,
    marginBottom: 10,
    textAlign: "center",
  },
  message: {
    fontSize: 14,
    fontFamily: fonts.dmSansRegular,
    color: colors.gray,
    textAlign: "center",
    marginBottom: 30,
  },
  retryButton: {
    width: 200,
  },
});

export default NetworkErrorView;
