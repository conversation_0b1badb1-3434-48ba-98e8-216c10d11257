/**
 * ErrorUtils.ts
 * Utility functions for handling errors in the application
 */

/**
 * Determines the type of error
 * @param error - The error object
 * @returns The error type as a string ('network', 'timeout', 'server', 'auth', or 'unknown')
 */
export const getErrorType = (error: any): string => {
  if (!error) return 'unknown';
  
  // Check for network errors
  if (
    error.message?.includes('Network request failed') ||
    error.message?.includes('network') ||
    error.message?.includes('Network') ||
    error.message?.includes('internet') ||
    error.message?.includes('connection')
  ) {
    return 'network';
  }
  
  // Check for timeout errors
  if (
    error.message?.includes('timeout') ||
    error.message?.includes('Timeout') ||
    error.message?.includes('timed out')
  ) {
    return 'timeout';
  }
  
  // Check for server errors (5xx)
  if (error.status >= 500 || error.message?.includes('server')) {
    return 'server';
  }
  
  // Check for authentication errors
  if (
    error.status === 401 ||
    error.status === 403 ||
    error.message?.includes('unauthorized') ||
    error.message?.includes('Unauthorized') ||
    error.message?.includes('forbidden') ||
    error.message?.includes('Forbidden') ||
    error.message?.includes('token')
  ) {
    return 'auth';
  }
  
  return 'unknown';
};

/**
 * Gets a user-friendly error message based on the error
 * @param error - The error object
 * @returns A user-friendly error message
 */
export const getErrorMessage = (error: any): string => {
  const errorType = getErrorType(error);
  
  switch (errorType) {
    case 'network':
      return 'Network error. Please check your internet connection and try again.';
    case 'timeout':
      return 'Request timed out. Please try again later.';
    case 'server':
      return 'Server error. Please try again later.';
    case 'auth':
      return 'Authentication error. Please log in again.';
    default:
      return error.message || 'An unexpected error occurred. Please try again.';
  }
};
