import React, { useState, useEffect } from "react";
import {
  Dimensions,
  StyleSheet,
  TouchableOpacity,
  View,
  Text,
  ScrollView,
  Image,
  Platform,
  ActivityIndicator,
  Alert,
} from "react-native";
import * as DocumentPicker from "expo-document-picker";
import { colors } from "../../../Config/colors";
import H4 from "../../../Components/H4";
import P from "../../../Components/P";
import { fonts } from "../../../Config/Fonts";
import Button from "../../../Components/Button";
import { SvgXml } from "react-native-svg";
import { svg } from "../../../Config/Svg";
import Input from "../../../Components/Input";
import SelectInput from "../../../Components/SelectInput";
import * as yup from "yup";
import { Formik } from "formik";
import { useToast } from "../../../Context/ToastContext";
import NetworkErrorView from "../../../Components/NetworkErrorView";
import {
  getErrorMessage,
  getErrorType,
} from "../../../utils/networkErrorHandler";
import { UpdateUserIdentity, GetPractIdentity } from "../../../RequestHandler.tsx/User";
import { uploadDocumentToCloudinary } from "../../../Services/CloudinaryService";

const { height } = Dimensions.get("window");

// ID type options for dropdown
const idTypeOptions = [
  { label: "National Identification Number", value: "NIN" },
  { label: "Passport", value: "passport" },
  { label: "Voter's Card", value: "voters-card" },
];

// KYC Status badge component
const KYCStatusBadge = ({ status }: { status: string }) => {
  const getStatusColor = () => {
    switch (status?.toLowerCase()) {
      case 'verified':
        return { bg: '#E8F5E8', text: '#2E7D32', border: '#4CAF50' };
      case 'pending':
        return { bg: '#FFF3E0', text: '#F57C00', border: '#FF9800' };
      case 'rejected':
        return { bg: '#FFEBEE', text: '#D32F2F', border: '#F44336' };
      default:
        return { bg: '#F5F5F5', text: '#757575', border: '#BDBDBD' };
    }
  };

  const statusColors = getStatusColor();

  return (
    <View style={[styles.statusBadge, { backgroundColor: statusColors.bg, borderColor: statusColors.border }]}>
      <P style={[styles.statusText, { color: statusColors.text }]}>
        KYC Status: {status?.charAt(0).toUpperCase() + status?.slice(1) || 'Unknown'}
      </P>
    </View>
  );
};

export default function IdentificationComponent() {
  const [loading, setLoading] = useState(false);
  const [fetchingIdentity, setFetchingIdentity] = useState(true);
  const { handleToast } = useToast();
  const [documentUploaded, setDocumentUploaded] = useState(false);
  const [documentInfo, setDocumentInfo] = useState<{
    name: string;
    uri: string;
    type: string;
    size: number;
  } | null>(null);
  const [initialValues, setInitialValues] = useState({
    idType: "",
    idNumber: "",
    documentUploaded: false,
  });
  const [kycStatus, setKycStatus] = useState<string>("");
  const [isEditable, setIsEditable] = useState(true);
  const [networkError, setNetworkError] = useState<any>(null);
  const [showNetworkError, setShowNetworkError] = useState(false);

  // Fetch identity details from API
  useEffect(() => {
    fetchIdentityDetails();
  }, []);

  // Function to fetch identity details
  const fetchIdentityDetails = async () => {
    try {
      setFetchingIdentity(true);
      const response = await GetPractIdentity();

      if (response && response.data) {
        // Pre-populate form with existing data from the nested structure
        const existingData = {
          idType: response.data.id_type || "",
          idNumber: response.data.documentId || "",
          documentUploaded: !!response.data.documentUrl,
        };

        setInitialValues(existingData);
        setKycStatus(response.data.status || "");

        // Determine if form should be editable based on KYC status
        const status = response.data.status?.toLowerCase();
        setIsEditable(status !== 'pending' && status !== 'verified');

        // If there's an existing document, set the document info
        if (response.data.documentUrl) {
          setDocumentUploaded(true);
          setDocumentInfo({
            name: "Existing Document",
            uri: response.data.documentUrl,
            type: "application/pdf",
            size: 0,
          });
        }
      }
    } catch (error) {
      console.error("Error fetching identity details:", error);

      // Check if it's a network error
      if (getErrorType(error) === 'network' || getErrorType(error) === 'timeout') {
        setNetworkError(error);
        setShowNetworkError(true);
      } else {
        // Don't show error for this, just use default values
        setIsEditable(true);
      }
    } finally {
      setFetchingIdentity(false);
    }
  };

  // Function to retry after network error
  const handleRetry = () => {
    setShowNetworkError(false);
    setNetworkError(null);
    fetchIdentityDetails();
  };

  const identificationSchema = yup.object().shape({
    idType: yup.string().required("ID type is required"),
    idNumber: yup.string().required("ID number is required"),
    documentUploaded: yup
      .boolean()
      .oneOf([true], "Please upload your documentation")
      .required("Please upload your documentation"),
  });

  const handleUploadDocument = async (setFieldValue: (field: string, value: any) => void) => {
    if (!isEditable) {
      handleToast("Cannot edit document while KYC is pending or verified", "error");
      return;
    }

    try {
      // Open document picker for selecting files
      const result = await DocumentPicker.getDocumentAsync({
        type: [
          "application/pdf",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "image/jpeg",
          "image/png",
        ],
        copyToCacheDirectory: true,
        multiple: false,
      });

      // Check if the user selected a document
      if (
        result.canceled === false &&
        result.assets &&
        result.assets.length > 0
      ) {
        const selectedFile = result.assets[0];

        // Check file size (limit to 100MB)
        const fileSize = selectedFile.size || 0;
        const maxSize = 100 * 1024 * 1024; // 100MB in bytes

        if (fileSize > maxSize) {
          handleToast("File size exceeds 100MB limit", "error");
          return;
        }

        // Store document info and update state
        setDocumentInfo({
          name: selectedFile.name,
          uri: selectedFile.uri,
          type: selectedFile.mimeType,
          size: fileSize,
        });

        // Update Formik state
        setFieldValue("documentUploaded", true);
        setDocumentUploaded(true);
        handleToast("Document uploaded successfully", "success");
      }
    } catch (error) {
      console.error("Error picking document:", error);
      handleToast("Failed to upload document. Please try again.", "error");
    }
  };

  const handleSubmit = async (values: {
    idType: string;
    idNumber: string;
    documentUploaded: boolean;
  }) => {
    if (!isEditable) {
      handleToast("Cannot update details while KYC is pending or verified", "error");
      return;
    }

    try {
      setLoading(true);

      let documentUrl = "";

      // Only upload if a new document was selected
      if (documentInfo && !documentInfo.uri.startsWith("https://")) {
        try {
          // Show uploading message
          handleToast("Uploading document...", "success");

          // Upload document to Cloudinary
          documentUrl = await uploadDocumentToCloudinary(
            documentInfo.uri,
            documentInfo.type
          );

          if (!documentUrl) {
            throw new Error("Failed to upload document");
          }
        } catch (uploadError) {
          console.error("Error uploading document:", uploadError);
          handleToast("Failed to upload document. Please try again.", "error");
          setLoading(false);
          return;
        }
      } else if (documentInfo) {
        // Use existing document URL
        documentUrl = documentInfo.uri;
      }

      // Prepare the request body for the API
      const identityData = {
        id_type: values.idType,
        documentUrl: documentUrl,
        documentId: values.idNumber,
      };

      console.log("Sending identity data:", identityData);

      // Call the API to update user identity
      const response = await UpdateUserIdentity(identityData);
      console.log("Identity update response:", response);

      // Show success message
      handleToast("Identification details updated successfully", "success");

      // Refresh the data to get updated status
      await fetchIdentityDetails();
    } catch (error) {
      console.error("Error updating identity:", error);

      // Check if it's a network error
      if (
        getErrorType(error) === "network" ||
        getErrorType(error) === "timeout"
      ) {
        setNetworkError(error);
        setShowNetworkError(true);
      } else {
        // Show error message
        handleToast(
          getErrorMessage(error) || "Failed to update identity",
          "error"
        );
      }
    } finally {
      setLoading(false);
    }
  };

  // If there's a network error, show the NetworkErrorView
  if (showNetworkError) {
    return (
      <NetworkErrorView
        errorType={
          getErrorType(networkError) as
            | "network"
            | "timeout"
            | "server"
            | "generic"
        }
        message={getErrorMessage(networkError)}
        onRetry={handleRetry}
      />
    );
  }

  // Show loading while fetching initial data
  if (fetchingIdentity) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <P style={styles.loadingText}>Loading identification details...</P>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        validationSchema={identificationSchema}
        onSubmit={(values) => {
          handleSubmit(values);
        }}
      >
        {(formikProps) => (
          <ScrollView
            style={{ width: "100%" }}
            automaticallyAdjustKeyboardInsets={true}
            contentContainerStyle={{
              paddingBottom: 50,
            }}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
          >
            {/* Title and KYC Status */}
            <View style={styles.headerSection}>
              <H4 style={styles.title}>Identity Verification</H4>
              <KYCStatusBadge status={kycStatus} />
              {!isEditable && (
                <View style={styles.infoContainer}>
                  <P style={styles.infoText}>
                    Your KYC is {kycStatus}. You cannot edit these details at this time.
                  </P>
                </View>
              )}
            </View>

            {/* Form Fields */}
            <View style={styles.formContainer}>
              {/* ID Type */}
              <SelectInput
                label="ID type"
                placeholder="Select"
                options={idTypeOptions}
                selectedValues={
                  formikProps.values.idType
                    ? [formikProps.values.idType]
                    : []
                }
                onSelect={(values) => {
                  if (values.length > 0 && isEditable) {
                    formikProps.setFieldValue("idType", values[0]);
                  }
                }}
                multiSelect={false}
                disabled={!isEditable}
                error={
                  formikProps.errors.idType && formikProps.touched.idType
                }
                errorText={formikProps.errors.idType}
              />

              {/* ID Number */}
              <Input
                contStyle={{ marginTop: 16 }}
                label="ID number"
                placeholder="--------"
                value={formikProps.values.idNumber}
                onChangeText={isEditable ? formikProps.handleChange("idNumber") : undefined}
                onBlur={formikProps.handleBlur("idNumber")}
                editable={isEditable}
                error={
                  formikProps.errors.idNumber &&
                  formikProps.touched.idNumber
                }
                errorText={formikProps.errors.idNumber}
              />

              {/* Upload Documentation */}
              <View style={styles.uploadSection}>
                <P style={styles.uploadLabel}>Upload Documentation</P>
                <TouchableOpacity
                  style={[
                    styles.uploadContainer,
                    documentUploaded && styles.uploadedContainer,
                    !isEditable && styles.disabledContainer,
                    formikProps.errors.documentUploaded &&
                      formikProps.touched.documentUploaded &&
                      styles.uploadError,
                  ]}
                  onPress={() =>
                    isEditable && handleUploadDocument(formikProps.setFieldValue)
                  }
                  onBlur={() =>
                    formikProps.setFieldTouched("documentUploaded")
                  }
                  disabled={!isEditable}
                >
                  <SvgXml
                    xml={
                      svg.upload ||
                      '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="#F0A500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M17 8L12 3L7 8" stroke="#F0A500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 3V15" stroke="#F0A500" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'
                    }
                    width={24}
                    height={24}
                  />
                  <P style={[styles.uploadText, !isEditable && styles.disabledText]}>
                    {documentUploaded
                      ? "Document Uploaded Successfully"
                      : isEditable
                        ? "Drag & Drop your File here or Browse Files"
                        : "Document Upload Disabled"}
                  </P>
                  {documentUploaded && documentInfo && (
                    <P
                      style={[
                        styles.uploadSubtext,
                        { color: colors.primary },
                      ]}
                    >
                      {documentInfo.name}
                    </P>
                  )}
                  <P style={styles.uploadSubtext}>
                    Works with any DOC, DOCX, PDF below 100MB
                  </P>
                  {formikProps.errors.documentUploaded &&
                    formikProps.touched.documentUploaded && (
                      <P style={styles.errorText}>
                        {formikProps.errors.documentUploaded}
                      </P>
                    )}
                </TouchableOpacity>
              </View>
            </View>

            {/* Submit Button - Only show if editable */}
            {isEditable && (
              <View style={styles.buttonContainer}>
                <Button
                  btnText="Update Identity"
                  onPress={formikProps.handleSubmit}
                  loading={loading}
                  style={styles.submitButton}
                />
              </View>
            )}
          </ScrollView>
        )}
      </Formik>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: "100%",
    backgroundColor: colors.white,
  },
  headerSection: {
    marginBottom: 20,
  },
  title: {
    fontSize: 20,
    fontFamily: fonts.dmSansBold,
    marginBottom: 12,
    color: colors.black,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    alignSelf: 'flex-start',
    marginBottom: 12,
  },
  statusText: {
    fontSize: 14,
    fontFamily: fonts.dmSansMedium,
  },
  infoContainer: {
    backgroundColor: colors.primaryLight_100,
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
  },
  infoText: {
    fontSize: 14,
    fontFamily: fonts.dmSansRegular,
    color: colors.black,
  },
  formContainer: {
    width: "100%",
    marginTop: 10,
  },
  uploadSection: {
    width: "100%",
    marginTop: 32,
  },
  uploadLabel: {
    fontFamily: fonts.dmSansMedium,
    fontSize: 14,
    color: colors.black,
    marginBottom: 8,
  },
  uploadContainer: {
    width: "100%",
    height: 120,
    borderWidth: 1,
    borderColor: colors.stroke,
    borderStyle: "dashed",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.white,
    paddingHorizontal: 16,
  },
  uploadedContainer: {
    borderColor: colors.primary,
    backgroundColor: colors.primaryLight_100,
  },
  disabledContainer: {
    backgroundColor: '#F5F5F5',
    borderColor: '#E0E0E0',
    opacity: 0.6,
  },
  uploadError: {
    borderColor: colors.error || "red",
  },
  uploadText: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    color: colors.black,
    textAlign: "center",
    marginTop: 8,
  },
  disabledText: {
    color: '#9E9E9E',
  },
  uploadSubtext: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    color: colors.navGray,
    textAlign: "center",
    marginTop: 4,
  },
  buttonContainer: {
    width: "100%",
    marginTop: 30,
  },
  submitButton: {
    width: "100%",
    height: 56,
    borderRadius: 28,
  },
  errorText: {
    fontFamily: fonts.dmSansRegular,
    fontSize: 14,
    color: colors.error || "red",
    marginTop: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 14,
    fontFamily: fonts.dmSansRegular,
    color: colors.navGray,
  },
});